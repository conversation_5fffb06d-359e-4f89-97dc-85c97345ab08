[project]
name = "hive"
version = "1.0"
description = "Bold Idea's coding club management system"
readme = "README.md"
authors = [
  {name = "<PERSON>", email = "<EMAIL>"},
  {name = "<PERSON><PERSON>", email = "ram<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"},
  {name = "<PERSON>", email = "s<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"},
]
requires-python = "~=3.9.16"
dependencies = [
  "braintree~=4.17.1",
  "ciso8601~=2.1.1",
  "cryptography~=3.4",
  "dictdiffer~=0.8.1",
  "django-bitfield~=2.1.0",
  "django-compressor~=2.4",
  "django-cors-headers~=3.9.0",
  "django-cryptography~=0.3",
  "django-extensions~=3.1.3",
  "django-import-export~=2.0.2",
  "django-ipware~=1.1.6",
  "django-json-widget~=1.0.1",
  "django-libsass~=0.7",
  "django-localflavor~=2.0",
  "django-markupfield~=1.4.2",
  "django-material~=1.6.3",
  "django-phonenumber-field~=2.1.0",
  "django-taggit-serializer~=0.1.7",
  "django-taggit~=1.5.1",
  "django-vinaigrette~=2.0.1",
  "django~=2.2.12",
  "djangorestframework~=3.11.0",
  "drf-nested-routers~=0.90.2",
  "easy-thumbnails~=2.7",
  "furl~=2.1.3",
  "google-api-python-client~=1.6.3",
  "gunicorn~=20.1.0",
  "hashids~=1.2.0",
  "ics==0.7",
  "intuit-oauth~=1.2.3",
  "jsonmerge~=1.8.0",
  "libsass~=0.22.0",
  "lti~=0.9.5",
  "mailchimp3~=3.0.6",
  "markdown3-newtab~=0.2.0",
  "Markdown~=3.3.4",
  "martor~=1.6.13",
  "mdx-truly-sane-lists~=1.2",
  "openpyxl~=2.6.0",
  "phonenumbers~=8.10.2",
  "pillow~=4.2.1",
  "postgis~=1.0.4",
  "pygeocoder~=1.2.5",
  "Pygments~=2.15.0",
  "pyopenssl~=19.1.0",
  "python-dateutil~=2.8.2",
  "python-dotenv~=0.8.2",
  "python-gitlab~=2.10.1",
  "python-quickbooks~=0.8.4",
  "pytz>=2023.3",
  "pyxero~=0.9.0",
  "ratelimit~=2.2.1",
  "requests~=2.27.1",
  "sentry-sdk~=1.5.8",
  "social-auth-app-django~=1.2.0",
  "social-auth-core~=3.3.3",
  "unidecode~=1.0.22",
  "django-slack-utils>=0.3.1",
  "pycryptodome>=3.17",
  "slackclient~=2.7.2",
  "jq>=1.4.1",
  "psycopg2-binary~=2.8.2",
  "azure-appconfiguration>=1.4.0",
  "azure-identity>=1.13.0",
  "azure-appconfiguration-provider>=1.0.0",
  "django-azure-communication-email>=1.0.1",
  "aiohttp>=3.8.4",
  "apscheduler>=3.10.1",
  "ipython>=8.13.2",
  "ipdb>=0.13.13",
]

[project.optional-dependencies]
dev = [
  "freezegun~=1.1.0",
  "azure-mgmt-web>=7.1.0",
  "azure-mgmt-appconfiguration>=3.0.0",
  "time-machine>=2.10.0",
]

[project.scripts]
manage = "hive:manage"
preload-appconfig = "azure_support:preload_appconfig"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[dependency-groups]
dev = [
    "black>=25.1.0",
    "isort>=6.0.1",
]
