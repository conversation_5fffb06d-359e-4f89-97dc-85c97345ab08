from django.db import models

from .query import StudentSurveyLinkQuerySet


class StudentSurveyLink(models.Model):
    """
    We have different surveys depending on the student's grade level. This was previous defined in
    settings.py, but this allows admins to change the url if necessary.
    """

    grade_level = models.CharField(
        max_length=32,
        choices=(
            ("ELEMENTARY", "Elementary"),
            ("MIDDLE", "Middle school"),
            ("HIGH", "High school"),
        ),
    )
    type = models.CharField(
        max_length=32,
        choices=(("PRE", "pre-survey"), ("MID", "mid-survey"), ("POST", "post-survey")),
    )
    form_url = models.URLField(
        help_text=(
            "Use {student.student_id} in the URL to pass the student id as a parameter. "
            "This can be used to pre-fill the student id form field."
        )
    )

    objects = StudentSurveyLinkQuerySet.as_manager()

    def get_url(self, student):
        return self.form_url.format(student=student)

    def __str__(self):
        return f"{self.get_grade_level_display()} {self.get_type_display()}"
