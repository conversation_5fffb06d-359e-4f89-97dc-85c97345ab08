from django.contrib import admin
from django.utils.safestring import mark_safe

from hive.admin import register

from . import models


@register(models.StudentSurveyLink)
class StudentSurveyLink(admin.ModelAdmin):
    fields = ["grade_level", "type", "form_url"]
    list_display = ["grade_level", "type", "_form_url"]
    list_filter = ["grade_level", "type"]

    def get_queryset(self, request):
        return super().get_queryset(request).with_ordering()

    def _form_url(self, obj):
        return mark_safe(
            f'<a href="{obj.form_url}" target="_blank">open in new tab</a>'
        )

    _form_url.verbose_name = "Form URL"
