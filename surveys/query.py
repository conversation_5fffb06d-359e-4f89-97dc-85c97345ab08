from django.db.models import Case, PositiveIntegerField, QuerySet, Value, When


class StudentSurveyLinkQuerySet(QuerySet):
    def with_ordering(self):
        return self.annotate(
            grade_level_order=Case(
                When(grade_level="ELEMENTARY", then=Value(1)),
                When(grade_level="MIDDLE", then=Value(2)),
                When(grade_level="HIGH", then=Value(3)),
                default=Value(0),
                output_field=PositiveIntegerField(),
            ),
            type_order=Case(
                When(type="PRE", then=Value(1)),
                When(type="MID", then=Value(2)),
                When(type="POST", then=Value(3)),
                default=Value(0),
                output_field=PositiveIntegerField(),
            ),
        ).order_by("grade_level_order", "type_order")
