# Hive
For a more detailed overview of Hive and Bold Idea's technology
architecture, see [here][1].

**Hive** is Bold Idea's coding club management system.

This codebase serves as the application for several domains used for
running our coding clubs:

**hive.boldidea.org** hosts the volunteer portal and admin portal. It
includes all functionality needed by volunteers and staff. The admin
site (used only by staff and certain authorized volunteers) is located
at hive.boldidea.org/admin.

**clubs.boldidea.org** is the registration site where parents sign up
their students.

**my.boldidea.org** is for students. Right now, this site serves as a
home for student assessments, but we may use it for other functionality
in the future, such as collaborating w/ teammates, giving students a
page to track their overall year-to-year progress at Bold Idea, etc.

**studio.boldidea.org** is used to catalog and showcase student
projects, most of which are hosted statically on GitLab Pages.


## Contributing
### Coding Style
Please follow the Django coding standards when contributing to Hive.

https://docs.djangoproject.com/en/dev/internals/contributing/writing-code/coding-style/

The one exception is that we allow line lengths up to 120 characters as
opposed to 88. Docstring should still be limited to 79 when possible.

### Requirements
* git
* docker
* docker compose

### Recommended tools
* [direnv][2]: set project-specicfic environment variables, such as `COMPOSE_FILE`
* [AI coding agent][4]: to help familiarize yourself with the codebase, a coding agent
  is highly recommended. There a dozens out there, so you'll want to research
  what works best for you and your preferred coding environment. Tools such as
  Cursor, Claude Code, Copilot Chat, Codium, Goose, Opencode, & Crush are all
  fine choices. 

### Docker and docker compose
Local development is managed via `docker` and `docker compose`. All
docker-related files are stored in the `docker/` sub-directory.

> *Note:* You may optionally set `COMPOSE_FILE="docker/docker-compose.yml"`
> in your environment. Otherwise, `docker compose` command must be run within
> the `docker/` subdirectory. The command examples in this README assume you are
> working from that directory or have the environment variable set.


### Setting up your development environment
To get started, clone the repository, change to the `docker` directory, and run
the `mkenv` compose command:

    $ <NAME_EMAIL>:boldidea/sites/hive.git
    $ cd hive/docker/
    $ touch .env
    $ docker compose run --rm mkenv > .env
    $ docker compose build --no-cache hive

> *Note:* you may see warnings about unset environment variables when running
> this command for the first time. These may be ignored for now.

The `mkenv` command outputs a default environment config, which we
save in `docker/.env`. Note that this file is ignored by git, and
should **never** be committed to the repository. This command also
generates default secrets for the following:

- `DJANGO_DB_PASSWORD` (also used for `POSTGRES_PASSWORD`)
- `DJANGO_SECRET_KEY`
- `AES_KEY`

> **IMPORTANT**: If you re-run the `mkenv` command after the postgresql
> container/volumme is first created, you will need to manually re-set the
> password for the database user in the postgresql container.

For more info on environment settings, see the 
[Environment Variables](#environment-variables) section.

### Resolving hostnames locally

The default development config sets `BASE_DOMAIN` to `"boldidea.local"`,
which causes the nginx proxy to serve for the following hostnames:

- hive.boldidea.local
- clubs.boldidea.local
- my.boldidea.local
- api.boldidea.local
- sso.boldidea.local
- studio.boldidea.local

You'll need to set up your system to resolve the hostnames to your local
host. 

The easiest way to do this is to edit your system's hosts file
(`/etc/hosts` on Mac or Linux, `c:\Windows\System32\Drivers\etc\hosts`
on Windows):

    127.0.0.1 localhost hive.boldidea.local clubs.boldidea.local my.boldidea.local studio.boldidea.local

If you're using Mac OS or Linux, you can also set up `dnsmasq` to
resolve the wildcard domain `*.boldidea.local`. See 
[Using dnsmasq](#using-dnsmasq) for more info.

Alternatively, you can use `localtest.me`, which always resolves to
localhost (this domain is privately owned, so use at your own risk). To
use this, simply change the `BASE_DOMAIN` setting to `"localtest.me"`.

Note: in this README, we will assume you are using `boldidea.local`.

### Setting up SSL locally
There are certain aspects of the site that require SSL (eg, cross-origin
authentication between sites). We've included a script that creates
self-signed certificates for you:

    $ docker compose run --rm localcerts create boldidea.local
 
This will generate certificates in the `hive_ssl-certs` volume.

Next, you'll need to add these certificates to your browser's trusted
certificates. You can export the `.ca.crt` file by running the following
command:

    $ docker compose run --rm localcerts dump boldidea.local ca-cert > /tmp/boldidea.local.ca.crt

#### Adding the self-signed cert in Windows or Linux
In Chrome, go to
[chrome://settings/certificates](chrome://settings/certificates),
click the "Authorities" tab, click "Import", then select the exported
`.ca.crt` file.

#### Adding the self-signed cert in Mac OS:
If you're using Mac OS, you will need to add the certificate to your system keychain using the "Keychain Access" app:

1. In the Keychain Access app, click "System" in the left menu
2. Drag the generated keychain file from the finder into Keychain Access.
3. Click the "Certificates" tab
4. Double-click on the "boldidea.local" certificarte
5. Under "Trust > When using this certifciate", select "Always Trust"
6. Close the window and confirm the change using your system password.

### Database management
You can use the following command to access the psql commandline on the
database container:

    $ docker compose run --rm psql

The easiest way to get started using the site is to use a copy of the
production database (we don't currently have a mock database available).

To restore a dumped database file, use the following command:

    $ cat dump_file.sql | docker compose run --rm -T pg_restore

### Running Django management commands
Django management commands (such as `migrate`, `makemigrations`,
`shell`, `makemessages`, etc) can be run using the following
docker compose command:

    $ docker compose run --rm manage <command> [args]

For example, to apply any unapplied migrations:

    $ docker compose run --rm manage migrate

You can see all available management commands by passing `--help`:

    $ docker compose run --rm manage --help

### Starting the services
Next, start the containers by running:

    $ docker compose --profile=dev up -d

This starts up the following containers:

- A python service for each site
- Nginx proxy
- Postgresql server

Note: You can also set the `COMPOSE_PROFILES` environment variable to `dev` to
avoid having to pass `--profile=dev`.

To reduce the impact on local resources, you can start up a single
service (such as "clubs") by specifying the service name, eg:

    $ docker compose up -d clubs

### Adding packages
To add packages, use:

    $ docker compose run --rm uv add packagename

You will then need to re-build and re-start your containers

    $ docker compose build hive
    $ docker compose --profile=dev up -d


### Accessing the websites
The nginx proxy listens on port `8000`, You should now be able to load
the sites in your browser:

 - [https://hive.boldidea.local:8000](https://hive.boldidea.local:8000)
 - [https://clubs.boldidea.local:8000](https://clubs.boldidea.local:8000)
 - [https://my.boldidea.local:8000](https://my.boldidea.local:8000)
 - [https://api.boldidea.local:8000](https://api.boldidea.local:8000)
 - [https://sso.boldidea.local:8000](https://sso.boldidea.local:8000)
 - [https://studio.boldidea.local:8000](https://studio.boldidea.local:8000)

### Viewing console logs & using pdb/ipdb
To view the console output or to use `pdb` or `ipdb`, you'll need to
attach to the running container. You can use `docker container ps` to
list the currently running containers, then use the container name to
attach to the console. For example, to attach to the container running
the clubs site, use:

    $ docker attach hive-clubs

### Static and media directories
The project's `STATIC_DIR` and `MEDIA_DIR` are defined as mounts in
docker compose. If you need to access these directly, run the docker
volume inspect command to see where the files are stored on your system:

    $ docker volume inspect hive_hive-static
    $ docker volume inspect hive_hive-media

### Environment variables
Hive relies on environment variables for all settings. It's important
that you *do not* store any of these variables in the repository, as
they contain sensitive information and environment-specific settings.

See the `docker/.env` file to see all settings that are controlled by
environment variables.

Certain features require API keys or other secrets to be set in
environment variables. Contact <EMAIL> if you need any of these
values.

### Using dnsmasq

#### OSX
See [here](https://gist.github.com/ogrrd/5831371) for instructions on
setting up dnsmasq with OSX.

#### Linux & NetworkManager
Install `dnsmasq` pacakge and configure it to start at boot. Then, add
the following line to `/etc/dnsmasq.conf`:
```
address=/.local/127.0.0.1
```
Then add the following config to /etc/NetworkManager/NetworkManager.conf
to make it use dnsmasq:
```
[main]
dns=dnsmasq
```

### Deploying to azure
Any pushes to the production branch will trigger a deployment:

The following command will immediately update production to match main and push
those changes:
```
$ git push origin main:production
```

A build pipeline will be triggered for both `main` and `production`.

You can track the build and deployment on [gitlab][3]. Switch to the
`production` branch, and next to the latest commit hash you should see an icon
indicating build and deploy status. It will be yellow (in progress), green
(success), or red (failure). Clicking the icon will show you the pipeline
details.

Sometimes you might get a deploy failure indicating the secret key for GitLabCI
has expired. For example:

> ERROR: AADSTS7000222: The provided client secret keys for app '[MASKED]' are
  expired. Visit the Azure portal to create new keys for your app:
  https://aka.ms/NewClientSecret, or consider using certificate credentials for
  added security: https://aka.ms/certCreds.

See the following section for instructions to rotate the Gitlab CI key on Azure.

You can re-start a job by clicking "Build" -> "Jobs", and clicking the retry
button next to the failed job.

The GitLab job will tell Azure to pull a new image and restart the web app
services. It may take a few minutes for the app to restart. You can verify the
status by viewing one of the app-hive-* app services in Azure and viewing
the deployment logs.

> **Note**: You may see downtime alerts while the site is restarting.
  This is expected behavior.

### Rotating CI Key

To rotate the the Gitlab CI key:

1. log in to portal.azure.com and search for "App registrations".
2. Click on "All Applications", then "GitLabCI"".
3. Click "Manage" -> "Certificates & secrests".
4. Click the + button to add a new secret.
5. Enter "rbac" in the description, and select the maximum expires date.
6. Copy the key value to your clipboard.
7. Go to the [gitlab repository][3], and click on "Settings" -> "CI/CD".
8. Click "Variables", then find `AZURE_PASSWORD`, and click the edit icon.
9. Paste the key into the "Value" field, and click "Save changes".

---

[1]: https://docs.google.com/document/d/13cypTHF6TkBmsW60j4PFpCi1a-acxQOHt7gflGJnuM8
[2]: https://direnv.net/
[3]: https://gitlab.com/boldidea/sites/hive
[4]: https://medium.com/@justinmilner/the-top-coding-assistant-platforms-of-july-2024-a862e84c1b34
