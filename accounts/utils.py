from django.contrib.admin.utils import NestedObjects
from django.db import transaction


class MergeError(Exception):
    pass


class MergeIntegrityError(MergeError):
    def __init__(self, related_objects):
        self.related_objects = set(related_objects)
        msg = (
            "Record being merged cannot be deleted due to existing related "
            "objects that would also be deleted"
        )
        super().__init__(msg)


@transaction.atomic()
def merge_account(account, to_account):
    delete_objects = []
    parent_delete_objects = []

    contact_name = (None, None)

    if to_account.default_contact:
        # Merge contacts
        contact_name = (
            to_account.default_contact.first_name,
            to_account.default_contact.last_name,
        )

    # Move contacts
    for contact in account.contacts.all():
        if (contact.first_name, contact.last_name) == contact_name:
            # Skip contact if first/last name is the same as the default and delete it
            delete_objects.append(contact)
            parent_delete_objects.append(contact.contact_ptr)
            continue
        contact.account = to_account
        contact.save()

    # Move students
    for student in account.students.all():
        student.account = to_account
        student.save()

    # Move registrations
    for registration in account.registrations.all():
        registration.account = to_account
        registration.save()

    # Some parent accounts might also have a volunteer account associated with the user,
    # If the to-be-deleted account has an associated volunteer, throw an error.
    if hasattr(account.user.contact, "volunteer") and hasattr(
        to_account.user.contact, "volunteer"
    ):
        raise MergeError(
            "Both account users have associated volunteer accounts. "
            "Consider merging those volunteer accounts first: "
            f"{account.user.volunteer.email}, {to_account.user.volunteer.email}"
        )
    elif hasattr(account.user.contact, "volunteer"):
        raise MergeError(
            "The account to be deleted has an associated volunteer account. "
            f"Consider keeping the other account (#{account.pk}) instead."
        )

    # Delete user and account
    delete_objects.append(account.user)
    delete_objects.append(account)

    # Make sure other objects are deleted that we didn't expect
    related_objects = []
    for obj in delete_objects:
        collector = NestedObjects(using="default")
        collector.collect([obj])
        for model, obj in collector.instances_with_model():
            if obj not in delete_objects and obj not in parent_delete_objects:
                related_objects.append((model.__name__, obj))

    if len(related_objects):
        raise MergeIntegrityError(related_objects)

    # Delete remaining objects
    for obj in delete_objects:
        obj.delete()
