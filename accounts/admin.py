from django import http
from django.contrib import admin, messages
from django.db import transaction
from django.shortcuts import render
from django.urls import reverse
from import_export import fields as ie_fields
from import_export import resources
from import_export.admin import ExportActionModelAdmin

from accounts import models, utils
from contacts.admin import ContactAdminMixin
from hive.admin import admin_site
from hive.utils import short_description


@short_description("Merge accounts")
def merge_accounts(modeladmin, request, queryset):
    selected = request.POST.getlist(admin.ACTION_CHECKBOX_NAME)
    url = reverse("admin:accounts_account_merge")
    return http.HttpResponseRedirect("{}?accounts={}".format(url, ",".join(selected)))


class AccountAdmin(admin.ModelAdmin):
    icon = '<i class="material-icons">folder_shared</i>'
    list_display = ["__str__", "user", "default_contact", "date_added"]
    search_fields = [
        "id",
        "user__email__unaccent",
        "contacts__first_name__unaccent",
        "contacts__last_name__unaccent",
        "contacts__email__unaccent",
        "students__first_name__unaccent",
        "students__last_name__unaccent",
    ]
    fields = ["user", "default_contact"]
    autocomplete_fields = ["default_contact"]
    actions = [merge_accounts]

    def get_urls(self):
        from django.conf.urls import url

        return [
            url(
                "^merge/$",
                self.admin_site.admin_view(self.merge_view),
                name="accounts_account_merge",
            )
        ] + super().get_urls()

    def merge_view(self, request, *args, **kwargs):
        from accounts.models import Account

        if request.method == "POST":
            account_ids = request.POST.getlist("accounts")
            accounts = Account.objects.filter(id__in=account_ids)
            keep_id = request.POST.get("keep")
            keep_account = Account.objects.get(pk=keep_id)
            merge_accounts = Account.objects.filter(id__in=account_ids).exclude(
                id=keep_id
            )
            try:
                with transaction.atomic():
                    for account in merge_accounts:
                        utils.merge_account(account, keep_account)
            except utils.MergeError as error:
                context = {
                    "accounts": accounts,
                    "merge_error": error,
                }
                return render(request, "admin/accounts/merge.html", context)
            self.message_user(request, "Successfully merged account.", messages.SUCCESS)
            return http.HttpResponseRedirect(
                reverse("admin:accounts_account_changelist")
            )
        account_ids = (int(id) for id in request.GET.get("accounts").split(","))
        accounts = Account.objects.filter(id__in=account_ids)
        context = {"accounts": accounts}
        return render(request, "admin/accounts/merge.html", context)


class AccountContactResource(resources.ModelResource):
    account = ie_fields.Field()
    address = ie_fields.Field()

    class Meta:
        model = models.AccountContact
        fields = ["account", "first_name", "last_name", "email", "phone", "address"]

    def dehydrate_account(self, obj):
        return "#" + obj.account.account_number

    def dehydrate_address(self, obj):
        return obj.account.default_contact.formatted_address


class AccountContactAdmin(ContactAdminMixin, ExportActionModelAdmin):
    icon = '<i class="material-icons">person</i>'
    list_display = ["email", "first_name", "last_name", "account"]
    search_fields = [
        "first_name__unaccent",
        "last_name__unaccent",
        "account__id",
        "email__unaccent",
    ]
    resource_class = AccountContactResource
    autocomplete_fields = ["account"]

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.base_fields["account"].widget.can_add_related = False
        form.base_fields["account"].widget.can_edit_related = False
        form.base_fields["account"].required = False
        form.base_fields["account"].help_text = "Leave blank to create a new account"
        return form

    def save_model(self, request, obj, form, change):
        account = None
        if not obj.account_id:
            # auto-create an account
            account = models.Account()
            account.save()
            obj.account = account
        super().save_model(request, obj, form, change)

    def __init__(self, *args, **kwargs):
        self.fieldsets = (
            (
                "Account Info",
                {
                    "fields": (
                        "account",
                        "legal_guardian",
                        "emergency_contact",
                        "relationship",
                    )
                },
            ),
        ) + self.fieldsets
        super().__init__(*args, **kwargs)


admin_site.register(models.Account, AccountAdmin)
admin_site.register(models.AccountContact, AccountContactAdmin)
