from django.conf.urls import url

from dashboard import views

# from workspaces import views as workspace_views

app_name = "dashboard"

urlpatterns = [
    url("^$", views.home, name="home"),
    url("^attendance/$", views.attendance_overview, name="attendance-overview"),
    url(
        "^attendance/volunteers/$",
        views.attendance_overview,
        name="volunteer-attendance",
        kwargs={"type": "volunteer"},
    ),
    url("^progress/$", views.student_progress_overview, name="progress-overview"),
    url("^clubs/$", views.all_periods, name="all-periods"),
    url("^clubs/(?P<club_id>[^/]+)/$", views.club, name="club"),
    url(
        "^clubs/(?P<club_id>[^/]+)/attendance/$",
        views.attendance_overview,
        name="club-attendance",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/attendance/volunteer/$",
        views.attendance_overview,
        name="club-volunteer-attendance",
        kwargs={"type": "volunteer"},
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/progress/$",
        views.student_progress_overview,
        name="club-progress-overview",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/sessions/(?P<session_id>[^/]+)/$",
        views.club,
        name="club",
    ),
    url("^clubs/quizzes/(?P<quiz_id>[^/]+)/$", views.quiz_answers, name="quiz-answers"),
    url(
        "^clubs/(?P<club_id>[^/]+)/quizzes/(?P<quiz_id>[^/]+)/$",
        views.quiz_answers,
        name="quiz-answers",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/add-student/$",
        views.add_student,
        name="club-add-student",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students.csv$",
        views.download_student_roster,
        name="club-students-csv",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/volunteers.csv$",
        views.download_volunteer_roster,
        name="club-volunteers-csv",
    ),
    url(
        "^volunteers/(?P<volunteer_id>[^/]+)/$",
        views.volunteer_details,
        name="volunteer-details",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/volunteers/(?P<volunteer_id>[^/]+)/$",
        views.volunteer_details,
        name="volunteer-details",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students/(?P<student_id>[^/]+)/$",
        views.student_details,
        name="student-details",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students/(?P<student_id>[^/]+)/update/$",
        views.update_student,
        name="update-student",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students/(?P<student_id>[^/]+)/award-badge/$",
        views.award_badge,
        name="award-badge",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students/(?P<student_id>[^/]+)/award-badge/"
        "(?P<badge_id>[^/]+)/$",
        views.award_badge,
        name="award-badge",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students/(?P<student_id>[^/]+)/revoke-badge/"
        "(?P<badge_id>[^/]+)/$",
        views.revoke_badge,
        name="revoke-badge",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/students/(?P<student_id>[^/]+)/create-login/$",
        views.create_student_login,
        name="create-student-login",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/announcements/$",
        views.manage_announcements,
        name="manage-announcements",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/announcements/add$",
        views.edit_announcement,
        name="add-announcement",
    ),
    url(
        "^clubs/(?P<club_id>[^/]+)/announcements/(?P<announcement_id>[^/]+)/$",
        views.edit_announcement,
        name="edit-announcement",
    ),
    url(
        "^training-sessions/$",
        views.training_sessions_index,
        name="training-sessions-index",
    ),
    url(
        "^training-sessions/(?P<session_id>[^/]+)/$",
        views.training_session,
        name="training-session",
    ),
    # url('^workspaces/(?P<student_id>[^/]+)/view/$',
    #     workspace_views.workspace, name='student-workspace', kwargs={'read_only': True}),
    # url('^workspaces/(?P<student_id>[^/]+)/$',
    #     workspace_views.workspace, name='student-workspace', kwargs={'read_only': False}),
    url(
        "^student-projects/(?P<student_id>[^/]+)/(?P<project_slug>[^/]+)/download/$",
        views.download_student_project,
        name="download-student-project",
    ),
    # url('^student-projects/(?P<student_id>[^/]+)/(?P<project_slug>[^/]+)/$',
    #     views.student_project, name='student-project'),
    # url('^student-projects/(?P<student_id>[^/]+)/(?P<project_slug>[^/]+)/healthcheck/$',
    #     views.student_project_healthcheck, name='student-project-healthcheck'),
    url("^create-account/$", views.create_account, name="create-account"),
    url("^chat/$", views.chat, name="chat"),
]
