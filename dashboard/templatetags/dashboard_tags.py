from django.conf import settings
from django.contrib.staticfiles.templatetags.staticfiles import static
from django.template import Library
from django.urls import reverse
from django.utils import timezone
from django.utils.safestring import mark_safe
from easy_thumbnails.files import get_thumbnailer

from clubs.models import StudentAttendance, VolunteerAttendance
from courses.models import BadgeAssertion
from volunteers.models import Volunteer

register = Library()

THUMB_SIZE = 42


@register.inclusion_tag("dashboard/includes/attendance_item.html", takes_context=True)
def attendance_item(context, attendance):
    extra_info = ""
    request = context.get("request")
    moved = False
    badges = BadgeAssertion.objects.none()

    if isinstance(attendance, StudentAttendance):
        attendance_type = "student"
        id = attendance.student.id
        name = str(attendance.student)
        photo = attendance.student.photo
        club = attendance.session.club
        details_url = reverse(
            "dashboard:student-details",
            kwargs={"club_id": club.id, "student_id": attendance.student.pk},
        )
        badges = BadgeAssertion.objects.filter(
            student=attendance.student, student_registration__club=club
        )
        pronouns = attendance.student.preferred_pronouns
        extra_info = attendance.student.grade_name or ""
        birth_date = attendance.student.birth_date
        age = attendance.student.get_age()
        if attendance.student.grade:
            if attendance.student.grade > 0 and attendance.student.grade <= 12:
                extra_info += " grade"
        if hasattr(attendance, "student_registration"):
            if attendance.student_registration.status == "PENDING":
                info = "This student's registration is not yet complete."
                extra_info = mark_safe(
                    extra_info
                    + f' <em class="unverified" title="{info}">(registration pending)</em>'
                )
        else:
            moved = True
            info = "This student is no longer registered in this club."
            extra_info = mark_safe(
                extra_info + f'  <em class="moved" title="{info}">(moved)</em>'
            )

    elif isinstance(attendance, VolunteerAttendance):
        attendance_type = "volunteer"
        id = attendance.volunteer.id
        name = str(attendance.volunteer)
        photo = attendance.volunteer.photo
        pronouns = attendance.volunteer.preferred_pronouns
        extra_info = (
            attendance.assignment and attendance.assignment.get_role_display() or ""
        )
        birth_date = attendance.volunteer.birth_date
        age = attendance.volunteer.age
        if not attendance.assignment.verified:
            reason = "This mentor has one or more requirements pending."
            extra_info = mark_safe(
                extra_info
                + f' <em class="unverified" title="{reason}">(unverified)</em>'
            )
        details_url = reverse(
            "dashboard:volunteer-details",
            kwargs={"volunteer_id": attendance.volunteer.pk},
        )

    else:
        raise ValueError(
            "Expected `VolunteerAttendance` or `StudentAttendance` instance"
        )

    thumb_url = static("hive/images/generic-user.png")
    if photo:
        thumbnailer = get_thumbnailer(photo)
        thumb_opts = {"size": (42, 42), "crop": True}
        try:
            thumb_url = thumbnailer.get_thumbnail(thumb_opts).url
        except Exception:
            if getattr(settings, "THUMBNAIL_DEBUG", False):
                raise
            pass

    try:
        user_volunteer = request.user.contact.volunteer
    except Volunteer.DoesNotExist:
        user_volunteer = None

    user_roles = []
    if user_volunteer is not None:
        assignments = user_volunteer.assignments.filter(club=attendance.session.club)
        user_roles = [a.role for a in assignments]

    # Don't allow changing attendance for future
    can_change_status = (
        not attendance.is_future_date() and not attendance.session.canceled
    )
    can_remove = False

    # Volunteers can only remove their own attendance on future dates
    if attendance_type == "volunteer":
        can_remove = (
            attendance.volunteer == user_volunteer and attendance.is_future_date()
        )

        # Team captains and superusers can adjust attendance data as needed
        if (
            set(("TEAM_CAPTAIN", "SCHOOL_LIAISON")) & set(user_roles)
            or request.user.is_superuser
        ):
            can_remove = True

    today = timezone.now().date()
    is_birthday = today == birth_date.replace(year=today.year)

    return {
        "attendance": attendance,
        "pk": attendance.pk,
        "type": attendance_type,
        "id": id,
        "name": name,
        "pronouns": pronouns,
        "details_url": details_url,
        "thumb_url": thumb_url,
        "thumb_size": THUMB_SIZE,
        "present": attendance.present,
        "notes": attendance.notes,
        "extra_info": extra_info,
        "can_change_status": can_change_status,
        "can_remove": can_remove,
        "is_birthday": is_birthday,
        "age": age,
        "moved": moved,
        "badges": badges,
    }


@register.inclusion_tag("dashboard/includes/attendance_item.html", takes_context=True)
def attendance_item_template(context, attendance_type, session):
    # See `onAddVolunteerClick()` in dashboard/club.html for replacement tags
    can_remove = True
    can_change_status = not session.is_future_date()

    return {
        "type": attendance_type,
        "pk": "__pk__",
        "id": "__id__",
        "name": "__name__",
        "details_url": "__profile_url__",
        "thumb_url": "__photo__",
        "extra_info": "__extra_info__",
        "thumb_size": THUMB_SIZE,
        "can_remove": can_remove,
        "can_change_status": can_change_status,
    }


@register.simple_tag
def session_classes(session):
    classes = ["session", session.type.lower()]
    if session.canceled:
        classes.append("canceled")
    if session.is_future:
        classes.append("future")
    return " ".join(classes)
