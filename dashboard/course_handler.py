from urllib.parse import quote as quote_url

from django.db.models import Count
from django.urls import reverse

from courses import models
from courses.handler import BaseCourseHandler
from courses.views import RedirectException
from volunteers.views import get_profile, require_profile


class CourseHandler(BaseCourseHandler):
    def get_roadmap_context(self, request, context):
        detailed_activities_link = None
        roadmap = context["roadmap"]
        try:
            detailed_activities_link = models.ResourceLink.objects.get(
                course=roadmap.course, slug="detailed-activities"
            )
            context["detailed_activities"] = detailed_activities_link
        except models.ResourceLink.DoesNotExist:
            pass

        return context

    def get_course(self, request):
        if "course_id" not in request.session:
            available_courses = self.get_available_courses(request)
            if available_courses.count() == 1:
                course = available_courses[0]
                request.session["course_id"] = course.id
            else:
                next = quote_url(request.get_full_path())
                raise RedirectException(
                    "{}?next={}".format(reverse("courses:select-course"), next)
                )
        return models.Course.objects.get(pk=request.session["course_id"])

    def on_course_selected(self, request, course):
        request.session["course_id"] = course.id

    def get_roadmap(self, request):
        course = self.get_course(request)
        roadmap, created = models.UserRoadmap.objects.get_or_create(
            user=request.user, course=course
        )
        return roadmap

    def update_roadmap_step(self, request, roadmap, step_id, completed):
        roadmap_step = None
        try:
            roadmap_step = roadmap.completed_steps.get(step_id=step_id)
        except models.UserRoadmapCompletedStep.DoesNotExist:
            pass

        if not completed and roadmap_step is not None:
            roadmap_step.delete()
            # Delete UserRoadmap if it has no compelted steps
            if roadmap.completed_steps.count() == 0:
                roadmap.delete()
        elif completed and roadmap_step is None:
            # Save roadmap if it is not already saved
            if roadmap.pk is None:
                if roadmap.pk is None:
                    roadmap.save()
            roadmap_step = models.UserRoadmapCompletedStep.objects.create(
                step_id=step_id, roadmap=roadmap
            )

        return roadmap_step

    def get_available_courses(self, request):
        if request.user.is_superuser:
            courses = models.Course.objects.all()
        else:
            volunteer = get_profile(request)
            if not volunteer:
                response = require_profile(request)
                raise RedirectException(response)

            clubs = volunteer.clubs.all()
            courses = models.Course.objects.filter(clubs__in=clubs).distinct()
        return courses.annotate(num_units=Count("units")).filter(num_units__gt=0)

    def get_resource_links(self, **kwargs):
        qs = super().get_resource_links(**kwargs)
        return qs.filter(visibility=models.ResourceLink.visibility.MENTORS)
