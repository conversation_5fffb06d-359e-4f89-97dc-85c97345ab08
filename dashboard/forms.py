from django import forms

from clubs import models
from hive.forms import BaseForm


class AnnouncementForm(BaseForm, forms.ModelForm):
    def __init__(self, clubs, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["clubs"].queryset = clubs

    class Meta:
        model = models.Announcement
        fields = [
            "clubs",
            "publish_date",
            "archive_date",
            "pinned",
            "visibility",
            "text",
            "icon",
            "icon_color",
        ]


class SessionForm(BaseForm, forms.ModelForm):
    apply_to_all = forms.BooleanField(label="Apply to all sessions", required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["conference_link"].label = "Conference URL"
        self.fields["conference_password"].label = "Conference password"
        self.fields["canceled"].label = "Cancel this session"

    class Meta:
        model = models.Session
        fields = ["conference_link", "conference_password", "apply_to_all", "canceled"]
