import csv
import logging
from collections import OrderedDict

from dateutil.parser import parse as parse_date
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib import messages
from django.core.exceptions import PermissionDenied, ValidationError
from django.core.validators import URLValidator
from django.db.models import (
    Case,
    ExpressionWrapper,
    PositiveIntegerField,
    Q,
    Value,
    When,
)
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone

from clubs.constants import REGISTRATION_STATUS_CHOICES
from clubs.models import (
    Announcement,
    Club,
    Session,
    Student,
    StudentAttendance,
    StudentRegistration,
    Volunteer,
)
from clubs.utils import (
    get_latest_period,
    get_period_description,
    get_periods_lookup,
    parse_period,
)
from courses.badges import badgr
from courses.badges.assertion_validation import AssertionValidation
from courses.badges.narrative_template import NarrativeTemplate
from courses.models import Badge, BadgeAssertion, Course, Quiz, StudentQuiz, UserRoadmap
from hive.utils import format_date, get_search_results
from ide import views as ide_views
from students.constants import STUDENT_LOGIN_SERVICE_CHOICES
from students.forms import StudentForm
from students.utils import get_student_participated_courses
from volunteers import utils as volunteer_utils
from volunteers import verification
from volunteers.constants import (
    LANGUAGE_PROFICENCY_CHOICES,
    VOLUNTEER_REQUIREMENT_STATUS_CHOICES,
)
from volunteers.models import TrainingSession
from volunteers.views import get_profile, profile_required

from . import forms
from .forms import AnnouncementForm

logger = logging.getLogger(__name__)


def create_account(request):
    url = reverse("volunteers:profile")
    next = request.GET.get("next")
    if next:
        url += "?next=" + next
    return HttpResponseRedirect(url)


def get_volunteer_clubs(request, as_of=None):
    volunteer = get_profile(request)
    return volunteer_utils.get_volunteer_clubs(
        volunteer, request.GET.get("period"), as_of=as_of
    )


def home(request):
    if not request.user or not request.user.is_authenticated:
        return HttpResponseRedirect(reverse("volunteers:opportunities"))

    def view(request):
        volunteer = get_profile(request)

        # allow superuser to show all clubs on dashboard
        show_all = request.user.is_superuser

        # Warning: don't modify this queryset here. Instead, modify
        # volunteers.utils.get_volunteer_clubs()
        clubs = get_volunteer_clubs(request)

        # get upcoming training sessions
        training_sessions = volunteer.get_upcoming_training_sessions()
        show_all_sessions = volunteer.facilitated_training_sessions.all().count() > 0

        # get any pending requirements
        requirements = volunteer.requirements.filter(
            requirement_type__responsible_party="VOLUNTEER"
        ).pending()

        # get most recent assignment (for checklist)
        assignments = volunteer.assignments.order_by("-club__start_date")
        assignment = None
        if assignments.count() > 0:
            assignment = assignments[0]

        training_online = all(session.online for session in training_sessions)

        context = {
            "user": request.user,
            "show_all_clubs": show_all,
            "requirements": requirements,
            "has_verified_clubs": any(c for c in clubs if c.is_verified),
            "clubs": clubs,
            "training_sessions": training_sessions.distinct(),
            "training_online": training_online,
            "show_all_sessions": show_all_sessions or request.user.is_superuser,
            "assignment": assignment,
        }
        return render(request, "dashboard/home.html", context)

    return profile_required(view)(request)


def get_course(request):
    course = None
    if request.session.get("course_id"):
        course = Course.objects.get(pk=request.session["course_id"])
    else:
        clubs = get_volunteer_clubs(request)
        courses = Course.objects.filter(clubs__in=clubs)
        if courses.count() == 1:
            course = courses[0]
    return course


def course_required(view):
    def wrapper(request, *args, **kwargs):
        course = get_course(request)
        if course:
            request.session["course_id"] = course.id
        else:
            return redirect(reverse("dashboard:select-course"))
        return view(request, *args, **kwargs)

    return wrapper


@profile_required
def attendance_overview(request, type="student", club_id=None):
    volunteer = get_profile(request)

    if request.GET.get("period"):
        year, period = parse_period(request.GET["period"])
    else:
        year, period = get_latest_period()

    if club_id is not None:
        clubs = [get_object_or_404(Club, pk=club_id)]
        clubs[0].is_verified = verification.get_club_verifications(volunteer, clubs[0])
    else:
        clubs = get_volunteer_clubs(request)

    for club in clubs:
        if not club.is_verified:
            continue
        club.attendees = []

        past_sessions = club.sessions.past().exclude(canceled=True)

        if type == "volunteer":
            assignments = club.volunteer_assignments.annotate(
                role_order=ExpressionWrapper(
                    Case(
                        When(Q(role="TEAM_CAPTAIN"), then=Value(0)),
                        When(Q(role="MENTOR"), then=Value(1)),
                        When(Q(role="FLOATER"), then=Value(2)),
                        default=Value(3),
                    ),
                    output_field=PositiveIntegerField(),
                )
            ).order_by("role_order", "volunteer__first_name", "volunteer__last_name")
            for assignment in assignments:
                volunteer = assignment.volunteer
                volunteer.role = assignment.role
                volunteer.attendances = []
                for session in club.sessions.all():
                    attendance = {
                        "session": session,
                        "role": assignment.role,
                        "present": (
                            session.is_past()
                            and session.type == "CLUB"
                            and not session.canceled
                            and volunteer in session.volunteers_attended
                        ),
                        "scheduled": (
                            session.type == "CLUB"
                            and not session.canceled
                            and volunteer in session.volunteers
                        ),
                    }
                    volunteer.attendances.append(attendance)
                club.attendees.append(volunteer)
        else:
            registrations = club.student_registrations.order_by(
                "-status", "student__first_name", "student__last_name"
            )
            for reg in registrations:
                student = reg.student
                student.status = reg.status
                student.attendances = []
                for session in club.sessions.all():
                    attendance = {
                        "session": session,
                        "present": (
                            session.is_past()
                            and not session.canceled
                            and student in session.students_attended
                        ),
                    }
                    student.attendances.append(attendance)
                club.attendees.append(student)

        num_sessions = past_sessions.filter(type="CLUB").count()
        num_students = club.student_registrations.exclude(status="CANCELED").count()

        for attendee in club.attendees:
            attendee.num_sessions_attended = len(
                [a for a in attendee.attendances if a["present"]]
            )
            if num_sessions > 0:
                attendee.attendance_rate = round(
                    (attendee.num_sessions_attended / num_sessions) * 100
                )

        total_student_attendances = 0
        total_coverage_rate = 0
        # total_mentor_attendances = 0
        for session in club.sessions.exclude(canceled=True):
            total_student_attendances += len(session.students_attended)
            total_coverage_rate += session.attended_coverage_percent or 0
            # total_mentor_attendances += len(session.students_attended)

        if num_sessions > 0 and num_students > 0:
            club.avg_student_attendance = round(
                total_student_attendances / (num_sessions * num_students) * 100
            )
            club.avg_coverage_rate = (
                num_sessions and round(total_coverage_rate / num_sessions) or 0
            )

    context = {
        "club_id": club_id,
        "type": type,
        "clubs": clubs,
        "year": year,
        "period": period,
        "period_description": get_period_description(year, period),
    }
    return render(request, "dashboard/attendance_overview.html", context)


@profile_required
def student_progress_overview(request, club_id=None):
    if request.GET.get("period"):
        year, period = parse_period(request.GET["period"])
    else:
        year, period = get_latest_period()

    registrations = StudentRegistration.objects.for_period(year, period).order_by(
        "course_id"
    )

    if club_id is not None:
        registrations = registrations.filter(club_id=club_id)

    context = {
        "registrations": registrations,
        "year": year,
        "period": period,
        "period_description": get_period_description(year, period),
    }
    return render(request, "dashboard/progress_overview.html", context)


@profile_required
def training_sessions_index(request):
    volunteer = get_profile(request)

    if request.user.is_superuser:
        past_sessions = TrainingSession.objects.filter(date__lt=timezone.now().date())
    else:
        past_sessions = TrainingSession.objects.filter(
            date__lt=timezone.now().date(), facilitators=volunteer
        )

    context = {
        "upcoming_sessions": volunteer.get_upcoming_training_sessions(),
        "past_sessions": past_sessions.distinct(),
    }

    return render(request, "dashboard/training_sessions_index.html", context)


@profile_required
def training_session(request, session_id):
    volunteer = get_profile(request)
    session = get_object_or_404(TrainingSession, pk=session_id)

    if request.method == "POST":
        session.attendees.clear()
        for volunteer_id in request.POST.getlist("volunteers"):
            session.attendees.add(int(volunteer_id))

    can_manage = request.user.is_superuser or volunteer in session.facilitators.all()
    context = {"session": session, "can_manage": can_manage}

    return render(request, "dashboard/training_session.html", context)


@profile_required
def club(request, club_id, session_id=None):
    club = get_object_or_404(Club, pk=club_id)
    volunteer_user = get_profile(request)

    is_staff = request.user.is_staff
    is_team_captain = volunteer_user in club.team_captains
    is_school_liaison = volunteer_user in club.get_volunteers_for_role("SCHOOL_LIAISON")

    try:
        verification.verify_club_access(volunteer_user, club)
    except verification.VerificationFailed:
        verified = False
    else:
        verified = True

    if session_id:
        session = Session.objects.get(pk=session_id)
    else:
        next_sessions = club.sessions.filter(date__gte=timezone.now().date()).order_by(
            "date"
        )
        session = None
        if next_sessions.count() == 0:
            if club.sessions.count() > 0:
                session = club.sessions.order_by("-date")[0]
        else:
            session = next_sessions[0]

    # handle session management actions
    manage_session_form = forms.SessionForm(instance=session)
    if request.method == "POST" and request.POST.get("save_session"):
        manage_session_form = forms.SessionForm(request.POST, instance=session)
        if manage_session_form.is_valid():
            session = manage_session_form.save()
            if manage_session_form.cleaned_data.get("apply_to_all"):
                # Apply conference settings to all sessions
                club.sessions.exclude(pk=session.pk).update(
                    conference_link=session.conference_link,
                    conference_password=session.conference_password,
                )
            return HttpResponseRedirect(".")

    # get existing attendances
    volunteer_attendances = OrderedDict()
    student_attendances = OrderedDict()

    volunteer_order = ("volunteer__first_name", "volunteer__last_name")
    student_order = ("student__first_name", "student__last_name")

    if session and session.type == "CLUB":
        for attendance in session.volunteer_attendances.order_by(*volunteer_order):
            volunteer_attendances[attendance.volunteer.pk] = attendance

        for attendance in session.student_attendances.order_by(*student_order):
            student_attendances[attendance.student.pk] = attendance

        # Add unsaved StudentAttendance objects based on current registration info
        registrations = club.student_registrations.all()
        for reg in registrations.order_by(*student_order):
            if not student_attendances.get(reg.student.pk):
                student_attendances[reg.student.pk] = StudentAttendance(
                    session=session, student=reg.student
                )
            # Annotate club registration on attendance item
            student_attendances[reg.student.pk].student_registration = reg

    role_order = ["TEAM_CAPTAIN", "SCHOOL_LIAISON", "MENTOR", "FLOATER", "GUEST"]
    volunteer_attendances = OrderedDict(
        sorted(
            volunteer_attendances.items(),
            key=lambda a: role_order.index(a[1].assignment.role),
        )
    )

    sessions = [s.pk for s in club.sessions.all()]
    prev_session = None
    next_session = None
    if session:
        i = sessions.index(session.pk)
        if i > 0:
            prev_session = sessions[i - 1]
        if i < len(sessions) - 1:
            next_session = sessions[i + 1]

    if is_staff or is_team_captain:
        announcements = club.announcements.current()
        archived_announcements = club.announcements.exclude(pk__in=announcements)
    else:
        announcements = club.announcements.current().for_volunteers()
        archived_announcements = None

    # Sort volunteer dropdown by:
    dropdown_priority = ["FLOATER", "MENTOR", "TEAM_CAPTAIN", "SCHOOL_LIAISON", "GUEST"]
    dropdown_assignments = sorted(
        club.volunteer_assignments.all(), key=lambda a: dropdown_priority.index(a.role)
    )
    attending_ids = volunteer_attendances.keys()
    other_volunteers = OrderedDict()
    for assignment in dropdown_assignments:
        volunteer = assignment.volunteer
        if volunteer in other_volunteers or volunteer.pk in attending_ids:
            continue
        other_volunteers[volunteer] = assignment

    # FIXME: might be better to use the permissions framework at this point
    show_announcements = is_staff or is_team_captain or club.announcements.count() > 0
    can_add_announcements = is_staff or is_team_captain or is_school_liaison
    can_edit_session = is_staff or is_team_captain or is_school_liaison
    can_add_other_volunteers = is_staff or is_team_captain
    can_add_students = is_staff or is_team_captain or is_school_liaison
    can_download_roster = is_staff or is_team_captain or is_school_liaison
    show_add_self = (
        not can_add_other_volunteers and volunteer_user.pk not in attending_ids
    )

    context = {
        "prev_session": prev_session,
        "next_session": next_session,
        "club": club,
        "session": session,
        "announcements": announcements,
        "archived_announcements": archived_announcements,
        "team_captains": club.team_captains,
        "volunteer_attendances": volunteer_attendances,
        "student_attendances": student_attendances,
        "dropdown_assignments": dropdown_assignments,
        "other_volunteers": other_volunteers,
        "quiz": quiz,
        "volunteer_user": volunteer_user,
        "can_add_other_volunteers": can_add_other_volunteers,
        "can_add_students": can_add_students,
        "can_download_roster": can_download_roster,
        "show_add_self": show_add_self,
        "can_add_announcements": can_add_announcements,
        "show_announcements": show_announcements,
        "can_edit_session": can_edit_session,
        "manage_session_form": manage_session_form,
        "verified": verified,
    }
    return render(request, "dashboard/club.html", context)


def download_student_roster(request, club_id):
    club = get_object_or_404(Club, pk=club_id)
    volunteer = get_profile(request)

    # FIXME: see comment about permissions at the end of `def club()`
    is_team_captain = volunteer in club.team_captains
    is_school_liaison = volunteer in club.get_volunteers_for_role("SCHOOL_LIAISON")

    # Only staff and team captains can download roster
    if not (volunteer.user.is_staff or is_team_captain or is_school_liaison):
        raise PermissionDenied

    # Verify club access
    try:
        verification.verify_club_access(volunteer, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = 'attachment; filename="students.csv"'

    writer = csv.writer(response)
    writer.writerow(
        [
            "reg_date",
            "student_id",
            "status",
            "first_name",
            "last_name",
            "birth_date",
            "default_password",
            "grade",
            "primary_contact_name",
            "primary_contact_relationship",
            "primary_contact_email",
            "primary_contact_phone",
            "shirt_size",
            "previous_activities",
            "possible_conflicts",
            "referred_by",
            "postal_code",
            "county",
            "school_name",
            "gender",
            "race",
            "ethnicity",
        ]
    )

    for student_reg in club.student_registrations.order_by("pk"):
        student = student_reg.student
        default_contact = None
        if student.account:
            default_contact = student.account.default_contact
        extra_data = student_reg.extra_data or {}
        reg_date = ""
        if student_reg.registration_id:
            reg_date = format_date(
                student_reg.registration.date, settings.CSV_DATE_FORMAT
            )
        writer.writerow(
            [
                reg_date,
                student.student_id,
                student_reg.status,
                student.first_name,
                student.last_name,
                student.birth_date,
                student.default_password,
                student.grade_name,
                default_contact and default_contact.name or "",
                default_contact and default_contact.relationship or "",
                default_contact and default_contact.email or "",
                default_contact and default_contact.phone or "",
                student.shirt_size,
                extra_data.get("previous_activities", ""),
                extra_data.get("possible_conflicts", ""),
                extra_data.get("referred_by", ""),
                student.postal_code,
                student.county,
                student.school_name,
                student.gender,
                student.race,
                student.ethnicity,
            ]
        )

    return response


def download_volunteer_roster(request, club_id):
    club = get_object_or_404(Club, pk=club_id)
    volunteer = get_profile(request)

    # Only staff and team captains can download roster
    if not (volunteer.user.is_staff or volunteer in club.team_captains):
        raise PermissionDenied

    # Verify club access
    try:
        verification.verify_club_access(volunteer, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = 'attachment; filename="volunteers.csv"'

    writer = csv.writer(response)
    writer.writerow(["role", "first_name", "last_name", "email", "phone"])

    sort_order = ("volunteer__first_name", "volunteer__last_name")
    for assignment in club.volunteer_assignments.order_by(*sort_order):
        writer.writerow(
            [
                assignment.get_role_display(),
                assignment.volunteer.first_name,
                assignment.volunteer.last_name,
                assignment.volunteer.email,
                assignment.volunteer.phone,
            ]
        )

    return response


@profile_required
def quiz(request, club_id, student_quiz_id):
    volunteer = get_profile(request)
    club = get_object_or_404(Club, pk=club_id)
    verification.verify_club_access(volunteer, club)

    answers = StudentQuiz.objects.get(pk=student_quiz_id)
    context = {"answers": answers}
    return render(request, "dashboard/quiz_result.html", context)


@profile_required
def volunteer_details(request, volunteer_id, club_id=None):
    volunteer = get_object_or_404(Volunteer, pk=volunteer_id)
    club = None
    if club_id:
        club = get_object_or_404(Club, pk=club_id)

    # Get all pending requirements
    pending_requirements = volunteer.requirements.pending()

    # Only team captains in the same club as the volunteer can view requirements
    volunteer_user = request.user.contact.volunteer

    # Get clubs for which user is a team captain
    team_clubs = []
    for club in volunteer.clubs.all():
        team_captains = [
            a.volunteer for a in club.volunteer_assignments.filter(role="TEAM_CAPTAIN")
        ]
        if volunteer_user in team_captains:
            team_clubs.append(club)

    is_superuser = request.user.is_superuser
    is_team_captain = len(team_clubs) > 0
    show_requirements = is_superuser or is_team_captain

    if request.method == "POST":
        # save requirement statuses
        for requirement in pending_requirements:
            status = request.POST.get("requirement_{}_status".format(requirement.id))
            completed_date = request.POST.get(
                "requirement_{}_completed_date".format(requirement.id)
            )
            if completed_date:
                completed_date = parse_date(completed_date).date()
            # If status has changed, update it
            if status and status != requirement.status:
                requirement.status = status
                # Set the completed date if set to completed
                if status == "COMPLETED":
                    requirement.completed_date = completed_date or timezone.now().date()
                else:
                    requirement.completed_date = None
                requirement.save()

    languages = []
    for lang, prof in volunteer.languages.items():
        if int(prof) > 0:
            proficiency = dict(LANGUAGE_PROFICENCY_CHOICES).get(int(prof))
            languages.append(
                {
                    "lang": lang,
                    "lang_name": dict(settings.LANGUAGES).get(lang),
                    "proficiency": proficiency.replace(f"{prof} - ", ""),
                }
            )

    context = {
        "team_captain_or_volunteer": ["TEAM_CAPTAIN", "VOLUNTEER"],
        "is_superuser": is_superuser,
        "is_team_captain": is_team_captain,
        "show_requirements": show_requirements,
        "pending_requirements": pending_requirements,
        "requirement_status_choices": VOLUNTEER_REQUIREMENT_STATUS_CHOICES,
        "volunteer": volunteer,
        "languages": languages,
    }

    return render(request, "dashboard/volunteer_details.html", context)


@profile_required
def student_details(request, club_id, student_id):
    volunteer = get_profile(request)
    club = get_object_or_404(Club, pk=club_id)
    student = get_object_or_404(Student, pk=student_id)
    student_reg = get_object_or_404(StudentRegistration, club=club, student=student)

    team_captain = (
        volunteer.assignments.filter(club=club, role="TEAM_CAPTAIN").count() > 0
    )

    verification.verify_student_access(volunteer, student, club)

    account = (
        student_reg.registration and student_reg.registration.account or student.account
    )

    student_contacts = []
    if account:
        default_contact = account.default_contact
        if default_contact:
            student_contacts.append(default_contact)
        student_contacts.extend(c for c in account.contacts.exclude(pk=default_contact))

    # filter out empty values
    extra_data = student_reg.extra_data or {}
    extra_data = {k: v for k, v in extra_data.items() if v}

    # attendances
    attendance_lookup = {}
    for attendance in student.session_attendances.all():
        attendance_lookup[attendance.session.id] = attendance

    attendances = []
    for registration in student.registrations.order_by("-club__start_date"):
        sessions = []
        for session in registration.club.sessions.order_by("date").exclude(
            canceled=True
        ):
            sessions.append((session, attendance_lookup.get(session.id)))
        attendances.append((registration.club, sessions))

    # get list of available login types:
    student_logins = student.logins.order_by("-active")
    available_login_services = []
    current_student_logins = [login.service for login in student_logins if login.active]
    for service_type, label in STUDENT_LOGIN_SERVICE_CHOICES:
        if service_type not in current_student_logins:
            available_login_services.append((service_type, label))

    # Use default course if student has not selected one
    course = student_reg.current_course

    roadmap = None
    if course:
        # Use empty roadmap if student has no roadmap yet
        roadmap = student_reg.current_roadmap or UserRoadmap(
            user=student.user, course=course
        )

    context = {
        "club": club,
        "student": student,
        "contacts": student_contacts,
        "student_reg": student_reg,
        "extra_data": extra_data,
        "attendances": attendances,
        "student_logins": student_logins,
        "can_edit_student": team_captain or volunteer.user.is_staff,
        "badges_enabled": settings.BADGES_ENABLED,
        "available_login_services": available_login_services,
        "GUACAMOLE_BASE_URL": settings.GUACAMOLE_BASE_URL,
        "roadmap": roadmap,
        "course": course,
    }
    return render(request, "dashboard/student_details.html", context)


@profile_required
def create_student_login(request, club_id, student_id):
    volunteer = get_profile(request)
    club = get_object_or_404(Club, pk=club_id)
    student = get_object_or_404(Student, pk=student_id)

    # Verify club access
    try:
        verification.verify_student_access(volunteer, student, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    role_can_edit = (
        volunteer.assignments.filter(
            club=club, role__in=("TEAM_CAPTAIN", "SCHOOL_LIAISON")
        ).count()
        > 0
    )
    can_edit_student = role_can_edit or volunteer.user.is_staff

    services = dict(STUDENT_LOGIN_SERVICE_CHOICES)

    if request.method == "POST" and can_edit_student:
        service = request.POST.get("service")
        student.logins.create(service=service)
        service_label = services[service]
        messages.add_message(
            request,
            messages.INFO,
            "Successfully created {} login for {}".format(service_label, student.name),
        )

    return HttpResponseRedirect(
        reverse(
            "dashboard:student-details",
            kwargs={"club_id": club_id, "student_id": student_id},
        )
    )


# TODO: add view for modifying an existing badge
#
# Note: while the Badgr API supports updating badges, the `narrativeTemplate` extension does not
# store individual field values on the assertion, so we have no way to reconstruct a form view with
# existing values. We could simply display a blank form when updating, but for now its easier to
# just revoke and create a new assertion.


@profile_required
def award_badge(request, club_id, student_id, badge_id=None):
    volunteer = get_profile(request)
    club = get_object_or_404(Club, pk=club_id)
    student = get_object_or_404(Student, pk=student_id)
    student_reg = get_object_or_404(StudentRegistration, club=club, student=student)
    badge = None
    evidence_url = None
    required_fields = None
    narrative_template = None
    assertion_validation = None

    # Verify student access
    try:
        verification.verify_student_access(volunteer, student, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    student_courses = get_student_participated_courses(student)

    if badge_id:
        badge = get_object_or_404(Badge, pk=badge_id)

    # TODO: build a django form dynamically based on the field definitions
    # so we can use the django form validation process
    form_errors = []
    required_fields = []

    # Check if student already has badge
    try:
        existing_assertion = BadgeAssertion.objects.get(student=student, badge=badge)
    except BadgeAssertion.DoesNotExist:
        existing_assertion = None

    if badge:
        # Get the narrative template extension from the badge, or make a default one
        narrative_template_ext = badge.data.get(
            "extensions:narrativeTemplate",
            {
                "evidenceTemplate": {
                    "template": "{{ evidence_narrative }}",
                    "fields": [
                        {
                            "name": "evidence_narrative",
                            "type": "textarea",
                            "label": "Evidence narrative",
                        }
                    ],
                }
            },
        )

        narrative_template = NarrativeTemplate(narrative_template_ext)
        # Set default values for template fields
        narrative_template.load_values(
            {
                "club": f"{club.name} - {club.period_description}",
                "mentor_name": volunteer.name,
            }
        )

        # Get required fields from the assertionValidation extension
        assertion_validation_ext = badge.data.get("extensions:assertionValidation")
        if assertion_validation_ext:
            assertion_validation = AssertionValidation(assertion_validation_ext)
            required_fields.extend(assertion_validation.get_required_fields())

    if not existing_assertion and badge and request.method == "POST":
        evidence_narrative = None
        form_errors = []

        # Initial validation
        # TODO: move to assertionValidation extension
        evidence_url = request.POST.get("evidence_url")
        if evidence_url:
            # validate the URL
            validate_url = URLValidator()
            try:
                validate_url(evidence_url)
            except ValidationError:
                form_errors.append("Please enter a valid URL for the evidence URL.")

        # validate fields in the narrative template
        narrative_template.load_values(request.POST, prefix="tpl_")
        if not narrative_template.is_valid():
            form_errors.extend(narrative_template.errors)

        # Render the evidence narrative
        evidence_narrative = narrative_template.render()

        # build the assertion
        assertion = badgr.build_assertion(
            badge, student, evidence_url, evidence_narrative
        )

        # Validate the assertion using the assertion validation extensions
        if assertion_validation and not assertion_validation.is_valid(assertion):
            form_errors.extend(assertion_validation.errors)

        # Double check that evidence narrative is not empty
        if not assertion["evidence"][0].get("narrative"):
            form_errors.append("Evidence narrative is required.")

        # Save badge if everything looks ok
        if not form_errors:
            # Upload assertion to Badgr
            res = badgr.save_assertion(assertion)

            # Save assertion to database
            #
            # If this fails for whatever reason, we can pick it up again on the next badge sync
            #     (see: coures.management.commands.sync_badges)
            db_assertion = BadgeAssertion(
                badge=badge,
                student=student,
                added_by=request.user,
                student_registration=student_reg,
                openbadge_url=res.result[0]["openBadgeId"],
            )
            db_assertion.update_openbadge_data(
                throttle=False
            )  # no need for throttle here
            db_assertion.save()

            messages.add_message(
                request,
                messages.INFO,
                'Successfully issued badge "{}" to {}'.format(
                    badge.data["name"], student.name
                ),
            )

            url = reverse(
                "dashboard:student-details",
                kwargs={"club_id": club_id, "student_id": student_id},
            )
            return HttpResponseRedirect(url)

    available_badges_by_course = OrderedDict()
    student_badges = student.badge_assertions.values("badge")
    for course in student_courses:
        badges = course.badges.exclude(pk__in=student_badges)
        if badges.count() == 0:
            continue
        available_badges_by_course[course] = badges

    context = {
        "available_badges_by_course": available_badges_by_course,
        "form_errors": form_errors,
        "required_fields": required_fields,
        "club": club,
        "student_courses": student_courses,
        "student": student,
        "volunteer": volunteer,
        "badge": badge,
        "existing_assertion": existing_assertion,
        "narrative_template": narrative_template,
        "evidence_url": evidence_url,
    }
    return render(request, "dashboard/award_badge.html", context)


@profile_required
def revoke_badge(request, club_id, student_id, badge_id):
    volunteer = get_profile(request)
    club = get_object_or_404(Club, pk=club_id)
    assertion = get_object_or_404(
        BadgeAssertion, student_id=student_id, badge_id=badge_id
    )
    badge = assertion.badge
    student = assertion.student

    if request.method == "POST" and request.POST.get("confirmed"):
        reason = request.POST["reason"]
        badgr.revoke_assertion(assertion, reason)
        assertion.delete()
        messages.add_message(
            request,
            messages.INFO,
            'Successfully revoked badge "{}" from {}'.format(
                badge.data["name"], student.name
            ),
        )

        url = reverse(
            "dashboard:student-details",
            kwargs={"club_id": club_id, "student_id": student_id},
        )
        return HttpResponseRedirect(url + "#badges")

    context = {
        "badge": badge,
        "student": student,
        "assertion": assertion,
        "club": club,
        "volunteer": volunteer,
    }
    return render(request, "dashboard/revoke_badge.html", context)


@profile_required
def quiz_answers(request, quiz_id, club_id=None):
    quiz = get_object_or_404(Quiz, pk=quiz_id)

    club = None
    if club_id:
        club = get_object_or_404(Club, pk=club_id)

    correct_answers = [q.correct_answers_set for q in quiz.questions.all()]
    team = None
    if club:
        team = club.students.all()
    results = quiz.build_results(correct_answers, team)

    def get_color(percent):
        # material red, orange, green 100
        if percent < 33:
            return "#ffcdd2"
        if percent > 66:
            return "#c8e6c9"
        return "#ffe0b2"

    for result in results:
        result["global_results"]["color"] = get_color(
            result["global_results"]["percent"]
        )
        if result.get("team_results"):
            result["team_results"]["color"] = get_color(
                result["team_results"]["percent"]
            )

    quizzes = [t.pk for t in quiz.course.quizzes.all()]

    next_quiz = None
    prev_quiz = None
    prev_url = None
    next_url = None

    i = quizzes.index(quiz.pk)
    if i > 0:
        prev_quiz = quizzes[i - 1]
    if i < len(quizzes) - 1:
        next_quiz = quizzes[i + 1]

    if next_quiz:
        kwargs = {"quiz_id": next_quiz}
        if club:
            kwargs["club_id"] = club.id
        next_url = reverse("dashboard:exit-ticket-answers", kwargs=kwargs)

    if prev_quiz:
        kwargs = {"quiz_id": prev_quiz}
        if club:
            kwargs["club_id"] = club.id
        prev_url = reverse("dashboard:exit-ticket-answers", kwargs=kwargs)

    context = {
        "club_id": club and club.id or None,
        "prev_url": prev_url,
        "next_url": next_url,
        "quiz": quiz,
        "quiz_results": results,
    }
    return render(request, "dashboard/quiz_answers.html", context)


@profile_required
def add_student(request, club_id):
    club = get_object_or_404(Club, pk=club_id)
    volunteer = get_profile(request)

    # Verify club access
    try:
        verification.verify_club_access(volunteer, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    # Only staff and team captains can add students
    if not (volunteer.user.is_staff or volunteer in club.team_captains):
        raise PermissionDenied

    club_url = reverse("dashboard:club", kwargs={"club_id": club.id})

    def register_student(student):
        status = "COMPLETED"
        # Set status to "PENDING" if student does not yet have a parent registration account
        if not student.account:
            status = "PENDING"

        StudentRegistration.objects.create(club=club, student=student, status=status)
        messages.add_message(
            request, messages.INFO, "Added {} to {}".format(student.name, club.name)
        )
        return HttpResponseRedirect(club_url)

    search_term = request.GET.get("q", request.POST.get("q", ""))
    matching_students = []

    instance = None
    if "student_id" in request.GET:
        instance = Student.objects.get(pk=request.GET["student_id"])
    elif request.POST.get("student_id"):
        instance = Student.objects.get(pk=request.POST["student_id"])

    if search_term:
        students = Student.objects.all()
        search_fields = ["first_name__unaccent", "last_name__unaccent"]
        matching_students = get_search_results(
            students, request.GET["q"], search_fields
        )

    # Check for existing registration to prevent duplicates
    has_existing_reg = (
        StudentRegistration.objects.filter(student=instance, club=club).count() > 0
    )

    if not has_existing_reg and request.method == "POST":
        if "student_id" in request.POST:
            instance = Student.objects.get(pk=request.POST["student_id"])
        form = StudentForm(request.POST, instance=instance)
        if form.is_valid():
            student = form.save()
            return register_student(student)

    else:
        initial = {}

        # guess defaults from search query
        if not instance and " " in search_term:
            initial["first_name"], initial["last_name"] = search_term.split(" ", 1)

        if instance and instance.grade:
            initial["grade_level"] = instance.grade

        form = StudentForm(instance=instance, initial=initial)

    # Ensure certain fields are required for this view
    form.fields["birth_date"].required = True
    form.fields["grade_level"].required = True

    context = {
        "instance": instance,
        "has_existing_reg": has_existing_reg,
        "club": club,
        "search_term": search_term,
        "matching_students": matching_students,
        "add_new": request.GET.get("add-new") or request.POST.get("add-new"),
        "form": form,
    }

    return render(request, "dashboard/add_student.html", context)


@profile_required
def update_student(request, club_id, student_id):
    club = get_object_or_404(Club, pk=club_id)
    student = get_object_or_404(Student, pk=student_id)
    volunteer = get_profile(request)

    # Verify club access
    try:
        verification.verify_club_access(volunteer, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    student_details_url = reverse(
        "dashboard:student-details",
        kwargs={"club_id": club.id, "student_id": student.id},
    )

    student_reg = StudentRegistration.objects.get(
        club_id=club_id, student_id=student_id
    )

    if request.method == "POST":
        form = StudentForm(request.POST, instance=student)

        if form.is_valid():
            form.save()
            # copy demographic info from student record
            student_reg.set_demographic_info()

            student_reg.save()
            return HttpResponseRedirect(student_details_url)
    else:
        form = StudentForm(instance=student)

    context = {
        "student": student,
        "student_reg": student_reg,
        "registration_status_choices": REGISTRATION_STATUS_CHOICES,
        "form": form,
    }

    return render(request, "dashboard/update_student.html", context)


@profile_required
def all_periods(request):
    return render(request, "dashboard/periods.html", {"periods": get_periods_lookup()})


@profile_required
def manage_announcements(request, club_id):
    club = get_object_or_404(Club, pk=club_id)
    volunteer = get_profile(request)

    # Verify club access
    try:
        verification.verify_club_access(volunteer, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    context = {"club": club, "announcements": club.announcements.all()}
    return render(request, "dashboard/manage_announcements.html", context)


@profile_required
def edit_announcement(request, club_id=None, announcement_id=None):
    club = None
    if club_id:
        club = get_object_or_404(Club, pk=club_id)

    # Verify club access
    volunteer = get_profile(request)
    try:
        verification.verify_club_access(volunteer, club)
    except verification.VerificationFailed:
        raise PermissionDenied

    announcement = None
    if announcement_id:
        announcement = get_object_or_404(Announcement, pk=announcement_id)

    # get available clubs
    # (include clubs that may have just ended -- up to 3 months ago)
    as_of = timezone.now() - relativedelta(months=3)
    clubs = get_volunteer_clubs(request, as_of=as_of).order_by("-start_date")

    if request.method == "POST":
        form = AnnouncementForm(clubs, request.POST, instance=announcement)
        if form.is_valid():
            instance = form.save(commit=False)
            instance.user = request.user
            instance.save()
            form.save_m2m()
            return redirect(
                reverse("dashboard:manage-announcements", kwargs={"club_id": club_id})
            )
    else:
        initial = {}
        if club:
            initial["clubs"] = [club.id]
        form = AnnouncementForm(clubs=clubs, instance=announcement, initial=initial)

    context = {"club": club, "announcement": announcement, "form": form}
    return render(request, "dashboard/edit_announcement.html", context)


@profile_required
def chat(request):
    clubs = get_volunteer_clubs(request).order_by("start_date")
    if clubs.count() == 1:
        return redirect(reverse("chat:main", kwargs={"club_code": clubs[0].club_code}))
    return render(request, "dashboard/chat.html", {"clubs": clubs})


# # KLUDGE: these should really only be in ide.views
# @profile_required
# def student_project(request, student_id, project_slug):
#     student = get_object_or_404(Student, student_id=student_id)
#     volunteer = get_profile(request)
#
#     # Verify student access
#     try:
#         verification.verify_student_access(volunteer, student)
#     except verification.VerificationFailed:
#         raise PermissionDenied
#
#     return ide_views.ide(request, project_slug, username=student.user.canonical_username)


@profile_required
def download_student_project(request, student_id, project_slug):
    student = get_object_or_404(Student, student_id=student_id)
    volunteer = get_profile(request)

    club = None
    if request.GET.get("club_id"):
        club = Club.objects.get(pk=request.GET["club_id"])

    # Verify student access
    try:
        verification.verify_student_access(volunteer, student, club=club)
    except verification.VerificationFailed:
        raise PermissionDenied

    return ide_views.download_project(
        request, project_slug=project_slug, username=student.user.canonical_username
    )


# @profile_required
# def student_project_healthcheck(request, student_id, project_slug):
#     student = get_object_or_404(Student, student_id=student_id)
#     volunteer = get_profile(request)
#
#     # Verify student access
#     try:
#         verification.verify_student_access(volunteer, student)
#     except verification.VerificationFailed:
#         raise PermissionDenied
#
#     return ide_views.healthcheck(request, project_slug, username=student.user.canonical_username)
