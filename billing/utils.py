from collections import namedtuple

# Common model for merchant transactions
Payment = namedtuple(
    "Payment",
    [
        "merchant",
        "ref",
        "date",
        "amount",
        "name",
        "email",
        "invoice_num",
        "item_type",
        "fee",
    ],
)


def currency(val, sign=False):
    # Convert value to currency string to avoid floating point errors
    if sign:
        return "${0:.2f}".format(val)
    return "{0:.2f}".format(val)
