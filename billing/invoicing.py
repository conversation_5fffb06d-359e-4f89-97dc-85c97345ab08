import decimal
import logging
import re
import time

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db import models
from django.utils import timezone
from quickbooks import objects
from quickbooks.exceptions import ObjectNotFoundException

from billing import qb

from . import accounts, exceptions
from .merchants import braintree
from .utils import currency

logger = logging.getLogger(__name__)


def docnum(invoice_number):
    """
    Makes sure the given invoice number is in valid QBO format
    """
    if isinstance(invoice_number, int):
        return str(invoice_number)
    return str(int(re.sub(r"[^0-9]", "", invoice_number)))


def invnum(num):
    """
    Converts invoice number to INV-#### format
    """
    if isinstance(num, str):
        num = docnum(num)
    return "INV-{:04d}".format(int(num))


def create_registration_invoice(registration):
    client = qb.get_client()
    customer = accounts.get_or_create_customer(registration.account)

    # XXX: This is a somewhat kludgy way of getting the last session date,
    # since theoretically someone can be registering for multiple clubs with
    # different dates. It should, however, work for most use cases.
    sessions = (
        registration.student_registrations.annotate(
            start_date=models.Min("club__sessions__date")
        )[0]
        .club.sessions.all()
        .order_by("date")
    )
    if sessions.count() == 0:
        # if there are no sessions, assume due date is immediate
        due_date = timezone.now()
    elif registration.num_payments:
        # if registrant chose multiple payments, due date is last payment date
        due_date = timezone.now() + relativedelta(months=registration.num_payments)
    else:
        # otherwise, due date is one month after first session
        due_date = sessions[0].date + relativedelta(months=1)

    reg_date = registration.date.date().strftime("%Y-%m-%d")
    due_date = due_date.strftime("%Y-%m-%d")

    # build line items
    line_items = []
    subtotal = decimal.Decimal()
    for reg in registration.student_registrations.all():
        line = objects.SalesItemLine()
        line.Description = str(reg)
        line.Amount = currency(reg.club.fee)
        line.DetailType = "SalesItemLineDetail"
        line.SalesItemLineDetail = objects.SalesItemLineDetail()
        line.SalesItemLineDetail.ServiceDate = reg_date
        line.SalesItemLineDetail.ItemRef = objects.Ref()
        line.SalesItemLineDetail.ItemRef.value = settings.PROGRAM_FEE_ITEM_ID
        line_items.append(line)

        subtotal += reg.club.fee

    if registration.discount:
        line = objects.SalesItemLine()
        line.Description = f"Discount: {registration.discount.name}"
        line.Amount = -registration.discount.calculate(subtotal)
        line.SalesItemLineDetail = objects.SalesItemLineDetail()
        line.SalesItemLineDetail.ServiceDate = timezone.now().strftime("%Y-%m-%d")
        line.SalesItemLineDetail.ItemRef = objects.Ref()
        line.SalesItemLineDetail.ItemRef.value = settings.PROGRAM_FEE_DISCOUNT_ITEM_ID
        line_items.append(line)

    invoice = objects.Invoice()
    invoice.TxnDate = reg_date
    invoice.Line = line_items
    invoice.DueDate = due_date
    invoice.CustomerRef = objects.Ref()
    invoice.CustomerRef.value = customer.Id
    invoice.AllowOnlineCreditCardPayment = True
    invoice.AllowOnlineACHPayment = True
    try:
        invoice = invoice.save(qb=client)
    except Exception:
        time.sleep(1)
        invoice.delete(qb=client)
        raise

    # Validate that a DocNumber was generated correctly
    try:
        invoice_num = int(invoice.DocNumber)
    except (ValueError, TypeError):
        invoice_num = None

    if invoice_num is None:
        time.sleep(1)
        invoice.delete(qb=client)
        raise exceptions.InvoiceNumberingError(
            "Quickbooks did not generate a valid invoice number. "
            "Please see the following URL for more info:\n"
            "https://community.intuit.com/questions/1332271"
        )

    registration.invoice_number = str(invoice_num)
    registration.save()

    return invoice


def add_discount(invoice_number, amount, description=None):
    """
    Adds a discount to an invoice
    """
    client = qb.get_client()
    if description is None:
        description = "Discount"
    invoice = get_invoice(invoice_number)
    if invoice is None:
        raise ValueError("Invoice {} was not found".format(invoice_number))
    line = objects.SalesItemLine()
    line.Description = description
    line.Amount = currency(-amount)
    line.SalesItemLineDetail = objects.SalesItemLineDetail()
    line.SalesItemLineDetail.ServiceDate = timezone.now().strftime("%Y-%m-%d")
    line.SalesItemLineDetail.ItemRef = objects.Ref()
    line.SalesItemLineDetail.ItemRef.value = settings.PROGRAM_FEE_DISCOUNT_ITEM_ID
    invoice.Line.append(line)
    try:
        invoice.save(qb=client)
    except Exception:
        time.sleep(1)
        invoice.delete(qb=client)
        raise


def get_invoices(qb_contact_id):
    client = qb.get_client()
    return objects.Invoice.filter(
        CustomerRef=str(qb_contact_id), order_by="TxnDate", qb=client
    )


def get_invoice(invoice_number):
    invoice_number = docnum(invoice_number)
    client = qb.get_client()
    results = objects.Invoice.filter(DocNumber=invoice_number, qb=client)
    if len(results) == 0:
        return None
    return results[0]


def get_invoice_by_id(invoice_id):
    client = qb.get_client()
    try:
        return objects.Invoice.get(invoice_id, qb=client)
    except ObjectNotFoundException:
        return None


def get_invoice_url(invoice_number):
    invoice = get_invoice(invoice_number)
    if invoice is None:
        return None
    return settings.INVOICE_URL.format(id=invoice.Id)


def get_invoice_pdf(invoice_id):
    client = qb.get_client()
    invoice = get_invoice_by_id(invoice_id)
    return invoice.download_pdf(qb=client)


def void_invoice(invoice_number):
    client = qb.get_client()
    invoice = get_invoice(invoice_number)
    return invoice.void(qb=client)


def submit_payment(request, account, invoice, amount):
    order_id = invnum(invoice.DocNumber)
    return braintree.submit_invoice_payment(request, account, order_id, amount)
