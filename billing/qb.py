import base64
import calendar
import json
import logging
import mimetypes
import os
import time

from Crypto.Cipher import AES
from django.conf import settings
from django.utils import timezone
from intuitlib.client import AuthClient
from intuitlib.enums import Scopes  # noqa
from intuitlib.exceptions import AuthClientError  # noqa
from quickbooks import QuickBooks, objects
from quickbooks.exceptions import AuthorizationException

from billing.models import QBRefreshToken
from hive.utils import decrypt_bytes, encrypt_bytes

logger = logging.getLogger(__name__)

QuickBooks.disable_global()


def dec(val):
    return "{0:.2f}".format(val)


def get_current_timestamp():
    return calendar.timegm(timezone.now().utctimetuple())


def show_timestamp(ts):
    utc_time = time.gmtime(ts)
    local_time = time.localtime(ts)
    print(time.strftime("%Y-%m-%d %H:%M:%S", local_time))
    print(time.strftime("%Y-%m-%d %H:%M:%S+00:00 (UTC)", utc_time))


def save_reimbursement_entry(claim):
    client = get_client()

    employee_id = claim.user.extra_data["qb_employee_id"]
    credit_account = settings.EMPLOYEE_REIMBURSEMENT_ACCOUNT_ID

    if claim.journal_id:
        entry = objects.JournalEntry.get(claim.journal_id)
    else:
        entry = objects.JournalEntry()

    # Credit the employee reimbursement account
    cr_line = objects.JournalEntryLine()
    cr_line.Amount = str(claim.amount)
    cr_line.Description = claim.description
    cr_line.DetailType = "JournalEntryLineDetail"
    cr_line.JournalEntryLineDetail = objects.JournalEntryLineDetail()
    cr_line.JournalEntryLineDetail.PostingType = "Credit"
    cr_line.JournalEntryLineDetail.AccountRef = objects.Ref()
    cr_line.JournalEntryLineDetail.AccountRef.value = credit_account
    cr_line.JournalEntryLineDetail.ClassRef = objects.Ref()
    cr_line.JournalEntryLineDetail.ClassRef.value = claim.class_id
    cr_line.JournalEntryLineDetail.Entity = objects.Entity()
    cr_line.JournalEntryLineDetail.Entity.Type = "Employee"
    cr_line.JournalEntryLineDetail.Entity.EntityRef = objects.Ref()
    cr_line.JournalEntryLineDetail.Entity.EntityRef.value = employee_id

    # Debit expense account
    dr_line = objects.JournalEntryLine()
    dr_line.Amount = str(claim.amount)
    dr_line.Description = claim.description
    dr_line.DetailType = "JournalEntryLineDetail"
    dr_line.JournalEntryLineDetail = objects.JournalEntryLineDetail()
    dr_line.JournalEntryLineDetail.PostingType = "Debit"
    dr_line.JournalEntryLineDetail.AccountRef = objects.Ref()
    dr_line.JournalEntryLineDetail.AccountRef.value = claim.account_id
    dr_line.JournalEntryLineDetail.ClassRef = objects.Ref()
    dr_line.JournalEntryLineDetail.ClassRef.value = claim.class_id

    entry.Line = [cr_line, dr_line]

    saved_entry = entry.save(qb=client)

    # Add receipt attachment
    if claim.receipt:
        attachment_ref = objects.AttachableRef()
        attachment_ref.EntityRef = objects.Ref()
        attachment_ref.EntityRef.type = "JournalEntry"
        attachment_ref.EntityRef.value = saved_entry.Id
        attachment = objects.Attachable()
        attachment.AttachableRef.append(attachment_ref)
        attachment.FileName = os.path.basename(claim.receipt.path)
        attachment.ContenType = mimetypes.guess_type(claim.receipt.name)[0]
        attachment._FilePath = claim.receipt.path
        attachment.save(qb=client)

    return saved_entry


class AuthError(Exception):
    def __init__(self, msg, original_error=None):
        self.original_error = original_error
        super().__init__(str(msg))


def get_auth_client(state_token=None):
    return AuthClient(
        client_id=settings.QUICKBOOKS_CLIENT_ID,
        client_secret=settings.QUICKBOOKS_CLIENT_SECRET,
        redirect_uri=settings.QUICKBOOKS_REDIRECT_URI,
        environment=settings.QUICKBOOKS_ENVIRONMENT,
        state_token=state_token,
    )


def get_refresh_token():
    refresh_token = QBRefreshToken.objects.first()
    if refresh_token is None:
        return None

    if refresh_token.encrypted_value:
        return decrypt_bytes(refresh_token.encrypted_value).decode()

    return None


def save_refresh_token(value):
    QBRefreshToken.objects.all().delete()
    encrypted_value = encrypt_bytes(value.encode())
    QBRefreshToken.objects.create(encrypted_value=encrypted_value)


def get_client():
    auth_client = get_auth_client()
    refresh_token = get_refresh_token()

    if not refresh_token:
        raise AuthError("No refresh token")

    try:
        return QuickBooks(
            refresh_token=refresh_token,
            auth_client=auth_client,
            company_id=settings.QUICKBOOKS_COMPANY_ID,
        )
    except AuthClientError as e:
        if hasattr(e, "content"):
            error_data = json.loads(e.content)
            if error_data["error"] == "invalid_grant":
                raise AuthError("Invalid Grant", e)
        raise AuthError(e)


def save_session(auth_code, realm_id):
    auth_client = get_auth_client()
    auth_client.get_bearer_token(auth_code, realm_id)
    save_refresh_token(auth_client.refresh_token)


def delete_session():
    QBRefreshToken.objects.all().delete()


def refresh_session():
    logger.info("Refreshing QuickBooks auth token")
    auth_client = get_auth_client()

    # Get latest refresh token
    refresh_token = get_refresh_token()
    auth_client.refresh(refresh_token=refresh_token)

    # Save the returned refresh token for subsequent calls
    save_refresh_token(auth_client.refresh_token)

    logger.info("Successfully refreshed QuickBooks auth token")


def get_company_info():
    client = get_client()
    try:
        return objects.CompanyInfo.get(settings.QUICKBOOKS_COMPANY_ID, qb=client)
    except AuthorizationException as e:
        raise AuthError(e)


def is_connected():
    try:
        get_company_info()
    except AuthError:
        return False
    else:
        return True
