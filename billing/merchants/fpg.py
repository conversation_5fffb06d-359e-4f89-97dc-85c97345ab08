"""
First Pay Gateway REST client
"""

import logging

from django.conf import settings
from firstpaygateway.client import Client

from billing.utils import Payment

logger = logging.getLogger(__name__)


def get_payments(start_date, end_date):
    msg = "Finding 1stpaygateway transactions between {:%-m/%-d/%Y} and {:%-m/%-d/%Y}"
    logger.info(msg.format(start_date, end_date))

    client = Client(
        merchant_key=settings.FPG_MERCHANT_KEY, processor_id=settings.FPG_PROCSESOR_ID
    )

    # find all settled transactions
    settled_result = client.query(
        query_trans_type="Settle",
        query_trans_status="Approved",
        start_date=start_date,
        end_date=end_date,
    )

    for tx in settled_result.data.orders:
        ref = str(tx.transaction_id)
        date = tx.order_info.trans_date_and_time.date()
        amount = tx.order_info.amount
        name = tx.cc_info.name_on_card
        email = tx.billing_info.email_address
        item_type = "MONTHLY_DONATION"  # fpg now only has monthly recurring donations.

        yield Payment("FPG", ref, date, amount, name, email, None, item_type, None)
