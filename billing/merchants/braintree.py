import logging
from decimal import Decimal

import braintree

from billing.exceptions import BillingError
from billing.utils import Payment
from clubs.models import Registration

internal_logger = logging.getLogger(__name__)

# create hasher for non-predicatble unique account ids
# from hashids import Hashids
# id_hasher = Hashids(settings.SECRET_KEY, min_length=8)


# FIXME Using REG-#### with the registration ID can lead to issues when the subscription id
# (order_id) in braintree needs to change to something else. We ran into an issue when we had to
# change someone's card, so we had to cancel the subscription and re-create a new one, but the
# subscription id could not be re-used.


def get_payments(start_date, end_date, logger=None):
    # According to braintree API docs, `between()` is inclusive, but only to the minute
    if logger is None:
        logger = internal_logger
    logger.info(
        "Finding braintree transactions between {:%-m/%-d/%Y} and {:%-m/%-d/%Y}".format(
            start_date, end_date
        )
    )
    transactions = braintree.Transaction.search(
        braintree.TransactionSearch.created_at.between(
            start_date.strftime("%-m/%-d/%Y 00:00"),
            end_date.strftime("%-m/%-d/%Y 23:59"),
        )
    )

    for transaction in transactions:
        # only process settled transactions
        if transaction.status != "settled":
            continue

        # only process sale transactions (credits/refunds are handled manually for now)
        if transaction.type != "sale" or transaction.refund_id:
            continue

        ref = str(transaction.id)
        date = transaction.created_at.date()
        amount = transaction.amount
        name = "{0.first_name} {0.last_name}".format(transaction.customer_details)
        email = transaction.customer_details.email

        invoice_num = None
        order_id = transaction.order_id or transaction.subscription_id or None

        # Order id's can start with INV-#### or REG-####
        # Note: registrations since Spring '19 should all start with REG-####
        if order_id and order_id.upper().startswith("INV-"):
            invoice_num = order_id.upper().replace("INV-", "")
            invoice_num = str(int(invoice_num))
        elif order_id and order_id.upper().startswith("REG-"):
            # Find matching registration and see if an invoice has been associated with it
            reg_id = order_id.upper().replace("REG-", "")
            try:
                reg_id = int(reg_id)
            except ValueError:
                logger.warn(
                    f"Skipped registration transaction with invalid order id: {reg_id}"
                )
                continue
            try:
                registration = Registration.objects.get(pk=reg_id)
            except Registration.DoesNotExist:
                pass
            else:
                invoice_num = registration.invoice_number

        item_type = None
        source = (
            transaction.custom_fields
            and transaction.custom_fields.get("source")
            or None
        )
        if source and source == "registration":
            item_type = transaction.recurring and "MONTHLY_PROGRAM_FEE" or "PROGRAM_FEE"
        elif transaction.plan_id and transaction.plan_id.startswith("PROGRAM_FEE"):
            item_type = "MONTHLY_PROGRAM_FEE"
        elif transaction.plan_id == "MONTHLY_DONATION":
            item_type = "MONTHLY_DONATION"
        elif source and source == "donation":
            item_type = "DONATION"
        else:
            item_type = None

        yield Payment(
            "BRAINTREE", ref, date, amount, name, email, invoice_num, item_type, None
        )


def _get_transactions_by_order_id(order_id):
    try:
        subscription = braintree.Subscription.find(order_id)
    except braintree.exceptions.NotFoundError:
        transactions = braintree.Transaction.search(
            braintree.TransactionSearch.order_id == order_id
        )
    else:
        transactions = subscription.transactions

    for transaction in transactions:
        if transaction.status == "settled":
            yield transaction


def _customer_id(account):
    return "ACCT-{}".format(account.id)


def _registration_order_id(registration):
    if registration.invoice_number:
        if not registration.invoice_number.startswith("INV-"):
            return "INV-{}".format(registration.invoice_number)
        return registration.invoice_number
    return "REG-{}".format(registration.id)


def process_registration_payment(request, registration, amount=None):
    """
    Processes the payment for a registration and sets recurring if applicable.
    The payment_method_nonce must exist in the request POST data.

    One-time payments can be made by passing an amount.
    """
    account = registration.account
    contact = account.default_contact

    # get or create the client vault record
    try:
        customer = braintree.Customer.find(_customer_id(account))
    except braintree.exceptions.NotFoundError:
        customer_result = braintree.Customer.create(
            {
                "id": _customer_id(account),
                "first_name": contact.first_name,
                "last_name": contact.last_name,
                "email": contact.email,
                "custom_fields": {"source": "registration"},
            }
        )
        if not customer_result.is_success:
            raise BillingError(customer_result.message)
        customer = customer_result.customer

    # Get or create the payment method
    if request.POST.get("payment_method_nonce"):
        address = request.POST.get("billing_address1")
        if request.POST.get("billing_address2"):
            address += "\n" + request.POST.get("billing_address2")
        payment_method_result = braintree.PaymentMethod.create(
            {
                "customer_id": customer.id,
                "payment_method_nonce": request.POST["payment_method_nonce"],
                "billing_address": {
                    "street_address": address,
                    "locality": request.POST.get("billing_city"),
                    "region": request.POST.get("billing_state"),
                    "postal_code": request.POST.get("billing_zip"),
                },
            }
        )
        if not payment_method_result.is_success:
            raise BillingError(payment_method_result.message)
        payment_method_token = payment_method_result.payment_method.token
    elif request.POST.get("payment_method_token"):
        payment_method_token = request.POST["payment_method_token"]
    else:
        raise ValueError("Payment method token or nonce was not provided")

    if not amount and registration.payment_frequency == "ONE_TIME":
        amount = registration.invoice_total

    transaction = None

    if amount:
        # Force 2-digit precision in decimal amount
        amount = Decimal(amount) + Decimal("0.00")
        result = braintree.Transaction.sale(
            {
                "payment_method_token": payment_method_token,
                "amount": amount,
                "order_id": _registration_order_id(registration),
                "options": {"submit_for_settlement": True},
            }
        )
        if not result.is_success:
            raise BillingError(result.message)
        transaction = result.transaction

    elif registration.payment_frequency == "MONTHLY":
        num_payments = registration.num_payments or 1
        amount = registration.invoice_total / num_payments
        # Force 2-digit precision in decimal amount
        amount = Decimal(amount) + Decimal("0.00")
        plan_id = "PROGRAM_FEE_MONTHLY_{}".format(num_payments)

        result = braintree.Subscription.create(
            {
                "payment_method_token": payment_method_token,
                "plan_id": plan_id,
                "price": amount,
                "id": _registration_order_id(registration),
            }
        )

        if not result.is_success:
            raise BillingError(result.message)

        if len(result.subscription.transactions) > 0:
            transaction = result.subscription.transactions[0]

    else:
        raise ValueError("Amount or payment frequency not provided")

    return transaction


def get_payment_methods(account):
    try:
        customer = braintree.Customer.find(_customer_id(account))
    except braintree.exceptions.NotFoundError:
        return []
    return customer.payment_methods


def cancel_transaction(transaction):
    # voids a recently submitted transaction and any associated subscriptions
    void_result = transaction.void(transaction.id)
    if not void_result.is_success:
        raise BillingError(void_result.message)
    if transaction.subscription_id:
        subscription = braintree.Subscription.find(transaction.subscription_id)
        cancel_result = subscription.cancel(subscription.id)
        if not cancel_result.is_success:
            raise BillingError(cancel_result.message)


def submit_invoice_payment(request, account, order_id, amount):
    # get or create the client vault record
    contact = account.default_contact
    try:
        customer = braintree.Customer.find(_customer_id(account))
    except braintree.exceptions.NotFoundError:
        customer_result = braintree.Customer.create(
            {
                "id": _customer_id(account),
                "first_name": contact.first_name,
                "last_name": contact.last_name,
                "email": contact.email,
                "custom_fields": {"source": "invoice"},
            }
        )
        if not customer_result.is_success:
            raise BillingError(customer_result.message)
        customer = customer_result.customer

    # Get or create the payment method
    if request.POST.get("payment_method_nonce"):
        address = request.POST.get("billing_address1")
        if request.POST.get("billing_address2"):
            address += "\n" + request.POST.get("billing_address2")
        payment_method_result = braintree.PaymentMethod.create(
            {
                "customer_id": customer.id,
                "payment_method_nonce": request.POST["payment_method_nonce"],
                "billing_address": {
                    "street_address": address,
                    "locality": request.POST.get("billing_city"),
                    "region": request.POST.get("billing_state"),
                    "postal_code": request.POST.get("billing_zip"),
                },
            }
        )
        if not payment_method_result.is_success:
            raise BillingError(payment_method_result.message)
        payment_method_token = payment_method_result.payment_method.token
    elif request.POST.get("payment_method_token"):
        payment_method_token = request.POST["payment_method_token"]
    else:
        raise ValueError("Payment method token or nonce was not provided")

    transaction = None

    # Force 2-digit precision in decimal amount
    amount = Decimal(amount) + Decimal("0.00")
    result = braintree.Transaction.sale(
        {
            "payment_method_token": payment_method_token,
            "amount": amount,
            "order_id": order_id,
            "options": {"submit_for_settlement": True},
        }
    )
    if not result.is_success:
        raise BillingError(result.message)
    transaction = result.transaction

    return transaction
