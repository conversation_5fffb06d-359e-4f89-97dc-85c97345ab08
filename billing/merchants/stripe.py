import logging
import math
import re
import time
from datetime import date
from decimal import Decimal

import stripe
from dateutil.parser import parse as parse_date
from django.conf import settings

from billing.utils import Payment

stripe.api_key = settings.STRIPE_API_KEY

logger = logging.getLogger(__name__)


def cents_to_dec(val):
    val = Decimal(val) / 100
    # round to two places
    twoplaces = Decimal(10) ** -2
    return val.quantize(twoplaces)


def get_payments(start_date, end_date):
    logger.info(
        "Finding stripe transactions between {:%-m/%-d/%Y} and {:%-m/%-d/%Y}".format(
            start_date, end_date
        )
    )
    date_filter = {
        "gte": math.floor(time.mktime(start_date.timetuple())),
        "lte": math.floor(time.mktime(end_date.timetuple())),
    }

    charges = stripe.Charge.list(created=date_filter, limit=100)

    for charge in charges["data"]:
        if not charge.paid:
            continue
        # get the fee amount from the balance transaction
        tx = stripe.BalanceTransaction.retrieve(charge.balance_transaction)

        ref = charge.id
        tx_date = date.fromtimestamp(charge.created)
        amount = cents_to_dec(charge.amount)
        fee = cents_to_dec(tx.fee)
        metadata = charge.metadata
        name = None
        email = None
        if hasattr(metadata, "donorbox_first_name"):
            first_name = metadata.donorbox_first_name
            last_name = metadata.donorbox_last_name
            name = first_name + " " + last_name
            email = metadata.donorbox_email

        elif hasattr(metadata, "email"):
            email = metadata.email

        # Squarespace only puts the email in the description preceeded by "Charge for ..."
        if not email and charge.description.startswith("Charge for "):
            email = charge.description.replace("Charge for ", "")

        if "billing_details" in charge:
            name = charge["billing_details"].get("name")

        # try looking for "Contribution by..."
        if not name:
            name = re.sub(
                r"^.*? Contribution by (.*?) to .*$", r"\1", charge.description
            )

        if not name:
            name = email

        item_type = (
            "MONTHLY_DONATION"  # stripe now only has monthly recurring donations.
        )
        yield Payment("STRIPE", ref, tx_date, amount, name, email, None, item_type, fee)
