import logging
import threading
import urllib
from datetime import timedelta

import jwt
from django import http
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin.templatetags.admin_urls import add_preserved_filters
from django.db.models import Count, Q
from django.shortcuts import get_object_or_404, render
from django.template.defaultfilters import date as date_format
from django.urls import reverse
from django.utils import timezone
from django.utils.safestring import mark_safe

from billing import processing
from hive.admin import register, register_appview

from . import models, qb

logger = logging.getLogger(__name__)


@register(models.SyncEvent)
class SyncEventAdmin(admin.ModelAdmin):
    list_display = ["date", "type", "_range", "item_count", "_result"]
    list_filter = ["type"]
    readonly_fields = ["date", "type", "_range"]
    ordering = ["-date"]

    # FIXME: This doesn't show timezone-aware dates in the filter, breaking the filter.
    # https://stackoverflow.com/questions/72261116
    date_hierarchy = "date"

    def has_add_permission(self, request, obj=None):
        return True

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ["date", "type", "_range"]
        return []

    def get_changeform_initial_data(self, request):
        now = timezone.localtime(timezone.now())
        start_date = now - timedelta(days=14)
        end_date = now
        return {
            "range_start": start_date.replace(hour=0, minute=0, second=0),
            "range_end": end_date,
        }

    def get_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields
        return ["type", "range_start", "range_end"]

    def get_urls(self):
        from django.conf.urls import url

        return [
            url(
                "^syncevent/(?P<object_id>[^/]+)/log_entries/$",
                self.admin_site.admin_view(self.get_log_entries),
                name="billing_syncevent_log_entries",
            )
        ] + super().get_urls()

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.annotate(
            num_log_entries=Count("log_entries"),
            num_warnings=Count("log_entries", filter=Q(log_entries__level="WARN")),
            num_errors=Count("log_entries", filter=Q(log_entries__level="ERROR")),
        )
        return qs

    def _response_add_change(self, request, obj, **kwargs):
        opts = obj._meta
        obj_url = reverse(
            "admin:billing_syncevent_change",
            args=[obj.pk],
            current_app=self.admin_site.name,
        )
        msg = "Snyc started. See log messages below."
        self.message_user(request, msg, messages.INFO)
        post_url_continue = obj_url
        preserved_filters = self.get_preserved_filters(request)
        post_url_continue = add_preserved_filters(
            {"preserved_filters": preserved_filters, "opts": opts}, post_url_continue
        )
        return http.HttpResponseRedirect(post_url_continue)

    def response_add(self, request, obj, **kwargs):
        return self._response_add_change(request, obj, **kwargs)

    def response_change(self, request, obj, **kwargs):
        return self._response_add_change(request, obj, **kwargs)

    def _range(self, obj):
        start = date_format(obj.range_start, "DATE_FORMAT")
        end = date_format(obj.range_end, "DATE_FORMAT")
        return f"{start} - {end}"

    _range.short_description = "Date range"

    def _result(self, obj):
        if obj.sync_state != "COMPLETED":
            return ""
        if obj.num_errors > 0:
            desc = obj.num_errors == 1 and "1 error" or f"{obj.num_errors} errors"
            return mark_safe(
                f'<i class="icon-error fas fa-times-circle" title="{desc}"></i>'
            )
        elif obj.num_warnings > 0:
            desc = (
                obj.num_warnings == 1 and "1 warning" or f"{obj.num_warnings} warnings"
            )
            return mark_safe(
                f'<i class="icon-warn fas fa-exclamation-triangle" title="{desc}"></i>'
            )
        else:
            return mark_safe(
                '<i class="icon-success fas fa-check" title="no errors or warnings"></i>'
            )

    _result.short_description = "Result"

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id:
            title = "Billing sync event"
        else:
            title = "Sync billing"
        extra_context = extra_context or {}
        extra_context.update({"title": title})
        # Overridden to make changeform_view() non-atomic, so that spawned thread within
        # save_model() has guaranteed access to committed object
        return self._changeform_view(request, object_id, form_url, extra_context)

    def save_model(self, request, obj, form, change):
        def start_sync(event_id):
            logger.info(f"Starting background sync for event_id {event_id}")
            event = models.SyncEvent.objects.get(pk=event_id)

            # Start sync in background
            if event.type == "PAYMENTS":
                _process = processing.process_payments
            elif event.type == "REGISTRATION_INVOICES":
                _process = processing.process_registrations
            elif event.type == "TEST":
                _process = processing.test_process
            else:
                raise ValueError(f"Unknown event type: {event.type}")

            _process(event)

        obj.save()

        if obj.sync_state == "NOT_STARTED":
            obj.sync_state = "STARTED"
            obj.save()

            # FIXME: Use a more sophisticated approach, eg: celery, or SSE w/ django-channels
            thread = threading.Thread(target=start_sync, args=(obj.pk,))
            thread.setDaemon(True)
            thread.start()

    def get_log_entries(self, request, object_id):
        event = get_object_or_404(models.SyncEvent, pk=object_id)
        last_id = request.GET.get("last_id", 0)
        entries = event.log_entries.order_by("date").filter(pk__gt=last_id)
        data = {"sync_state": event.sync_state, "log_entries": []}
        for entry in entries:
            data["log_entries"].append(
                {
                    "id": entry.id,
                    "date": str(entry.date),
                    "level": entry.level,
                    "message": entry.message,
                }
            )

        return http.JsonResponse(data)

    def changelist_view(self, request, extra_context=None):
        extra_context = {}
        extra_context.update(
            get_qbauth_context(
                request, redirect_url=reverse("admin:billing_syncevent_changelist")
            )
        )
        return super().changelist_view(request, extra_context)

    def get_changelist_instance(self, request):
        cl = super().get_changelist_instance(request)
        cl.title = "Billing sync events"
        # Force has_filters style so we can show qb connection in sidebar
        cl.has_other_filters = cl.has_filters
        cl.has_filters = True
        return cl


def get_qbauth_context(request, redirect_url=None):
    is_connected = False
    state_token = None
    error_info = None

    if redirect_url:
        # Use a JWT to store "next" in state token
        payload = {"next": redirect_url}
        state_token = jwt.encode(payload, settings.SECRET_KEY)

    auth_client = qb.get_auth_client(state_token=state_token)

    # Test the connection
    try:
        company_info = qb.get_company_info()
    except qb.AuthError as e:
        company_info = None
        is_connected = False
        error_info = str(e)
    else:
        is_connected = True

    disconnect_url = reverse("admin:billing_qbauth") + "?disconnect=1"
    if redirect_url:
        disconnect_url += f"&next={redirect_url}"

    return {
        "authorize_url": auth_client.get_authorization_url([qb.Scopes.ACCOUNTING]),
        "disconnect_url": disconnect_url,
        "connected": is_connected,
        "company_info": company_info,
        "error_info": error_info,
    }


@register_appview("billing", "qbauth/$", "qbauth", "Quickbooks Connection", "Billing")
def qbauth(request, *args, **kwargs):
    if request.GET.get("code"):
        next = None
        if request.GET.get("state"):
            # If state is a JWT, extract "next" url from it and redirect
            try:
                state = jwt.decode(request.GET["state"], settings.SECRET_KEY)
                if state.get("next"):
                    next = urllib.parse.unquote(state.get("next"))
            except jwt.DecodeError:
                pass

        qb.save_session(request.GET["code"], request.GET["realmId"])
        next = next or "."
        return http.HttpResponseRedirect(next)

    if request.GET.get("disconnect") == "1":
        qb.delete_session()
        next = request.GET.get("next", ".")
        return http.HttpResponseRedirect(next)

    context = get_qbauth_context(request)

    return render(request, "admin/billing/qbauth.html", context)
