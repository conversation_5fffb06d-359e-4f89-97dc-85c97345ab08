import re

from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.safestring import mark_safe

from clubs.models import Registration

SYNC_EVENT_TYPES = (
    ("REGISTRATION_INVOICES", "Registration invoices"),
    ("PAYMENTS", "Payments"),
)

if settings.DEBUG:
    SYNC_EVENT_TYPES += (("TEST", "Test"),)

SYNC_STATE_TYPES = (
    ("NOT_STARTED", "Not started"),
    ("STARTED", "Started"),
    ("COMPLETED", "Completed"),
)

LOG_LEVELS = (
    ("DEBUG", "debug"),
    ("INFO", "info"),
    ("WARN", "warning"),
    ("ERROR", "error"),
)


class SyncEvent(models.Model):
    date = models.DateTimeField(default=timezone.now)
    type = models.CharField(max_length=64, choices=SYNC_EVENT_TYPES)
    range_start = models.DateTimeField()
    range_end = models.DateTimeField()
    sync_state = models.CharField(
        max_length=32, choices=SYNC_STATE_TYPES, default="NOT_STARTED"
    )
    item_count = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.get_type_display()} {self.date}"

    @classmethod
    def get_admin_verbose_name(self):
        return "Sync activity"

    class Meta:
        ordering = ["-date"]


class SyncEventLogEntry(models.Model):
    date = models.DateTimeField(default=timezone.now)
    event = models.ForeignKey(
        SyncEvent, related_name="log_entries", on_delete=models.CASCADE
    )
    level = models.CharField(max_length=32, choices=LOG_LEVELS)
    message = models.TextField()
    object_id = models.PositiveIntegerField(blank=True, null=True)
    object_type = models.ForeignKey(
        ContentType, on_delete=models.SET_NULL, blank=True, null=True
    )
    object = GenericForeignKey("object_type", "object_id")

    def __str__(self):
        return f"{self.date} [{self.level}] {self.message}"

    class Meta:
        ordering = ["date"]

    @property
    def message_autolinked(self):
        message = self.message

        # Autolink transaction ids to braintree
        merchant_id = settings.BRAINTREE_MERCHANT_ID
        tx_url = (
            f"https://www.braintreegateway.com/merchants/{merchant_id}/transactions"
        )
        message = re.sub(
            r"((transaction|payment) #([a-zA-Z0-9]+))",
            rf'<a href="{tx_url}/\3" target="_blank">\1</a>',
            message,
        )

        # Autolink registrations
        if self.object and isinstance(self.object, Registration):
            reg_url = reverse(
                "admin:clubs_registration_change", kwargs={"object_id": self.object.id}
            )
            message = re.sub(
                r"(registration #[0-9]+)", rf'<a href="{reg_url}">\1</a>', message
            )

        return mark_safe(message)


class QBRefreshToken(models.Model):
    date_modified = models.DateTimeField(default=timezone.now)
    encrypted_value = models.BinaryField()

    def save(self, *args, **kwargs):
        self.date_modified = timezone.now()
        super().save(*args, **kwargs)
