import logging
from datetime import datetime, timedelta

from dateutil.parser import parse as parse_date
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone

from billing import qb
from billing.models import SyncEvent
from billing.processing import process_registrations

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Create invoices in QBO for new registrations"

    def add_arguments(self, parser):
        parser.add_argument(
            "-d",
            "--days",
            type=int,
            dest="days",
            default=14,
            help="number of days ago to process",
        )
        parser.add_argument(
            "--start-date", type=str, dest="start_date", help="start date"
        )
        parser.add_argument("--end-date", type=str, dest="end_date", help="end date")

    def handle(self, *args, **options):
        # Override some logging options
        root_logger = logging.getLogger("")
        root_logger.setLevel(logging.INFO)
        start_date = None
        end_date = None
        today = timezone.now().date()
        if options.get("start_date"):
            start_date = parse_date(options["start_date"]).date()
            if options.get("end_date"):
                end_date = parse_date(options["end_date"]).date()
            else:
                end_date = today
        elif options.get("days"):
            start_date = today - timedelta(days=options["days"])
            end_date = today

        if start_date is None or end_date is None:
            raise CommandError("Invalid options")

        start_date = datetime.combine(start_date, datetime.min.time())
        end_date = datetime.combine(end_date, datetime.max.time())

        start_date = timezone.make_aware(start_date)
        end_date = timezone.make_aware(end_date)

        # don't auto-process if not connected
        if not qb.is_connected():
            raise CommandError(
                "Quickbooks is not connected. Please connect quickbooks in the Hive admin portal."
            )

        event = SyncEvent.objects.create(
            type="REGISTRATION_INVOICES", range_start=start_date, range_end=end_date
        )
        process_registrations(event)

        # Don't save event if nothing was sync'd and there are no warnings/errors
        num_issues = event.log_entries.filter(level__in=("WARN", "ERROR")).count()
        if event.item_count == 0 and num_issues == 0:
            event.delete()
