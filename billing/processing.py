import collections
import itertools
import logging
import re
import time
from datetime import <PERSON><PERSON><PERSON>

from django.conf import settings
from django.utils import timezone
from quickbooks import batch, objects

from billing import invoicing, qb
from billing.exceptions import BillingError
from billing.merchants import braintree  # , stripe
from billing.models import LOG_LEVELS as SYNC_EVENT_LOG_LEVELS
from billing.models import SyncEventLogEntry
from clubs.models import Registration

internal_logger = logging.getLogger(__name__)

RFC_822_RE = re.compile(
    r"[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)"
    r"*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?"
)


# NOTE: when logging messages using SyncEventLogger, the log level has special meaning:
#
#   error: An error occurred that stopped any further processing
#   warning: An error or warning occurred, but did not stop further processing
#   info: Useful info or success


class SyncEventLogger:
    def __init__(self, instance):
        self.event = instance

    def update_event_state(self, state):
        self.event.sync_state = state
        self.event.save()

    def save_event(self):
        self.event.save()
        return self.event

    def log(self, level, msg, object=None):
        # log to internal logger
        internal_log = getattr(internal_logger, level)
        internal_log(msg)

        level = level.upper()

        if level == "DEBUG" and not settings.DEBUG:
            return

        if not self.event.pk:
            self.save_event()

        if level in [lev[0] for lev in SYNC_EVENT_LOG_LEVELS]:
            # log to database
            entry = SyncEventLogEntry.objects.create(
                event=self.event, level=level, message=msg, object=object
            )
            return entry

    def info(self, *args, **kwargs):
        return self.log("info", *args, **kwargs)

    def warn(self, *args, **kwargs):
        return self.log("warn", *args, **kwargs)

    def error(self, *args, **kwargs):
        return self.log("error", *args, **kwargs)

    def debug(self, *args, **kwargs):
        return self.log("debug", *args, **kwargs)


def batch_create(items, type, logger):
    client = qb.get_client()

    if len(items) > 0:
        yield logger.info("Running batch create for {} {}s...".format(len(items), type))
    try:
        result = batch.batch_create(items, qb=client)
    except Exception as e:
        yield logger.error(e.detail)
        raise

    for obj in result.successes:
        customer_name = obj.CustomerRef.name
        yield logger.info(
            "  Added ${} {} for customer {}".format(obj.TotalAmt, type, customer_name)
        )
        logger.event.item_count += 1

    for fault in result.faults:
        obj = fault.original_object
        if obj.CustomerRef:
            customer_name = obj.CustomerRef.name
        else:
            customer_name = "???"
        yield logger.warn(
            "  Error while adding ${} {} for customer {}: {}".format(
                obj.TotalAmt, type, customer_name, fault.Error[0].Message
            )
        )


# NOTE: The process_* functions were originally written as a generator that yields log entries in
# the hopes we could have a realtime view of the entires in the admin (eg, using SSE) without
# having to poll.


def process_registrations_iter(event):
    start_date = event.range_start
    end_date = event.range_end

    logger = SyncEventLogger(event)

    logger.update_event_state("STARTED")

    if not qb.is_connected():
        raise BillingError(
            "Quickbooks is not connected. Please connect quickbooks in the Hive admin portal."
        )

    try:
        registrations = Registration.objects.filter(date__range=(start_date, end_date))

        for registration in registrations.order_by("date"):
            if registration.invoice_total == 0:
                continue
            if not registration.invoice_number:
                yield logger.info(
                    "Creating invoice for registration {}...".format(registration),
                    object=registration,
                )
                try:
                    invoice = invoicing.create_registration_invoice(registration)
                except Exception as e:
                    yield logger.error(
                        f"  an error occurred while creating invoice: {e}"
                    )
                    raise
                else:
                    yield logger.info(
                        "  created invoice #{}".format(invoice.DocNumber),
                        object=registration,
                    )
                    logger.event.item_count += 1
                time.sleep(1)

        if registrations.count() == 0:
            yield logger.info("No registrations found within date range")
        elif logger.event.item_count == 0:
            yield logger.info("No invoiceable registrations found within date range")

    except Exception as e:
        yield logger.error(f"An unexpected error occurred during sync: {e}")
        raise
    finally:
        logger.update_event_state("COMPLETED")


def process_registrations(*args, **kwargs):
    iter = process_registrations_iter(*args, **kwargs)
    collections.deque(iter)


def process_payments_iter(event):
    """
    Gets all payments from merchant accounts within the given date range, and processes them
    accordingly in quickbooks. Payments are recorded as either a sales receipt or invoice payment.
    """
    # FIXME: This process currently handles registrations as well as donations. Ideally this should
    # only handle registrations, and donations should be handled by the donation website.
    logger = SyncEventLogger(event)
    logger.update_event_state("STARTED")

    if not qb.is_connected():
        raise BillingError(
            "Quickbooks is not connected. Please connect quickbooks in the Hive admin portal."
        )

    start_date = event.range_start
    end_date = event.range_end

    try:

        client = qb.get_client()

        # get all payments from braintree & stripe
        transactions = sorted(
            itertools.chain(
                (braintree.get_payments(start_date, end_date, logger=logger)),
                # (stripe.get_payments(start_date, end_date))
            ),
            key=lambda t: t.date,
        )

        if len(transactions) == 0:
            yield logger.info(
                "No merchant account transactions found within date range"
            )
            return

        # Pad date range for searching qbo by a few days in both directions
        qbo_start_date = start_date - timedelta(days=4)
        qbo_end_date = end_date + timedelta(days=4)

        # Get all QBO payments & sales within the given date range
        q_where = "TxnDate >= '{:%Y-%m-%d}' AND TxnDate <= '{:%Y-%m-%d}'".format(
            qbo_start_date, qbo_end_date
        )

        existing_payments = objects.Payment.where(q_where, qb=client)
        time.sleep(1)
        existing_sales = objects.SalesReceipt.where(q_where, qb=client)
        time.sleep(1)

        existing_refs = list(
            itertools.chain(
                (pmt.PaymentRefNum for pmt in existing_payments),
                (sale.PaymentRefNum for sale in existing_sales),
            )
        )

        # Gather all related invoices
        invoice_nums = [
            "'{}'".format(t.invoice_num) for t in transactions if t.invoice_num
        ]
        if len(invoice_nums) > 0:
            invoice_where = "DocNumber in ({})".format(", ".join(invoice_nums))
            qbo_invoices = {
                i.DocNumber: i for i in objects.Invoice.where(invoice_where, qb=client)
            }
            time.sleep(1)
        else:
            qbo_invoices = {}

        qbo_payments = []
        qbo_sales = []

        for transaction in transactions:
            log_obj = None

            # Get associated registration if we're dealing with an invoice payment
            if transaction.invoice_num:
                try:
                    log_obj = Registration.objects.get(
                        invoice_number=transaction.invoice_num
                    )
                except Registration.DoesNotExist:
                    yield logger.warn(
                        f"Cannot process payment for invoice #{transaction.invoice_num}: "
                        "Cannot find registration with this invoice number"
                    )
                except Registration.MultipleObjectsReturned:
                    yield logger.warn(
                        f"Cannot process payment for invoice #{transaction.invoice_num}: "
                        "Multiple registrations found with this invoice number"
                    )

            if transaction.ref in existing_refs:
                yield logger.debug(
                    "Skipping payment #{}: already processed".format(transaction.ref),
                    object=log_obj,
                )
                continue

            # skip any transactions prior to FY19
            if transaction.date < timezone.datetime(2018, 7, 1).date():
                yield logger.debug(
                    "Skipping transaction #{} (prior to fy19)".format(transaction.ref),
                    object=log_obj,
                )
                continue

            yield logger.debug(
                "Processing payment #{}".format(transaction.ref), object=log_obj
            )
            qbo_invoice = None
            if transaction.invoice_num:
                qbo_invoice = qbo_invoices[transaction.invoice_num]

            # Get the customer ref
            customer_ref = None
            if qbo_invoice:
                customer_ref = qbo_invoice.CustomerRef
            else:
                if transaction.email:
                    # Try to find customer by email
                    results = objects.Customer.filter(
                        PrimaryEmailAddr=transaction.email, qb=client
                    )
                    time.sleep(1)
                    if len(results) > 0:
                        customer_ref = results[0].to_ref()

                if not customer_ref:
                    # Try to find customer by name
                    results = objects.Customer.filter(
                        DisplayName=transaction.name, qb=client
                    )
                    time.sleep(1)
                    if len(results) > 0:
                        customer_ref = results[0].to_ref()

                if not customer_ref:
                    # Create the customer
                    yield logger.info(
                        '  Creating customer "{}"'.format(transaction.name),
                        object=log_obj,
                    )
                    customer = objects.Customer()
                    customer.DisplayName = transaction.name
                    # QBO validates email based on RFC-822
                    if RFC_822_RE.match(transaction.email):
                        customer.PrimaryEmailAddr = objects.EmailAddress()
                        customer.PrimaryEmailAddr.Address = transaction.email
                    customer.save(qb=client)
                    logger.event.item_count += 1
                    time.sleep(1)
                    customer_ref = customer.to_ref()

            deposit_acct = settings.MERCHANT_DEPOSIT_ACCOUNTS[transaction.merchant]

            item_type = transaction.item_type

            # The following payment types will automatically create sales receipt.
            # Otherwise, the payment should not be processed without an invoice.
            sales_receipt_types = {
                "DONATION": "One-time donation",
                "MONTHLY_DONATION": "Monthly donation",
            }

            if qbo_invoice:
                qbo_payment = objects.Payment()
                qbo_payment.CustomerRef = customer_ref
                qbo_payment.PaymentRefNum = transaction.ref
                qbo_payment.TotalAmt = transaction.amount
                qbo_payment.TxnDate = transaction.date.strftime("%Y-%m-%d")
                qbo_payment.DepositToAccountRef = objects.Ref()
                qbo_payment.DepositToAccountRef.value = deposit_acct
                qbo_payment.Line = [objects.PaymentLine()]
                qbo_payment.Line[0].Amount = qbo_payment.TotalAmt
                qbo_payment.Line[0].LinkedTxn = [objects.LinkedTxn()]
                qbo_payment.Line[0].LinkedTxn[0].TxnType = "Invoice"
                qbo_payment.Line[0].LinkedTxn[0].TxnId = qbo_invoice.Id
                qbo_payments.append(qbo_payment)
            elif item_type in sales_receipt_types:
                description = sales_receipt_types[item_type]
                item_id = settings.SALES_ITEMS[item_type]
                qbo_sale = objects.SalesReceipt()
                qbo_sale.CustomerRef = customer_ref
                qbo_sale.DocNumber = transaction.ref[:20]
                qbo_sale.PaymentRefNum = transaction.ref
                qbo_sale.TotalAmt = transaction.amount
                qbo_sale.TxnDate = transaction.date.strftime("%Y-%m-%d")
                qbo_sale.DepositToAccountRef = objects.Ref()
                qbo_sale.DepositToAccountRef.value = deposit_acct
                qbo_sale.Line = [objects.SalesItemLine()]
                qbo_sale.Line[0].LineNum = 1
                qbo_sale.Line[0].Description = description
                qbo_sale.Line[0].Amount = qbo_sale.TotalAmt
                qbo_sale.Line[0].DetailType = "SalesItemLineDetail"
                qbo_sale.Line[0].SalesItemLineDetail = objects.SalesItemLineDetail()
                qbo_sale.Line[0].SalesItemLineDetail.ItemRef = objects.Ref()
                qbo_sale.Line[0].SalesItemLineDetail.ItemRef.value = item_id
                qbo_sales.append(qbo_sale)
            elif item_type is not None:
                yield logger.warn(
                    "  skipping transaction #{}: cannot process {} payment type w/o invoice".format(
                        transaction.ref, item_type
                    ),
                    object=log_obj,
                )
                continue
            else:
                yield logger.warn(
                    "  skipping transaction #{}: could not determine transaction source".format(
                        transaction.ref
                    ),
                    object=log_obj,
                )
                continue

        if qbo_payments:
            yield from batch_create(qbo_payments, "payment", logger=logger)
        if qbo_sales:
            yield from batch_create(qbo_sales, "sales receipt", logger=logger)

    except Exception as e:
        yield logger.error(f"An unexpected error occured during sync: {e}")
        raise
    finally:
        logger.update_event_state("COMPLETED")


def process_payments(*args, **kwargs):
    iter = process_payments_iter(*args, **kwargs)
    collections.deque(iter)


def test_process_iter(event):
    logger = SyncEventLogger(event)
    logger.update_event_state("STARTED")

    try:
        yield logger.info("Starting test...")

        for i in range(0, 10):
            if i == 5:
                # Test warning
                yield logger.warn(f"Test {i+1}")
            else:
                yield logger.info(f"Test {i+1}")
            time.sleep((10 - i) / 3)

    except Exception as e:
        yield logger.error(f"An unexpected error occurred during sync: {e}")
        raise
    finally:
        logger.update_event_state("COMPLETED")


def test_process(*args, **kwargs):
    iter = test_process_iter(*args, **kwargs)
    collections.deque(iter)
