"""
Functions for dealing with hive registration accounts in QBO
"""

from quickbooks import objects
from quickbooks.exceptions import QuickbooksException
from unidecode import unidecode

from billing import qb


class QuickbooksError(Exception):
    pass


def get_account_contact(account):
    contact = account.default_contact
    if not contact and account.contacts.count() > 0:
        contact = account.contacts.all()[0]
    return contact


def qb_contact_name(contact):
    if not contact.account:
        return None
    contact_name = "{} (#{})".format(contact.name, contact.account.account_number)
    # Quickbooks account names are stupidly limited to ASCII.
    # They also report "Duplicate name" if you try to save w/ non-ascii characters. </venting>
    return unidecode(contact_name)


def create_customer(account):
    client = qb.get_client()

    contact = get_account_contact(account)

    qb_customer = objects.Customer()
    qb_customer.GivenName = contact.first_name
    qb_customer.FamilyName = contact.last_name
    qb_customer.DisplayName = qb_contact_name(contact)
    qb_customer.PrimaryEmailAddr = objects.EmailAddress()
    qb_customer.PrimaryEmailAddr.Address = account.user.email

    qb_address = objects.Address()
    qb_address.Line1 = contact.address1
    qb_address.Line2 = contact.address2
    qb_address.City = contact.city
    qb_address.CountrySubDivisionCode = contact.state
    qb_address.PostalCode = contact.postal_code
    qb_customer.BillAddr = qb_address

    if contact.phone:
        qb_phone = objects.PhoneNumber()
        qb_phone.FreeFormNumber = contact.phone
        qb_customer.PrimaryPhone = qb_phone

    try:
        qb_customer.save(qb=client)
    except QuickbooksException as e:
        raise QuickbooksError(
            f'Error while creating customer "{qb_customer.DisplayName}": {e}'
        )

    return qb_customer


def escape_name(name):
    return name.replace("'", "\\'")


def get_customer(account):
    client = qb.get_client()
    # search by billing_id, then by name with account number, then by name alone.
    searches = []
    if account.billing_id:
        searches.append("Id = '{}'".format(account.billing_id))
    contact = get_account_contact(account)
    if contact:
        searches.append(
            "DisplayName = '{}'".format(escape_name(qb_contact_name(contact)))
        )
        searches.append("DisplayName = '{}'".format(escape_name(contact.name)))

    for search in searches:
        result = objects.Customer.where(search, qb=client)
        if len(result) > 0:
            return result[0]
    return None


def get_or_create_customer(account):
    customer = get_customer(account)
    if customer is not None:
        return customer
    create_customer(account)
    return get_customer(account)
