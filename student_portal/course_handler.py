from django.db import transaction
from django.urls import reverse

from courses import models
from courses.handler import BaseCourseHandler
from courses.views import RedirectException
from student_portal.views import get_club, get_registration, get_student


class CourseHandler(BaseCourseHandler):
    def _get_registration(self, request):
        club = get_club(request)
        if not club:
            raise RedirectException(reverse("student_portal:select-club"))
        return get_registration(request, club)

    def get_course(self, request):
        student = get_student(request)
        registration = self._get_registration(request)

        if not registration.current_course:
            clubs = student.clubs.current(only_started=True)
            courses = models.Course.objects.filter(clubs__in=clubs)
            if courses.count() == 1:
                course = courses[0]
                registration.current_course = course
                registration.save()
            else:
                raise RedirectException(reverse("courses:select-course"))

        return registration.current_course

    def get_roadmap(self, request):
        registration = self._get_registration(request)
        course = self.get_course(request)

        # find existing roadmap (or create a new one) for this course
        existing = models.UserRoadmap.objects.filter(user=request.user, course=course)
        if existing.count() > 0:
            roadmap = existing.order_by("-date_created")[0]
        else:
            # We don't save the roadmap until there is at least one completed step, so we return a
            # temporary unsaved instance.
            roadmap = models.UserRoadmap(user=request.user, course=course)

        if roadmap.pk and registration.current_roadmap != roadmap:
            registration.current_roadmap = roadmap
            registration.save()
            if roadmap not in registration.roadmaps.all():
                registration.roadmaps.add(roadmap)

        return roadmap

    @transaction.atomic
    def update_roadmap_step(self, request, roadmap, step_id, completed):
        registration = self._get_registration(request)
        roadmap_step = None

        try:
            roadmap_step = roadmap.completed_steps.get(step_id=step_id)
        except models.UserRoadmapCompletedStep.DoesNotExist:
            pass

        if not completed and roadmap_step is not None:
            roadmap_step.delete()
            # Delete UserRoadmap if it has no compelted steps
            if roadmap.completed_steps.count() == 0:
                registration.current_roadmap = None
                registration.save()
                registration.roadmaps.remove(roadmap)
                roadmap.delete()
        elif completed and roadmap_step is None:
            # Save roadmap if it is not already saved
            if roadmap.pk is None:
                registration = self._get_registration(request)
                if roadmap.pk is None:
                    roadmap.save()
                if roadmap not in registration.roadmaps.all():
                    registration.roadmaps.add(roadmap)
                registration.current_roadmap = roadmap
                registration.save()

            roadmap_step = models.UserRoadmapCompletedStep.objects.create(
                step_id=step_id, roadmap=roadmap
            )

        return roadmap_step

    def get_available_courses(self, request):
        club = get_club(request)
        return club.courses.all()

    def on_course_selected(self, request, course):
        registration = self._get_registration(request)
        if registration.current_course != course:
            # re-set roadmap (it will be selected next time get_roadmap() is called)
            registration.current_roadmap = None
            # set the new course
            registration.current_course = course
            registration.save()

    def get_select_course_context(self, request, context):
        context["show_warning"] = True
        return context

    def get_resource_links(self, **kwargs):
        qs = super().get_resource_links(**kwargs)
        return qs.filter(visibility=models.ResourceLink.visibility.STUDENTS)
