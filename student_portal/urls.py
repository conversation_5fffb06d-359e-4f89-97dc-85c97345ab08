from django.conf.urls import url

from student_portal import views
from workspaces import views as workspace_views

app_name = "student_portal"

urlpatterns = [
    url(r"^$", views.home, name="home"),
    url(r"^select-club", views.select_club, name="select-club"),
    url(r"^login/$", views.StudentLoginView.as_view(), name="login"),
    # url(r'^workspace/$', workspace_views.workspace, name='workspace'),
    url(r"^badges/$", views.badges, name="badges"),
]
