from django.contrib.auth.views import LoginView, login_required
from django.shortcuts import redirect, render
from django.urls import reverse
from django.utils import timezone

from clubs.models import Club, StudentRegistration
from courses.views import RedirectException, get_course_handler
from students.models import Student

from .forms import StudentAuthenticationForm


def get_student(request):
    if request.user and request.user.is_authenticated:
        try:
            return Student.objects.get(user=request.user)
        except Student.DoesNotExist:
            pass
    return None


def get_registration(request, club):
    student = get_student(request)
    registration = StudentRegistration.objects.get(student=student, club=club)
    return registration


def get_club(request):
    club = None
    student = get_student(request)
    current_clubs = student.clubs.exclude(canceled=True).current(only_started=True)

    # If selected club is over, and there are new clubs to choose from, force re-selection
    if request.session.get("club_id"):
        club = Club.objects.get(pk=request.session.get("club_id"))
        if current_clubs.count() > 0 and club not in current_clubs:
            del request.session["club_id"]

    if not request.session.get("club_id"):
        # If there's only one club, use that
        if current_clubs.count() == 1:
            club = current_clubs[0]
            request.session["club_id"] = club.id

    return club


def club_required(view):
    def wrapper(request, *args, **kwargs):
        club = get_club(request)
        if not club:
            return redirect(reverse("student_portal:select-club"))
        return view(request, *args, **kwargs)

    return wrapper


def get_course(request, club):
    return get_registration(request, club).current_course


class StudentLoginView(LoginView):
    form_class = StudentAuthenticationForm


@login_required
@club_required
def home(request):
    student = get_student(request)
    club = get_club(request)
    course_handler = get_course_handler()
    available_courses = course_handler.get_available_courses(request)

    # Get announcements
    announcements = club.announcements.current().for_students()

    # Get current session
    next_sessions = club.sessions.filter(date__gte=timezone.now().date()).order_by(
        "date"
    )
    session = None
    if next_sessions.count() == 0:
        if club.sessions.count() > 0:
            session = club.sessions.order_by("-date")[0]
    else:
        session = next_sessions[0]

    try:
        course = course_handler.get_course(request)
        roadmap = course_handler.get_roadmap(request)
    except RedirectException:
        # We don't redirect to select-course on the home page so that students can see the session
        # links (join online, etc) without selecting a course
        course = None
        roadmap = None

    context = {
        "session": session,
        "club": club,
        "course": course,
        "available_courses": available_courses,
        "announcements": announcements,
        "student": student,
        "roadmap": roadmap,
    }

    return render(request, "student_portal/home.html", context)


@login_required
def select_club(request):
    if request.GET.get("club_id"):
        request.session["club_id"] = request.GET["club_id"]
        return redirect("student_portal:home")

    student = get_student(request)
    clubs = student.clubs.current(only_started=True)

    if clubs.count() == 0:
        clubs = student.clubs.order_by("-start_date")

    context = {"clubs": clubs}
    return render(request, "student_portal/select_club.html", context)


@login_required
def badges(request):
    context = {"badge_assertions": request.user.student.badge_assertions.all()}
    return render(request, "student_portal/badges.html", context)
