def club_registration_info(request):
    from clubs.models import Club
    from students.models import StudentLogin

    club_id = request.session.get("club_id")

    if not club_id:
        return {}

    if not hasattr(request.user, "student"):
        return {}

    student = request.user.student

    registration = student.registrations.get(club_id=club_id)

    if not registration.current_course:
        return {}

    try:
        club = Club.objects.get(id=club_id)
    except Club.DoesNotExist:
        return {}

    coding_login = None
    course = registration.current_course
    try:
        if course.coding_environment == "REPLIT":
            coding_login = student.logins.get(service="REPLIT")
        elif course.coding_environment == "GLITCH":
            coding_login = student.logins.get(service="GOOGLE")
    except StudentLogin.DoesNotExist:
        pass

    return {
        "club": club,
        "course": course,
        "registration": registration,
        "coding_environment": {
            "type": course.coding_environment,
            "name": course.get_coding_environment_display(),
            "url": course.coding_environment_url,
            "external": course.coding_environment_url.startswith("http"),
            "login": coding_login,
        },
    }
