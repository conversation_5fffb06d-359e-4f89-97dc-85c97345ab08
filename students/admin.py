import urllib.parse

from django import forms, http
from django.contrib import admin, messages
from django.core.exceptions import ValidationError
from django.db import transaction
from django.shortcuts import render
from django.urls import reverse
from import_export import fields as resource_fields
from import_export import resources
from import_export.admin import ExportActionModelAdmin

from clubs.admin_filters import (
    ClubCategoryFilter,
    ClubFilter,
    CourseFilter,
    PeriodFilter,
)
from hive.admin import register
from hive.utils import short_description
from students import models, utils


class StudentResource(resources.ModelResource):
    name = resource_fields.Field()
    student_id = resource_fields.Field()
    grade = resource_fields.Field()
    age = resource_fields.Field()
    guardian_name = resource_fields.Field()
    guardian_phone = resource_fields.Field()
    guardian_email = resource_fields.Field()
    address1 = resource_fields.Field()
    address2 = resource_fields.Field()
    city = resource_fields.Field()
    state = resource_fields.Field()
    postal_code = resource_fields.Field()
    other_info = resource_fields.Field()
    previous_experience = resource_fields.Field()
    current_club = resource_fields.Field()
    school = resource_fields.Field()
    school_student_id = resource_fields.Field()
    referred_by = resource_fields.Field()

    class Meta:
        model = models.Student
        fields = [
            "name",
            "student_id",
            "age",
            "grade",
            "birth_date",
            "shirt_size",
            "race",
            "ethnicity",
            "gender",
            "school_student_id",
            "referred_by",
        ]

    def dehydrate_name(self, student):
        return student.name

    def dehydrate_student_id(self, student):
        return student.student_id

    def dehydrate_age(self, student):
        return student.get_age()

    def dehydrate_grade(self, student):
        return student.grade_name

    def dehydrate_guardian_name(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.name

    def dehydrate_guardian_phone(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.phone

    def dehydrate_guardian_email(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.email

    def dehydrate_previous_experience(self, student):
        if student.extra_data:
            return student.extra_data.get("previous_activities")

    def dehydrate_address1(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.address1

    def dehydrate_address2(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.address2

    def dehydrate_city(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.city

    def dehydrate_state(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.state

    def dehydrate_postal_code(self, student):
        if student.account and student.account.default_contact:
            return student.account.default_contact.postal_code

    def dehydrate_other_info(self, student):
        if student.extra_data:
            return student.extra_data.get("other_info")
        return ""

    def dehydrate_current_club(self, student):
        registrations = student.registrations.order_by("-date")
        if registrations.count() > 0:
            return registrations[0].club.name
        return ""

    def dehydrate_school(self, student):
        return student.school or student.school_other

    def dehydrate_school_student_id(self, student):
        return student.school_student_id

    def dehydrate_referred_by(self, student):
        return student.extra_data and student.extra_data.get("referred_by")


# class StudentLoginInline(admin.TabularInline):
#     fields = ['service', 'username', 'password']


def generate_logins(service, modeladmin, request, queryset):
    existing = models.StudentLogin.objects.filter(
        service=service, active=True, student__in=queryset
    ).values_list("student_id", flat=True)
    queryset = queryset.exclude(pk__in=existing)
    count = 0
    for student in queryset:
        try:
            models.StudentLogin.objects.create(student=student, service=service)
            count += 1
        except Exception as e:
            modeladmin.message_user(request, "Error: {}".format(str(e)), messages.ERROR)
            continue

    if count > 0:
        modeladmin.message_user(
            request,
            "Successfully created {} {} logins".format(count, service.title()),
            messages.SUCCESS,
        )


@short_description("Generate logins (Google)")
def generate_google_logins(modeladmin, request, queryset):
    generate_logins("GOOGLE", modeladmin, request, queryset)


@short_description("Generate logins (Scratch)")
def generate_scratch_logins(modeladmin, request, queryset):
    generate_logins("SCRATCH", modeladmin, request, queryset)


@short_description("Merge students")
def merge_students(modeladmin, request, queryset):
    selected = [str(s.pk) for s in queryset]
    url = reverse("admin:students_student_merge")
    orig_filter = urllib.parse.quote(request.META["QUERY_STRING"])
    return http.HttpResponseRedirect(
        "{}?students={}&orig_filter={}".format(url, ",".join(selected), orig_filter)
    )


@short_description("Modify student segments")
def modify_segments(modeladmin, request, queryset):
    selected = [str(s.pk) for s in queryset]
    url = reverse("admin:students_student_modify_segments")
    orig_filter = urllib.parse.quote(request.META["QUERY_STRING"])
    return http.HttpResponseRedirect(
        "{}?students={}&orig_filter={}".format(url, ",".join(selected), orig_filter)
    )


class NeedsLoginFilter(admin.SimpleListFilter):
    title = "needs login"
    parameter_name = "needs_login"

    def lookups(self, request, model_admin):
        return (
            ("GOOGLE", "Google"),
            ("LDAP", "LDAP"),
            ("GLITCH", "Glitch"),
            ("REPLIT", "Repl.it"),
        )

    def queryset(self, request, queryset):
        service = self.value()
        if service is None:
            return queryset
        logins = models.StudentLogin.objects.filter(service=service, active=True)
        return queryset.exclude(logins__in=logins)


class StudentClubCategoryFilter(ClubCategoryFilter):
    club_lookup = "registrations__club"


class StudentClubFilter(ClubFilter):
    club_lookup = "registrations__club"


class StudentCourseFilter(CourseFilter):
    club_lookup = "registrations__club"


@register(models.StudentSegment)
class StudentSegmentAdmin(admin.ModelAdmin):
    list_display = ["name"]
    prepopulated_fields = {"slug": ("name",)}
    fields = ["name", "slug"]


@register(models.Student)
class StudentAdmin(ExportActionModelAdmin):
    icon = '<i class="material-icons">face</i>'
    list_display = [
        "name",
        "student_id",
        "_segments",
        "age",
        "grade_name",
        "school_name",
        "guardian_name",
        "guardian_phone",
    ]
    search_fields = ["first_name__unaccent", "last_name__unaccent", "student_id"]
    list_filter = [
        "segments",
        PeriodFilter,
        StudentClubCategoryFilter,
        StudentCourseFilter,
        StudentClubFilter,
        NeedsLoginFilter,
    ]
    resource_class = StudentResource
    actions = ExportActionModelAdmin.actions + [
        modify_segments,
        merge_students,
        generate_google_logins,
        generate_scratch_logins,
    ]

    readonly_fields = [
        "student_id",
        "grade_name",
        "date_added",
        "date_modified",
        "guardian_name",
        "guardian_phone",
        "age",
    ]

    autocomplete_fields = ["account"]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "account",
                    "date_added",
                    "student_id",
                    ("first_name", "last_name"),
                    ("birth_date", "age"),
                    ("grade_offset", "grade_name"),
                    "preferred_pronouns",
                    ("school", "school_other", "school_student_id"),
                    "shirt_size",
                    # ('extra_data',),
                )
            },
        ),
        (
            "Demographics",
            {
                "fields": (
                    ("postal_code", "county"),
                    ("gender", "race", "ethnicity"),
                    ("annual_income_min", "annual_income_max", "num_household_members"),
                ),
                "classes": ("collapse", "collapsed"),
            },
        ),
    )

    def age(self, obj):
        return obj.age

    def _segments(self, obj):
        return obj.segment_names and ", ".join(obj.segment_names) or ""

    def get_urls(self):
        from django.conf.urls import url

        return [
            url(
                "^modify-segments/$",
                self.admin_site.admin_view(self.modify_segment_view),
                name="students_student_modify_segments",
            ),
            url(
                "^merge/$",
                self.admin_site.admin_view(self.merge_view),
                name="students_student_merge",
            ),
        ] + super().get_urls()

    def guardian_name(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.name

    def guardian_phone(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.phone

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return (
            qs.with_age().with_segments_arr().select_related("account__default_contact")
        )

    def lookup_allowed(self, *args, **kwargs):
        # Allow all lookups
        return True

    def redirect_with_filters(self, request, url):
        if request.GET.get("orig_filter"):
            url += "?" + urllib.parse.unquote(request.GET["orig_filter"])
        return http.HttpResponseRedirect(url)

    def modify_segment_view(self, request, *args, **kwargs):
        from students.models import Student

        if request.method == "POST":
            student_ids = request.POST.getlist("students")
            segment_id = request.POST.get("segment_id")
            action = request.POST.get("action")
            students = Student.objects.filter(id__in=student_ids)
            count = students.count()
            segment = models.StudentSegment.objects.get(pk=segment_id)
            msg = ""

            for student in students:
                if action == "ADD":
                    student.segments.add(segment)
                    msg = f'Successfully added {count} students to "{segment.name}" segment.'
                elif action == "REMOVE":
                    student.segments.remove(segment)
                    msg = f'Successfully removed {count} students from "{segment.name}" segment.'
                else:
                    raise ValidationError(f"Invalid action: {action}")

            self.message_user(request, msg, messages.SUCCESS)
            return self.redirect_with_filters(
                request, reverse("admin:students_student_changelist")
            )

        student_ids = (int(id) for id in request.GET.get("students").split(","))

        students = Student.objects.filter(id__in=student_ids).order_by("id")
        context = {
            "segments": models.StudentSegment.objects.all(),
            "students": students,
        }
        return render(request, "admin/students/modify_segments.html", context)

    def merge_view(self, request, *args, **kwargs):
        from students.models import Student

        if request.method == "POST":
            student_ids = request.POST.getlist("students")
            students = Student.objects.filter(id__in=student_ids)
            keep_id = request.POST.get("keep")
            keep_student = Student.objects.get(pk=keep_id)
            merge_students = Student.objects.filter(id__in=student_ids).exclude(
                id=keep_id
            )
            # merge students by most recent first
            merge_students = merge_students.order_by("-id")
            try:
                with transaction.atomic():
                    for student in merge_students:
                        utils.merge_student(student, keep_student)
            except utils.MergeError as error:
                context = {
                    "students": students,
                    "merge_error": error,
                }
                return render(request, "admin/students/merge.html", context)
            self.message_user(request, "Successfully merged student.", messages.SUCCESS)

            if request.POST.get("next"):
                return http.HttpResponseRedirect(request.POST["next"])

            return self.redirect_with_filters(
                request, reverse("admin:students_student_changelist")
            )

        student_ids = (int(id) for id in request.GET.get("students").split(","))
        students = Student.objects.filter(id__in=student_ids).order_by("id")
        context = {"next": request.GET.get("next"), "students": students}
        return render(request, "admin/students/merge.html", context)


class StudentLoginResource(resources.ModelResource):
    student_name = resource_fields.Field()
    student_age = resource_fields.Field()
    student_grade = resource_fields.Field()
    username = resource_fields.Field()
    password = resource_fields.Field()

    def dehydrate_student_name(self, login):
        return login.student.name

    def dehydrate_student_age(self, login):
        return login.student.get_age()

    def dehydrate_student_grade(self, login):
        return login.student.grade

    def dehydrate_username(self, login):
        return login.username

    def dehydrate_password(self, login):
        return login.password


class StudentLoginAdminForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # allow blank username and password to be auto-generated by model
        self.fields["username"].required = False
        self.fields["username"].help_text = "Leave blank to auto-generate"

        self.fields["password"].required = False
        self.fields["password"].help_text = "Leave blank to auto-generate"

    class Meta:
        model = models.StudentLogin
        fields = "__all__"


"""
@register(models.StudentLogin)
class StudentLogin(ExportActionModelAdmin):
    form = StudentLoginAdminForm
    icon = '<i class="material-icons">vpn_key</i>'
    list_display = ['student', 'service']
    search_fields = ['student__first_name__unaccent', 'student__last_name__unaccent']
    list_filter = [
        PeriodFilter,
        StudentClubCategoryFilter,
        StudentCourseFilter,
        StudentClubFilter,
        'service',
        'active'
    ]
    resource_class = StudentLoginResource
"""
