from datetime import date, datetime

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.gis.db.models import <PERSON>Field
from django.contrib.postgres.fields import ArrayField, JSONField
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from localflavor.us import models as lf_models

from accounts.models import Account
from clubs.query import PeriodQuerySet
from clubs.utils import get_current_school_year
from contacts.models import DemographicsModel, Person
from hive import google
from hive.utils import decrypt, encrypt, geocode
from users.models import User

from . import constants, scratch, utils
from .query import StudentQuerySet


class StudentUser(User):
    """
    Proxy model needed for creating student users from within the main Hive site, where
    USERNAME_FIELD defaults to 'email'. For students, we want USERNAME_FIELD to be 'username'.
    """

    USERNAME_FIELD = "username"

    class Meta:
        proxy = True

    @property
    def student_channel_name(self):
        return f"student-{self.username}"

    def save(self, *args, **kwargs):
        if not self.canonical_username:
            self.canonical_username = self.student.student_id
        super().save(*args, **kwargs)


class StudentSegment(models.Model):
    """
    StudentSegments are used to segment students for the purpose of reporting.
    """

    name = models.CharField(max_length=32)
    slug = models.SlugField(unique=True)

    def natural_key(self):
        return (self.slug,)

    def __str__(self):
        return self.name


class Student(Person, DemographicsModel, models.Model):
    student_id = models.CharField(max_length=16, unique=True, null=True)
    user = models.OneToOneField(
        StudentUser,
        related_name="student",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    account = models.ForeignKey(
        Account,
        related_name="students",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    date_added = models.DateTimeField(default=timezone.now)
    date_modified = models.DateTimeField(default=timezone.now)
    birth_date = models.DateField(_("Birth date"))
    encrypted_default_password = models.CharField(
        "Password", max_length=255, blank=True
    )
    not_duplicate = ArrayField(models.PositiveIntegerField(), default=list, blank=True)

    # The following fields are also stored on `clubs.StudentRegistration`. Values are copied from
    # the registration to the student record. The registration record serves as a historical record
    # of these fields at the the time of registration.
    segments = models.ManyToManyField(StudentSegment, blank=True, serialize=False)
    postal_code = lf_models.USZipCodeField(_("Postal code"), blank=True)
    postal_code_geo = PointField(null=True, blank=True)
    county = models.CharField(
        _("County"), max_length=255, blank=True, choices=constants.COUNTIES
    )
    school = models.ForeignKey(
        "clubs.Location",
        verbose_name=_("School"),
        blank=True,
        null=True,
        limit_choices_to={"type": "SCHOOL"},
        on_delete=models.PROTECT,
    )
    school_other = models.CharField(_("School other"), max_length=255, blank=True)
    school_student_id = models.CharField(
        _("School student id"), max_length=255, blank=True
    )
    grade_offset = models.IntegerField(blank=True, null=True)  # See get_grade/set_grade
    shirt_size = models.CharField(
        _("Shirt size"), max_length=5, choices=constants.SHIRT_SIZE_CHOICES, blank=True
    )
    annual_income_min = models.PositiveIntegerField(null=True, blank=True)
    annual_income_max = models.PositiveIntegerField(null=True, blank=True)
    num_household_members = models.PositiveIntegerField(null=True, blank=True)

    extra_data = JSONField(null=True, blank=True)

    clubs = models.ManyToManyField("clubs.Club", through="clubs.StudentRegistration")

    objects = StudentQuerySet.as_manager()

    def save(self, *args, **kwargs):
        # FIXME: If birth_date changes, grade_offset should be adjusted accordingly
        self.date_modified = timezone.now()

        if not self.student_id:
            self.student_id = utils.generate_student_id(self)

        if not self.pk and not self.default_password:
            password = utils.generate_kidsafe_password()
            self.default_password = password

        # create a user for the student portal
        if not self.user_id:
            self.user = StudentUser.objects.create(
                username=self.student_id,
                password=self.default_password,
                canonical_username=self.student_id,
            )

        # Sync default_password with user record
        self.user.set_password(self.default_password)
        self.user.save()

        # Set postal code geo coordinates
        if self.postal_code:
            self.postal_code_geo = geocode(postalcode=self.postal_code)

        super().save(*args, **kwargs)

    def get_grade(self, school_year=None, offset=None):
        """
        Returns student grade as of the given school year. If school year is not supplied, the
        current school year (as if beginning in summer) is used.
        """
        # We get grade dynamically using an offset so we don't have to change it each year
        if offset is None:
            offset = self.grade_offset

        if not self.birth_date or offset is None:
            return None

        school_year = school_year or get_current_school_year()

        # A good approximation of school grade is to take the age of the
        # student at the start of the school year (usually around 9/1) and
        # subtract 5.
        september = date(school_year, 9, 1)
        september_age = relativedelta(september, self.birth_date).years
        return september_age - 5 + offset

    def set_grade(self, value, school_year=None):
        """
        Sets student grade as of the given school year. If school year is not supplied, the
        current school year (as if beginning in summer) is used.
        """
        if self.birth_date is None:
            raise ValueError("Cannot set grade without knowing birth date")
        # We determine the offset by initially setting it to zero to get the "assumed grade",
        # and getting the difference between the assumed grade and the given value.
        assumed_grade = self.get_grade(school_year=school_year, offset=0)
        if value is not None:
            school_year = school_year or get_current_school_year()
            self.grade_offset = int(value) - assumed_grade
        else:
            self.grade_offset = None

    @property
    def default_password(self):
        if self.encrypted_default_password:
            try:
                return decrypt(self.encrypted_default_password).decode()
            except UnicodeDecodeError:
                pass
        return ""

    @default_password.setter
    def default_password(self, value):
        self.encrypted_default_password = encrypt(value.encode())

    @property
    def school_name(self):
        if self.school:
            return self.school.name
        return self.school_other

    @property
    def grade(self):
        return self.get_grade()

    @grade.setter
    def grade(self, value):
        self.set_grade(value)

    @property
    def grade_name(self):
        return utils.get_grade_name(self.grade)

    def get_age(self, as_of=None):
        if not self.birth_date:
            return None
        if as_of is None:
            as_of = date.today()
        as_of = datetime.combine(as_of, datetime.min.time())
        return relativedelta(as_of, self.birth_date).years

    @property
    def google_login(self):
        google_logins = self.logins.filter(service="GOOGLE", active=True).order_by(
            "-date_added"
        )
        if google_logins.count() > 0:
            return google_logins[0]
        return None

    def get_absolute_url(self):
        # get last registered club
        from clubs.models import StudentRegistration

        registrations = StudentRegistration.objects.order_by("-id").filter(
            student_id=self.id
        )
        if len(registrations) > 0:
            club = registrations[0].club
            return reverse(
                "dashboard:student-details",
                kwargs={"club_id": club.id, "student_id": self.id},
            )

    def __str__(self):
        return "{0.first_name} {0.last_name}".format(self)


class StudentLogin(models.Model):
    """
    The logins stored here are not used for authentication. This is primarily used to store the
    login info that is printed student folder labels.
    """

    date_added = models.DateTimeField(auto_now_add=True)
    student = models.ForeignKey(
        Student, related_name="logins", on_delete=models.CASCADE
    )
    service = models.CharField(
        choices=constants.STUDENT_LOGIN_SERVICE_CHOICES, max_length=255
    )
    username = models.CharField("Username/email", max_length=255)
    password = models.CharField(max_length=255)
    encrypted_password = models.CharField(max_length=255)
    active = models.BooleanField(default=True)

    objects = PeriodQuerySet.as_manager("student__clubs")

    class Meta:
        unique_together = ["service", "username"]

    @property
    def password(self):
        if not self.encrypted_password:
            return ""
        return decrypt(self.encrypted_password).decode()

    @password.setter
    def password(self, value):
        self.encrypted_password = encrypt(value.encode())

    def save(self, *args, **kwargs):
        skip_sync = kwargs.pop("skip_sync", False)

        if self.service == "GOOGLE":
            if not skip_sync:
                google.create_or_update_student_account(self)

        elif self.service in ("REPLIT", "GLITCH"):
            if not self.id:
                self.username = "{}@{}".format(
                    self.student.student_id, settings.GOOGLE_STUDENT_ACCOUNT_DOMAIN
                )
                self.password = self.student.default_password

        elif self.service == "SCRATCH":
            if not self.id:
                self.username = scratch.find_available_username(self.student.student_id)
                self.password = self.student.default_password

        super().save(*args, **kwargs)

    def __str__(self):
        return "[{}] {}".format(self.service, self.student)
