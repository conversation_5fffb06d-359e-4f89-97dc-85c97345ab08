from datetime import date

from django.contrib.postgres.aggregates import ArrayAgg
from django.db import models

from clubs.query import PeriodQuerySet
from hive.utils import Age, DatePart


class SegmentQuerySetMixin(models.QuerySet):
    def with_segments_arr(self):
        """
        Annotates student segments combined into array field for easy display without requiring an
        extra query per row
        """
        filter = models.Q(segments__slug__isnull=False)
        return self.annotate(
            segment_slugs=ArrayAgg("segments__slug", filter=filter),
            segment_names=ArrayAgg("segments__name", filter=filter),
        )


class StudentQuerySet(SegmentQuerySetMixin, PeriodQuerySet):
    club_lookup = "clubs"

    def with_age(self, as_of=None):
        if as_of is None:
            as_of = date.today()
        as_of = models.Value(as_of, output_field=models.DateField())
        return self.annotate(age=DatePart("year", Age(as_of, "birth_date")))
