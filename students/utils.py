import random
from collections import defaultdict

from django.contrib.admin.utils import NestedObjects
from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Count, F, Value
from django.db.models.functions.text import Concat, Lower, Transform, Trim
from django.db.models.query import IntegrityError

from hive.utils import clean_username, ordinal


class Left(Transform):
    template = "%(function)s(%(expressions)s, %(n)s)"
    function = "LEFT"
    lookup_name = "left"


prefixes = [
    "fast",
    "tiny",
    "noisy",
    "thin",
    "cute",
    "quiet",
    "red",
    "rose",
    "short",
    "slow",
    "sweet",
    "good",
    "itchy",
    "damp",
    "ivory",
    "gold",
    "round",
    "old",
    "amber",
    "crazy",
    "slimy",
    "mushy",
    "lazy",
    "windy",
    "wise",
    "odd",
    "loud",
    "best",
    "jolly",
    "fresh",
    "busy",
    "pink",
    "small",
    "misty",
    "smart",
    "green",
    "swift",
    "olive",
    "brave",
    "lucky",
    "hot",
    "fancy",
    "slim",
    "bent",
    "lime",
    "jazzy",
    "first",
    "jade",
    "murky",
    "timy",
    "tall",
    "rich",
    "super",
    "mega",
    "spicy",
    "angry",
    "dizzy",
    "big",
    "scary",
    "muddy",
    "new",
    "gray",
    "lush",
    "black",
    "lumpy",
    "bumpy",
    "calm",
    "wild",
    "keen",
    "sad",
    "kind",
    "funny",
    "brown",
    "talk",
    "heavy",
    "icy",
    "ultra",
    "free",
    "messy",
    "flat",
    "curly",
    "great",
    "huge",
    "giant",
    "dark",
    "white",
    "zany",
    "wacky",
    "pale",
    "light",
]

suffixes = [
    "jaguar",
    "paper",
    "smily",
    "start",
    "mind",
    "bean",
    "month",
    "skunk",
    "hog",
    "mouse",
    "engine",
    "nail",
    "tiger",
    "kitten",
    "show",
    "salt",
    "fork",
    "amber",
    "eff",
    "angle",
    "quid",
    "hippo",
    "ink",
    "bird",
    "cave",
    "rat",
    "dingo",
    "snail",
    "jade",
    "lunch",
    "ice",
    "plant",
    "boot",
    "face",
    "time",
    "tree",
    "wool",
    "snake",
    "camel",
    "sugar",
    "grass",
    "cat",
    "bell",
    "lemur",
    "knot",
    "bulb",
    "ox",
    "soap",
    "cougar",
    "pony",
    "jeans",
    "brass",
    "seal",
    "twist",
    "puppy",
    "panda",
    "car",
    "purple",
    "war",
    "whale",
    "house",
    "patch",
    "box",
    "hook",
    "soda",
    "tooth",
    "raccoon",
    "hall",
    "cap",
    "hyena",
    "kick",
    "cheese",
    "muskrat",
    "thing",
    "plane",
    "soup",
    "lead",
    "book",
    "seed",
    "match",
    "ocean",
    "gnu",
    "horn",
    "butter",
    "look",
    "actor",
    "lizard",
    "mint",
    "flag",
    "summer",
    "warthog",
    "stop",
    "goose",
    "wombat",
    "worm",
    "sound",
    "dime",
    "wood",
    "giraffe",
    "iguana",
    "number",
    "copper",
    "bear",
    "grape",
    "rice",
    "kite",
    "noise",
    "mash",
    "yak",
    "dress",
    "stamp",
    "rhino",
    "flock",
    "fog",
    "jewel",
    "turtle",
    "crown",
    "brain",
    "jelly",
    "end",
    "lift",
    "lynx",
    "rabbit",
    "gopher",
    "drum",
    "steam",
    "smile",
    "leopard",
    "brown",
    "newt",
    "sand",
    "pump",
    "glue",
    "spring",
    "pink",
    "scale",
    "dugong",
    "help",
    "page",
    "story",
    "wind",
    "mice",
    "fruit",
    "robin",
    "ivory",
    "food",
    "shoe",
    "sloth",
    "stick",
    "bat",
    "bbear",
    "cheetah",
    "earth",
    "space",
    "cow",
    "wing",
    "play",
    "smaile",
    "lace",
    "puma",
]


def generate_kidsafe_password():
    return "".join(
        (
            random.sample(prefixes, 1)[0] + random.sample(suffixes, 1)[0],
            str(random.randint(0, 9)),
            str(random.randint(0, 9)),
        )
    )


def generate_student_id_options(student):
    # Iterate through various possible formats for student id's so that everyone has a unique id.
    first_initial = student.first_name[0]
    middle_initial = student.middle_name and student.middle_name[0] or None
    last_initial = student.last_name[0]
    birth_date = student.birth_date

    yield "{}{}{:%m%d%y}".format(first_initial, last_initial, birth_date).lower()
    yield "{}{}{:%m%d%Y}".format(first_initial, last_initial, birth_date).lower()
    yield "{}{}{:%Y%m%d}".format(first_initial, last_initial, birth_date).lower()

    if middle_initial:
        yield "{}{}{}{:%m%d%y}".format(
            first_initial, middle_initial, last_initial, birth_date
        ).lower()
        yield "{}{}{}{:%m%d%Y}".format(
            first_initial, middle_initial, last_initial, birth_date
        ).lower()
        yield "{}{}{}{:%Y%m%d}".format(
            first_initial, middle_initial, last_initial, birth_date
        ).lower()

    n = 0
    while True:
        n += 1
        yield "{}{}{}{:%m%d%y}".format(
            n, first_initial, last_initial, birth_date
        ).lower()


def generate_student_id(student):
    from students.models import Student

    student_id_options = generate_student_id_options(student)
    student_id = next(student_id_options)
    while Student.objects.filter(student_id=student_id).count() > 0:
        student_id = next(student_id_options)

    return clean_username(student_id)


class MergeError(Exception):
    pass


class MergeIntegrityError(MergeError):
    def __init__(self, related_objects):
        self.related_objects = set(related_objects)
        msg = (
            "Record being merged cannot be deleted due to existing related "
            "objects that would also be deleted"
        )
        super().__init__(msg)


@transaction.atomic()
def merge_student(student, to_student):
    delete_objects = []

    # list of models to allow cascade delete (typically M2M intermediate models)
    allow_cascade_delete = ["student_segments", "studentregistration_student_segments"]

    delete_objects.append(student)

    def is_empty(val):
        return val is None or val == ""

    # merge empty fields
    skip_fields = ["id", "extra_data"]
    for field in student._meta.fields:
        if field.name in skip_fields or hasattr(field, "rel"):
            continue
        keep_val = getattr(to_student, field.name, None)
        merge_val = getattr(student, field.name, None)
        if is_empty(keep_val) and not is_empty(merge_val):
            setattr(to_student, field.name, merge_val)

    # merge extra_data into a special json field, because I'm lazy, and deep-merge is hard.
    if student.extra_data:
        if not to_student.extra_data:
            to_student.extra_data = {}
        if "merged_extra_data" not in to_student.extra_data:
            to_student.extra_data["merged_extra_data"] = []
        to_student.extra_data["merged_extra_data"].append(student.extra_data)

    # update segments
    for segment in student.segments.all():
        to_student.segments.add(segment)

    to_student.save()

    # move logins
    for login in student.logins.all():
        login.student_id = to_student.id
        login.save(skip_sync=True)

    # move attendances
    for attendance in student.session_attendances.all().select_related(
        "session", "session__club"
    ):
        attendance.student_id = to_student.id
        club_code = attendance.session.club.club_code
        session_number = attendance.session.number
        try:
            attendance.save()
        except IntegrityError:
            raise MergeError(
                f"Cannot delete student id:{student.pk}: student has attendance data"
                f"in the same session as student with id {to_student.id} "
                f"(club: {club_code}, session: #{session_number})."
            )

    # move registrations
    for registration in student.registrations.all():
        # If the duplicate student was already regsitered for a the same club,
        # simply delete the duplicate registration.
        if to_student.registrations.filter(club_id=registration.club_id):
            delete_objects.append(registration)
        else:
            registration.student_id = to_student.id
            registration.save()

    # move exit tickets
    for ticket in student.quiz_results.all():
        ticket.student_id = to_student.id
        ticket.save()

    # move roadmaps
    for roadmap in student.user.course_roadmaps.all():
        roadmap.user_id = to_student.user.id
        roadmap.save()

    # move ide projects
    for project in student.user.ide_projects.all():
        project.user_id = to_student.user.id
        project.save()

    # move badges
    # FIXME: if the student_id changed, we would need to update the remote badge
    # on badgr with the new student email
    for badge in student.badge_assertions.all():
        badge.student_id = to_student.user.id
        badge.save()

    # Make sure other objects are not deleted that we didn't expect
    related_objects = []
    collector = NestedObjects(using="default")
    delete_objects_by_type = defaultdict(list)
    for obj in delete_objects:
        delete_objects_by_type[obj._meta.model].append(obj)

    for obj_list in delete_objects_by_type.values():
        collector.collect(obj_list)
        for model, obj in collector.instances_with_model():
            if (
                obj not in delete_objects
                and obj._meta.model_name not in allow_cascade_delete
            ):
                related_objects.append((model.__name__, obj))

    if len(related_objects):
        raise MergeIntegrityError(related_objects)

    # Delete remaining objects
    for obj in delete_objects:
        obj.delete()


def find_student(first_name, last_name, birth_date):
    from students.models import Student

    students = Student.objects.annotate(
        first_clean=Lower(Trim(F("first_name"))),
        last_clean=Lower(Trim(F("last_name"))),
    ).filter(
        first_clean=first_name.lower().strip(),
        last_clean=last_name.lower().strip(),
        birth_date=birth_date,
    )
    return students


def find_duplicate_students(student=None, account=None):
    """
    Find possible duplicates based on first/last initial and DOB.

    If `student` is provided, return only possible duplicates of the given student (can be un-saved
    student instance).

    If `account` is provided, duplicates from other accounts will be excluded (this is used during
    registration, as there is no solution to merge accounts during the signup process)
    """
    from students.models import Student

    search_q = Lower(
        Concat(
            Left("first_name", n=1),
            Left("last_name", n=1),
            Value("-"),
            "birth_date",
            output_field=CharField(),
        )
    )
    if student is not None:
        student_search = (
            f"{student.first_name[0]}{student.last_name[0]}-{student.birth_date}"
        )
        duplicates = (
            Student.objects.annotate(search=search_q)
            .filter(search=student_search.lower())
            .exclude(pk=student.pk)
        )
        duplicates = duplicates.exclude(pk__in=student.not_duplicate)
        if student.pk:
            duplicates = duplicates.exclude(not_duplicate__contains=[student.pk])
        yield from duplicates
    else:
        duplicates = (
            Student.objects.annotate(search=search_q)
            .values("search")
            .order_by("search")
            .annotate(count=Count("search"))
            .filter(count__gt=1)
        )
        for dup in duplicates:
            objects = Student.objects.annotate(search=search_q).filter(
                search=dup["search"]
            )
            obj_ids = [obj.pk for obj in objects]
            dup_objects = []
            for obj in objects:
                if (set(obj.not_duplicate) ^ set(obj_ids)) - set([obj.pk]):
                    dup_objects.append(obj)
            if len(dup_objects):
                yield (dup["search"], dup_objects)


def get_student_participated_courses(student):
    """
    Returns courses student has participated in (based on created roadmaps)
    """
    from courses.models import Course

    return Course.objects.filter(user_roadmaps__user=student.user).distinct()


def get_grade_name(grade):
    if grade is None:
        return None

    if grade == 0:
        return "K"
    if grade <= 12:
        return ordinal(grade)
    else:
        return "Graduate"
