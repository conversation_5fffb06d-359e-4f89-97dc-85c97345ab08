from django.utils.translation import gettext_lazy as _
from django.utils.translation import pgettext_lazy as _p

STUDENT_LOGIN_SERVICE_CHOICES = (
    ("GOOGLE", "Google"),
    ("LDAP", "LDAP / Workspaces"),
    ("REP<PERSON><PERSON>", "Repl.it"),
    ("G<PERSON><PERSON><PERSON>", "Glitch"),
    ("SCRATCH", "Scratch"),
    ("PENCILCODE", "PencilCode"),
    ("ML4K", "ML4K"),
)

SHIRT_SIZE_CHOICES = (
    ("YSM", _("Youth Small")),
    ("YM", _("Youth Medium")),
    ("YL", _("Youth Large")),
    ("YXL", _("Youth XL")),
    ("SM", _("Adult Small")),
    ("M", _("Adult Medium")),
    ("L", _("Adult Large")),
    ("XL", _("Adult XL")),
    ("XXL", _("Adult XXL")),
    ("3XL", _("Adult 3XL")),
)

RACE_CHOICES = (
    ("NATIVE", _("American Indian or Alaskan Native")),
    ("ASIAN", _("Asian")),
    ("BLACK", _("Black or African American")),
    ("PAC_ISLANDER", _("Hawaiian or Pacific Islander")),
    ("WHITE", _("White")),
    ("OTHER", _("Other")),
)

ETHNICITY_CHOICES = (
    ("HISPANIC", _("Hispanic or Latina/Latino")),
    ("NOT_HISPANIC", _("Not Hispanic or Latina/Latino")),
)

GENDER_CHOICES = (
    ("MALE", _("Male")),
    ("FEMALE", _("Female")),
    ("NONBINARY", _("Non-binary")),
)

INCOME_CHOICES = (
    (n, n)
    for n in (
        _("Under $15,000"),
        "$15,000 - $25,000",
        "$25,001 - $35,000",
        "$35,001 - $45,000",
        "$45,001 - $55,000",
        _("Over $55,000"),
    )
)

MILITARY_STATUS_CHOICES = (
    ("ENLISTED", _("Enlisted in the US militiary guard/reserve")),
    ("VETERAN", _("US military veteran")),
)

#: The 48 contiguous states, plus the District of Columbia.
CONTIGUOUS_STATES = (
    ("AL", _("Alabama")),
    ("AZ", _("Arizona")),
    ("AR", _("Arkansas")),
    ("CA", _("California")),
    ("CO", _("Colorado")),
    ("CT", _("Connecticut")),
    ("DE", _("Delaware")),
    ("DC", _("District of Columbia")),
    ("FL", _("Florida")),
    ("GA", _p("US state", "Georgia")),
    ("ID", _("Idaho")),
    ("IL", _("Illinois")),
    ("IN", _("Indiana")),
    ("IA", _("Iowa")),
    ("KS", _("Kansas")),
    ("KY", _("Kentucky")),
    ("LA", _("Louisiana")),
    ("ME", _("Maine")),
    ("MD", _("Maryland")),
    ("MA", _("Massachusetts")),
    ("MI", _("Michigan")),
    ("MN", _("Minnesota")),
    ("MS", _("Mississippi")),
    ("MO", _("Missouri")),
    ("MT", _("Montana")),
    ("NE", _("Nebraska")),
    ("NV", _("Nevada")),
    ("NH", _("New Hampshire")),
    ("NJ", _("New Jersey")),
    ("NM", _("New Mexico")),
    ("NY", _("New York")),
    ("NC", _("North Carolina")),
    ("ND", _("North Dakota")),
    ("OH", _("Ohio")),
    ("OK", _("Oklahoma")),
    ("OR", _("Oregon")),
    ("PA", _("Pennsylvania")),
    ("RI", _("Rhode Island")),
    ("SC", _("South Carolina")),
    ("SD", _("South Dakota")),
    ("TN", _("Tennessee")),
    ("TX", _("Texas")),
    ("UT", _("Utah")),
    ("VT", _("Vermont")),
    ("VA", _("Virginia")),
    ("WA", _("Washington")),
    ("WV", _("West Virginia")),
    ("WI", _("Wisconsin")),
    ("WY", _("Wyoming")),
)

# Counties used for student intake (this will likely need to be dynamic in the future)
COUNTIES = (
    ("Dallas County", "Dallas County"),
    ("Collin County", "Collin County"),
    ("Rockwall County", "Rockwall County"),
    ("Denton County", "Denton County"),
    ("Ellis County", "Ellis County"),
    ("Hood County", "Hood County"),
    ("Hunt County", "Hunt County"),
    ("Johnson County", "Johnson County"),
    ("Kaufman County", "Kaufman County"),
    ("Parker County", "Parker County"),
    ("Somervell County", "Somervell County"),
    ("Tarrant County", "Tarrant County"),
    ("Wise County", "Wise County"),
    ("OTHER", "Other"),
)
