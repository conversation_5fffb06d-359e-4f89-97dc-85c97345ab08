import re
import time

import requests


def strip(s):
    return re.sub(r"[^A-Za-z0-9]", "", s)


def find_available_username(username):
    counter = 0
    initial_username = username
    while counter < 50:
        # let's not pound the scratch servers
        counter += 1
        time.sleep(0.5)

        r = requests.get("https://api.scratch.mit.edu/users/{}".format(username))
        if r.status_code == 404:
            # username is available
            return username
        else:
            username = initial_username + "-" + str(counter)

    raise Exception("Could not find an available username for {}".format(username))
