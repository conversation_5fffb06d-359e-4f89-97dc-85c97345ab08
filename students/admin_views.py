import collections

from django import http
from django.db.models import Count
from django.shortcuts import render
from django.urls import reverse

from clubs.constants import LOCATION_TYPE_CHOICES, PERIOD_CHOICES
from clubs.models import Club, ClubCategory, Session, StudentRegistration
from clubs.utils import get_current_school_year
from hive.admin import register_appview
from hive.utils import percent
from students import models
from students.utils import find_duplicate_students


@register_appview(
    "students",
    "demographics/$",
    "student_demographics",
    "Student demographics",
    "Reports",
)
def demographics_view(request, *args, **kwargs):
    from contacts import constants

    start_year = Session.objects.with_period_info().order_by("date")[0].fiscal_year
    end_year = Session.objects.with_period_info().order_by("-date")[0].fiscal_year

    current_fy = get_current_school_year() + 1
    if request.GET.get("fy"):
        fy = int(request.GET["fy"])
    else:
        fy = current_fy

    valid_periods = (
        Session.objects.with_period_info()
        .for_year(fy, fiscal=True)
        .order_by()
        .values_list("period", flat=True)
        .distinct()
    )
    periods = collections.OrderedDict(
        (k, v) for k, v in PERIOD_CHOICES if k in valid_periods
    )
    period = request.GET.get("period", None)

    # If you switch to a different year that doesn't have the currently period
    # (eg, not having SCHOOL_YEAR before 2022), just set period to None, since
    # it won't be available in the list of periods.
    if period not in periods:
        period = None

    clubs_list = Club.objects.for_period(fy, period, fiscal=True)
    category_list = ClubCategory.objects.filter(clubs__in=clubs_list).distinct()
    registrations = (
        StudentRegistration.objects.for_period(fy, period, fiscal=True)
        .exclude(status="CANCELED")
        .select_related("student")
    )

    selected_category = None
    if request.GET.get("category"):
        selected_category = ClubCategory.objects.get(pk=request.GET["category"])
        registrations = registrations.filter(club__category=selected_category)
        clubs_list = clubs_list.filter(category=selected_category)

    location_types = LOCATION_TYPE_CHOICES
    selected_location_type = request.GET.get("location_type")
    if selected_location_type:
        registrations = registrations.filter(
            club__location__type=selected_location_type
        )
        clubs_list = clubs_list.filter(location__type=selected_location_type)

    segments = models.StudentSegment.objects.all()
    selected_segment = None
    if request.GET.get("segment"):
        selected_segment = segments.get(pk=request.GET["segment"])
        # TODO: This will filter by student's CURRENT segment, not segment at time of
        # registration. This may or may not be what we want, but we need to make that clear
        # somehow on the demographics page.
        registrations = registrations.filter(student__segments=selected_segment)

    selected_club = None
    if request.GET.get("club"):
        selected_club = Club.objects.get(pk=request.GET["club"])
        registrations = registrations.filter(club=selected_club)

    counts = {}

    total_registrations = registrations.count()
    total_students = registrations.values("student_id").distinct().count()

    # gender
    qs = registrations.values("student__gender").annotate(
        count=Count("student_id", distinct=True)
    )
    results = []
    choices = dict(constants.GENDER_CHOICES)
    for row in qs:
        choice = row["student__gender"]
        count = row["count"]
        results.append(
            {
                "value": choice,
                "label": choices.get(choice, "Unspecified"),
                "count": count,
                "percent": percent(count, total_students),
            }
        )
    counts["gender"] = results

    # race & ethnicity breakdown
    results = []
    race_choices = constants.RACE_CHOICES + (("", "Unspecified"),)
    for race, race_label in race_choices:
        # KLUDGE: query strategy could be optimized here
        race_qs = registrations.filter(student__race=race)
        count = race_qs.values("student_id").distinct().count()
        ethnicity_qs = race_qs.values("student__ethnicity").annotate(
            count=Count("student_id", distinct=True)
        )
        ethnicities = {}
        for row in ethnicity_qs:
            ethnicities[row["student__ethnicity"] or "UNSPECIFIED"] = row["count"]

        results.append(
            {
                "value": race,
                "label": race_label,
                "count": count,
                "percent": percent(count, total_students),
                "ethnicities": ethnicities,
            }
        )

    def race_sort(item):
        # sort by count, but put "UNSPECIFIED" last
        if item["value"] == "":
            return -1
        return item["count"]

    counts["race_ethnicity"] = sorted(results, key=race_sort, reverse=True)

    # combined race & ethnicity
    # (this pulls out hispanic into its own category alongside race)
    combined_results = []
    hispanic_total = 0
    for result in results:
        hispanic_count = result["ethnicities"].get("HISPANIC", 0)
        count = result["count"] - hispanic_count
        combined_results.append(
            {
                "value": result["value"],
                "label": result["label"],
                "count": count,
                "percent": percent(count, total_students),
            }
        )
        hispanic_total += hispanic_count

    combined_results.append(
        {
            "value": "HISPANIC",
            "label": "Hispanic",
            "count": hispanic_total,
            "percent": percent(hispanic_total, total_students),
        }
    )
    counts["race_ethnicity_combined"] = sorted(
        combined_results, key=race_sort, reverse=True
    )

    # FIXME:
    # ------
    # Age and Grade are determined based on age/grade at time of
    # registration. This means the totals are counted on a per-registration
    # basis, which may count a distinct student multiple times in the same
    # time period (eg, a student that signs up for summer as well as school
    # year).
    #
    # To fix this we would need to determine how we want to represent the
    # age/grade of the student for a selected period
    # ------

    # ages (as of registration date)
    ages = collections.Counter(r.student.get_age(r.date) for r in registrations)
    results = []
    for age, count in sorted(ages.items(), key=lambda t: t[0]):
        results.append(
            {
                "value": age,
                "label": age,
                "count": count,
                "percent": percent(count, total_students),
            }
        )
    counts["age"] = results

    # grade (as of registration date)
    grades = collections.Counter(r.student_grade for r in registrations)
    results = []
    for grade, count in sorted(grades.items(), key=lambda t: t[0] and t[0] or 0):
        results.append(
            {
                "value": grade,
                "label": grade,
                "count": count,
                "percent": percent(count, total_students),
            }
        )
    counts["grade"] = results

    cal_year = str(fy - 1)
    if period == "SPRING":
        cal_year = str(fy)
    elif period == "SCHOOL_YEAR":
        cal_year = f"{cal_year}-{fy}"

    report_title = "Student Demographics: "
    if period:
        report_title += f"{periods[period]} {cal_year}"
    else:
        report_title += f"FY {fy}"

    if selected_club:
        report_title += f" - {selected_club.name}"
    elif selected_category:
        report_title += f" - {selected_category.name}"
    context = {
        "fy": fy,
        "years": sorted(range(start_year, end_year + 1), reverse=True),
        "period": period,
        "periods": periods,
        "report_title": report_title,
        "registrations": registrations,
        "clubs_list": clubs_list,
        "category_list": category_list,
        "selected_club": selected_club,
        "selected_category": selected_category,
        "location_types": location_types,
        "selected_location_type": selected_location_type,
        "segments": segments,
        "selected_segment": selected_segment,
        "total_students": total_students,
        "total_registrations": total_registrations,
        "counts": counts,
    }

    return render(request, "admin/students/demographics.html", context)


@register_appview(
    "students",
    "duplicates/$",
    "find_duplicate_students",
    "Find duplicate students",
    "Data management",
)
def find_dupcliate_students_view(request):
    if request.method == "POST" and request.POST.get("action_merge"):
        student_ids = request.POST.getlist("student_ids")
        next = reverse("admin:students_find_duplicate_students")
        url = reverse("admin:students_student_merge")
        url += "?students={}&next={}".format(",".join(student_ids), next)
        return http.HttpResponseRedirect(url)
    elif request.method == "POST" and request.POST.get("action_mark_not_duplicate"):
        student_ids = set([int(id) for id in request.POST.getlist("student_ids")])
        students = models.Student.objects.filter(pk__in=student_ids)
        for student in students:
            not_duplicate = student_ids ^ set([student.id])
            student.not_duplicate = list(set(student.not_duplicate) | not_duplicate)
            student.save()
        return http.HttpResponseRedirect(".")

    duplicates = find_duplicate_students()
    context = {"duplicates": list(duplicates)}
    return render(request, "admin/students/find_duplicate_students.html", context)
