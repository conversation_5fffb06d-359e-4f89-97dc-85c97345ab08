from django import forms
from django.utils.translation import gettext_lazy as _

from clubs import utils
from clubs.models import Location
from hive.forms import BaseForm
from students import constants
from students.models import Student


class StudentForm(BaseForm, forms.ModelForm):
    birth_date = forms.DateField(label=_("Birth date (mm/dd/yyyy)"))
    grade_level = forms.ChoiceField(label=_("Grade level"), required=False)
    previous_activities = forms.CharField(widget=forms.Textarea, required=False)
    possible_conflicts = forms.CharField(widget=forms.Textarea, required=False)
    annual_income = forms.ChoiceField(
        label=_("Total annual household income"), required=False
    )
    num_household_members = forms.IntegerField(
        label=_("Total number of household members"),
        required=False,
        help_text="(include yourself)",
    )
    school = forms.ChoiceField(label=_("School"), required=False)
    school_student_id = forms.CharField(
        label=_("School student id"),
        required=False,
        help_text=(_("Student ID issued by school or district")),
    )
    other_info = forms.CharField(widget=forms.Textarea, required=False)
    postal_code = forms.CharField(required=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        grade_label = _("Grade level ({year} school year)*").format(
            year=utils.get_current_school_year_description()
        )
        grade_choices = [("", "---------")] + utils.get_grade_choices()
        self.fields["grade_level"].label = grade_label
        self.fields["grade_level"].choices = grade_choices

        school_choices = [("", "---------")]
        for school in Location.objects.filter(type="SCHOOL"):
            school_choices.append((school.pk, school.name))
        school_choices.append(("OTHER", "Other"))

        self.fields["school"].label += "*"
        self.fields["school"].choices = school_choices
        self.fields["school_other"].label = _("School name*")
        self.fields["school_other"].help_text = _(
            'If homeschooled enter "Homeschool", or "N/A"'
        )
        self.fields["annual_income"].choices = (("", "---------"),) + tuple(
            constants.INCOME_CHOICES
        )
        self.fields["preferred_pronouns"].help_text = _(
            "If specified, pronouns will be displayed on the club roster, visible only to mentors "
            "and students."
        )

        instance = kwargs.get("instance")
        if self.instance:
            if self.instance.grade:
                self.initial["grade_level"] = str(instance.grade)

            if not self.instance.school and self.instance.school_other:
                self.initial["school"] = "OTHER"

            if self.instance.annual_income_min is not None:
                current_min = self.instance.annual_income_min
                current_max = self.instance.annual_income_max
                for choice_val, label in constants.INCOME_CHOICES:
                    min, max = choice_val.split("-")
                    min = int(min)
                    max = max and int(max) or 99999999999999
                    if current_min >= min and current_max <= max:
                        self.initial["annual_income"] = choice_val

        self.update_fields()

    def clean(self):
        # grade must be set manually, only if birth date is given (grade is calculated based on
        # birth date and grade_offset)
        super().clean()

        self.instance.birth_date = self.cleaned_data.get("birth_date")

        # School OTHER won't work w/ model FK
        school = self.cleaned_data.get("school")
        if school == "OTHER" or school == "":
            self.cleaned_data["school"] = None
        else:
            self.cleaned_data["school"] = Location.objects.get(pk=school)

        if not school and not self.cleaned_data.get("school_other"):
            raise forms.ValidationError("Please select your school")
        elif school == "OTHER" and not self.cleaned_data.get("school_other"):
            raise forms.ValidationError("Please provide your school name")

        grade_level = self.cleaned_data.get("grade_level")
        if grade_level:
            if not self.instance.birth_date:
                raise forms.ValidationError("Birth date must be provided")
            self.instance.grade = grade_level
        else:
            self.instance.grade = None

        annual_income = self.cleaned_data.get("annual_income")
        if annual_income:
            income_min, income_max = annual_income.split("-")
            self.instance.annual_income_min = int(income_min)
            self.instance.annual_income_max = income_max and int(income_max) or None
        else:
            self.instance.annual_income_min = None
            self.instance.annual_income_max = None

    class Meta:
        model = Student
        fields = [
            "first_name",
            "last_name",
            "birth_date",
            "grade_level",
            "school",
            "school_other",
            "school_student_id",
            "postal_code",
            "county",
            "shirt_size",
            "race",
            "ethnicity",
            "gender",
            "preferred_pronouns",
            "annual_income",
            "num_household_members",
        ]
