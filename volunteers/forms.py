from django import forms
from django.conf import settings
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from clubs.constants import PERIOD_CHOICES
from hive.forms import BaseForm
from hive.utils import get_age
from hive.widgets import ProfilePhotoInput
from questionnaires.forms import QuestionnaireModelForm
from users import forms as user_forms
from volunteers import constants, models


class UserCreationForm(BaseForm, user_forms.UserCreationForm):
    pass


class UserChangeForm(BaseForm, user_forms.UserChangeForm):
    pass


class VolunteerForm(QuestionnaireModelForm):
    questionnaire_slug = "volunteer-profile"

    organization = forms.CharField()

    def __init__(self, *args, **kwargs):
        self.language_fields = []
        instance = kwargs.get("instance", None)
        initial = kwargs.get("initial", {})
        self.base_fields["email"].required = True
        self.base_fields["birth_date"].required = True

        if instance and instance.organization:
            initial["organization"] = str(instance.organization.pk)

        # Add fields for language
        for lang_code, lang_name in settings.LANGUAGES:
            initial_lang = None
            if instance and isinstance(instance.languages, dict):
                initial_lang = instance.languages.get(lang_code)
            choices = ((None, ""),) + constants.LANGUAGE_PROFICENCY_CHOICES
            field = forms.ChoiceField(
                label=lang_name, required=True, choices=choices, initial=initial_lang
            )
            self.base_fields["lang_{}".format(lang_code)] = field

        super().__init__(*args, **kwargs)

        self.fields["preferred_pronouns"].help_text = _(
            "If specified, pronouns will be displayed on the club roster, visible only to mentors "
            "and students."
        )
        self.fields["organization_other"].help_text = (
            'You can also enter "Self-employed" or "Prefer not to say".'
        )

        for lang_code, lang_name in settings.LANGUAGES:
            self.language_fields.append(self["lang_{}".format(lang_code)])

    def clean(self, *args, **kwargs):
        organization = self.cleaned_data.get("organization")
        if organization == "":
            raise forms.ValidationError({"organization": ["This field is required"]})
        elif organization == "OTHER":
            self.cleaned_data["organization"] = None
            if not self.cleaned_data.get("organization_other"):
                raise forms.ValidationError(
                    {"organization_other": ["This field is required"]}
                )
        else:
            try:
                org = models.Organization.objects.get(pk=organization)
            except models.Organization.DoesNotExist:
                raise forms.ValidationError(
                    {"organization": ["Invalid choice for organization"]}
                )
            self.cleaned_data["organization"] = org

        super().clean(*args, **kwargs)

    def save(self, commit=True):
        instance = super().save(commit=False)
        languages = {}
        for lang_code, lang_name in settings.LANGUAGES:
            key = "lang_{}".format(lang_code)
            languages[lang_code] = self.cleaned_data[key]
        instance.languages = languages
        instance.last_updated = timezone.now()
        if commit:
            instance.save()
        return instance

    def clean_birth_date(self):
        birth_date = self.cleaned_data["birth_date"]
        if get_age(birth_date) < 18:
            raise forms.ValidationError(
                "You must be 18 or older to volunteer with Bold Idea."
            )
        return birth_date

    class Meta:
        model = models.Volunteer
        fields = [
            "photo",
            "first_name",
            "last_name",
            "address1",
            "address2",
            "city",
            "state",
            "postal_code",
            "email",
            "phone",
            "alt_phone",
            "is_student",
            "organization",
            "organization_other",
            "birth_date",
            "shirt_size",
            "race",
            "ethnicity",
            "gender",
            "preferred_pronouns",
        ]
        widgets = {
            "photo": ProfilePhotoInput(
                message_default=(
                    "Click to change your profile photo. Make sure it clearly shows your face!"
                )
            ),
            "birth_date": forms.DateInput(
                attrs={
                    # data-placeholder will only show placeholder when focused
                    # (see hive/static/hive/js/init.js)
                    "data-placeholder": "MM/DD/YYYY"
                }
            ),
        }


class VolunteerSignupForm(QuestionnaireModelForm):
    questionnaire_data_field = "form_data"
    volunteer_period = forms.ChoiceField(required=False, widget=forms.RadioSelect)
    fall_signup_deadline = forms.DateField(required=False)
    agreement = forms.BooleanField(required=True)
    opt_in = forms.BooleanField(required=False, label="Subscribe to our mailing list?")

    if settings.VOLUNTEER_SIGNUP_REQUIRE_LEGAL_NAME_AND_DOB:
        birth_date = forms.DateField(required=True)
        full_legal_name = forms.CharField(required=True)

    def __init__(self, *args, **kwargs):
        required_kwargs = ("opportunity", "volunteer", "selected_clubs")
        for arg in required_kwargs:
            try:
                setattr(self, arg, kwargs.pop(arg))
            except KeyError:
                raise ValueError(f"The `{arg}` keyword argument is required")

        if self.opportunity.questionnaire:
            self.questionnaire_slug = self.opportunity.questionnaire.slug

        # Show period option if applicable (only applies to signups with 1 selected club)
        if self.opportunity.has_attendances and len(self.selected_clubs) == 1:
            club = self.selected_clubs[0]
            sessions = club.sessions.with_semester()
            all_dates = (
                f"{list(sessions)[0].date:%b %-d} - {list(sessions)[-1].date:%b %-d}"
            )
            period_choices = []

            if club.period == "SCHOOL_YEAR":
                fall_sessions = list(sessions.filter(semester="FALL"))
                spring_sessions = list(sessions.filter(semester="SPRING"))

                fall_available = (
                    timezone.now().date() < club.volunteer_fall_signup_deadline
                )

                if fall_available and len(fall_sessions):
                    period_choices.append(
                        (
                            "SCHOOL_YEAR",
                            mark_safe(f"Fall and Spring <em>({all_dates})</em>"),
                        )
                    )
                    fall_dates = f"{fall_sessions[0].date:%b %-d} - {fall_sessions[-1].date:%b %-d}"
                    period_choices.append(
                        ("FALL", mark_safe(f"Fall only <em>({fall_dates})</em>"))
                    )

                if len(spring_sessions):
                    spring_dates = f"{spring_sessions[0].date:%b %-d} - {spring_sessions[-1].date:%b %-d}"
                    period_choices.append(
                        ("SPRING", mark_safe(f"Spring only <em>({spring_dates})</em>"))
                    )
            else:
                # Only one choice will be available
                label = dict(PERIOD_CHOICES).get(club.period)
                period_choices = [(club.period, f"{label} <em>({all_dates})</em>")]

            self.base_fields["volunteer_period"].choices = period_choices

        super().__init__(*args, **kwargs)

        # Use questionnaire data stored on profile to pre-populate the signup questionnaire
        profile_questionnaire = self.volunteer.extra_data.get("questionnaire", {})
        self.set_initial_questionnaire_data(profile_questionnaire)

    def clean_birth_date(self):
        birth_date = self.cleaned_data["birth_date"]
        if get_age(birth_date) < 13:
            raise forms.ValidationError(
                "You must be over 13 to volunteer with Bold Idea."
            )
        return birth_date

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.volunteer = self.volunteer
        instance.opportunity = self.opportunity

        # Copy questionnaire answers to volunteer.extra_data
        profile_questionnaire = self.volunteer.extra_data.get("questionnaire", {})
        profile_questionnaire.update(instance.form_data.get("questionnaire", {}))
        instance.volunteer.extra_data["questionnaire"] = profile_questionnaire

        if commit:
            instance.volunteer.save()
            instance.save()

        return instance

    class Meta:
        model = models.VolunteerSignup
        exclude = ["volunteer", "opportunity", "form_data", "date"]
