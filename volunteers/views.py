import json
from urllib.parse import quote as quote_url

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate, login
from django.contrib.auth.views import login_required
from django.core.mail import send_mail
from django.db import transaction
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from django.template.loader import get_template
from django.urls import reverse
from django.utils import timezone

from clubs.models import Club, Session, VolunteerAttendance
from hive.utils import json_defaults, mc_subscribe
from volunteers import forms, models, utils


def require_profile(request, update=False):
    next = quote_url(request.get_full_path())
    if not request.user.is_authenticated:
        return HttpResponseRedirect("{}?next={}".format(reverse("login"), next))
    if update:
        messages.add_message(
            request,
            messages.INFO,
            "Welcome back! Please take a moment to make sure your profile information is "
            "up-to-date.",
        )
    else:
        messages.add_message(
            request,
            messages.INFO,
            "Please complete your volunteer profile before continuing.",
        )
    return HttpResponseRedirect(
        "{}?next={}".format(reverse("volunteers:profile"), next)
    )


def profile_required(view):
    @login_required(login_url=settings.LOGIN_URL)
    def wrapper(request, *args, **kwargs):
        profile = get_profile(request)
        if not profile:
            return require_profile(request)
        if relativedelta(timezone.now(), profile.last_updated).years > 0:
            return require_profile(request, update=True)
        return view(request, *args, **kwargs)

    return wrapper


def get_profile(request):
    if request.user and request.user.is_authenticated:
        try:
            return models.Volunteer.objects.get(user=request.user)
        except models.Volunteer.DoesNotExist:
            pass
    return None


def opportunities(request):
    opportunities = models.VolunteerOpportunity.objects.all()

    if "preview" in request.GET:
        opportunities = opportunities.filter(status__in=("PUBLISHED", "DRAFT"))
    else:
        opportunities = opportunities.filter(status="PUBLISHED")

    context = {"preview": "preview" in request.GET, "opportunities": opportunities}

    return render(request, "volunteers/opportunities.html", context)


@transaction.atomic
def profile(request):
    user = request.user.is_authenticated and request.user or None
    contact = user and user.contact or None
    profile = get_profile(request)
    next = request.GET.get("next")
    initial = {}

    # If contact exists, use that as base for volunteer
    if contact and not profile:
        profile = models.Volunteer(contact_ptr=contact)
        profile.__dict__.update(contact.__dict__)

    if user:
        UserForm = forms.UserChangeForm
    else:
        UserForm = forms.UserCreationForm

    if request.method == "POST":
        form = forms.VolunteerForm(
            request.POST, request.FILES, instance=profile, initial=initial
        )
        user_form = UserForm(request.POST, instance=user)
        next = request.POST.get("next")
        if form.is_valid() and user_form.is_valid():
            # Save the user, then associate the user w/ the volunteer object
            volunteer = form.save()
            user = user_form.save(commit=False)
            user.contact = volunteer
            user.save()

            # log the user in
            if not request.user.is_authenticated:
                user = authenticate(
                    email=form.cleaned_data["email"],
                    password=user_form.cleaned_data["password1"],
                )
                login(request, user)

            # redirect
            if next:
                redirect = next
            else:
                redirect = reverse("volunteers:opportunities")
            return HttpResponseRedirect(redirect)
        else:
            # form has errors
            messages.add_message(
                request,
                messages.ERROR,
                "There was an error with your submission. Please check the "
                "form for errors.",
            )
    else:
        user_form = UserForm(instance=user)
        form = forms.VolunteerForm(instance=profile, initial=initial)

    context = {
        "profile": profile,
        "form": form,
        "user_form": user_form,
        "user": user,
        "next": next,
        "languages": settings.LANGUAGES,
        "organizations": models.Organization.objects.order_by("name"),
    }
    return render(request, "volunteers/profile.html", context)


@transaction.atomic
def signup(request, slug):
    opportunities = models.VolunteerOpportunity.objects.filter(
        status__in=("UNLISTED", "PUBLISHED")
    )
    opportunity = get_object_or_404(opportunities, slug=slug)
    volunteer = get_profile(request)

    # order clubs by # volunteers so that greatest need is at the top
    clubs = (
        opportunity.clubs.with_volunteer_stats()
        .select_related("location")
        .order_by("category__name", "name", "-remaining_volunteers_needed")
    )

    map_locations = [p.location for p in clubs if p.location and p.location.address1]

    selected_club_ids = request.GET.getlist("club", request.POST.getlist("club"))
    selected_club_ids = [
        id for id in selected_club_ids if id
    ]  # filter out empty values
    selected_clubs = Club.objects.filter(pk__in=selected_club_ids).select_related(
        "location"
    )

    if not selected_clubs and clubs.count() == 1 and opportunity.skip_club_selection:
        selected_clubs = clubs
    else:
        clubs = clubs.exclude(hide_on_volunteer_signup=True)

    select_multiple = False

    working_with_minors = opportunity.role in (
        "MENTOR",
        "FLOATER",
        "TEAM_CAPTAIN",
        "SCHOOL_LIAISON",
        "GUEST",
    )

    online = any(club.online for club in clubs)

    if opportunity.role == "FLOATER":
        select_multiple = True

    context = {
        "opportunity": opportunity,
        "clubs": clubs,
        "online": online,
        "map_locations": map_locations,
        "select_multiple": select_multiple,
        "selected_clubs": selected_clubs,
        "working_with_minors": working_with_minors,
        "show_legal_name_and_dob": settings.VOLUNTEER_SIGNUP_REQUIRE_LEGAL_NAME_AND_DOB,
        "GOOGLE_MAPS_API_KEY": settings.GOOGLE_MAPS_API_KEY,
    }

    # whether or not the opportunity has clubs associated with it
    has_clubs = clubs.count() > 0

    # If club hasn't selected, go ahead and render the selection page (no need for the form yet)
    if has_clubs and len(selected_clubs) == 0:
        return render(request, "volunteers/signup.html", context)

    if not volunteer and (len(selected_clubs) > 0 or not has_clubs):
        return require_profile(request)

    # The template needs to know which of the selected clubs the volunteer has already signed up
    already_signed_up = [p for p in selected_clubs if volunteer in p.volunteers.all()]
    context["already_signed_up"] = already_signed_up

    if selected_clubs.count() > 0 and len(already_signed_up) == selected_clubs.count():
        # Show the "already signed up" page
        return render(request, "volunteers/already_signed_up.html", context)

    requirements = []
    if selected_clubs.count() > 0:
        # Get all requirements needed by the selected clubs
        requirements = utils.get_club_requirements(volunteer, *selected_clubs)

        for club in selected_clubs:
            # Make sure volunteer meets age requirements
            if club.min_volunteer_age and volunteer.age < club.min_volunteer_age:
                context["selected_clubs"] = selected_clubs.filter(
                    min_volunteer_age__gt=volunteer.age
                )
                context["min_age"] = min(p.min_volunteer_age for p in clubs)
                return render(request, "volunteers/age_gate.html", context)

    context["requirements"] = [
        r for r in requirements if r.requirement_type.responsible_party == "VOLUNTEER"
    ]

    context["background_check_required"] = any(
        r.requirement_type.slug == "standard-background-check" for r in requirements
    )

    initial = {
        "volunteer": volunteer,
        "opportunity": opportunity,
        "selected_clubs": selected_clubs,
    }

    if settings.VOLUNTEER_SIGNUP_REQUIRE_LEGAL_NAME_AND_DOB:
        initial["full_legal_name"] = volunteer.full_legal_name
        initial["birth_date"] = volunteer.birth_date

    # Set initial volunteer period
    if opportunity.has_attendances and len(selected_clubs) == 1:
        if selected_clubs[0].period == "SCHOOL_YEAR":
            if timezone.now().date() < selected_clubs[0].volunteer_fall_signup_deadline:
                initial["volunteer_period"] = "SCHOOL_YEAR"
            else:
                # In this case, we can only sign up for spring, so we don't show the option.
                initial["volunteer_period"] = "SPRING"
        else:
            initial["volunteer_period"] = selected_clubs[0].period

    if request.method == "POST" and request.POST.get("submit-signup-form"):
        form = forms.VolunteerSignupForm(
            request.POST,
            opportunity=opportunity,
            volunteer=volunteer,
            selected_clubs=selected_clubs,
            initial=initial,
        )
        if form.is_valid():
            # Create the signup instance
            signup = form.save(commit=False)

            # Add form data for historical purposes
            form_data = signup.form_data or {}
            form_data.update(form.cleaned_data)
            form_data["opportunity"] = signup.opportunity.slug
            form_data["selected_clubs"] = [p.club_code for p in selected_clubs.all()]
            signup.form_data = json.loads(json.dumps(form_data, default=json_defaults))

            signup.save()
            signup.volunteer.save()

            if settings.VOLUNTEER_SIGNUP_REQUIRE_LEGAL_NAME_AND_DOB:
                # Update the legal name / birth date on volunteer
                volunteer.full_legal_name = form.cleaned_data["full_legal_name"]
                volunteer.birth_date = form.cleaned_data["birth_date"]
                volunteer.save()

            # Save requirements
            for requirement in requirements:
                requirement.save()

            # Create assignments
            if opportunity.role:
                for club in selected_clubs:
                    # Automatically create assignment and associate it with the signup
                    assignment, created = (
                        models.VolunteerAssignment.objects.get_or_create(
                            club=club,
                            volunteer=volunteer,
                            defaults={"role": opportunity.role, "signup": signup},
                        )
                    )
                    assignment.update_requirements()
                    assignment.update_verification()

            if opportunity.has_attendances:
                # Add attendance records
                selected_sessions = Session.objects.filter(club__in=selected_clubs)

                # If a semester was selected, use only those sessions
                volunteer_period = form.cleaned_data.get("volunteer_period")
                if volunteer_period in ("SPRING", "FALL"):
                    selected_sessions = selected_sessions.with_semester().filter(
                        semester=volunteer_period
                    )

                # Create attendance records
                for session in selected_sessions:
                    # An attendance record represents the expectation that a person is present
                    # at a given session.
                    #
                    # Create attendance if it has not been created yet
                    VolunteerAttendance.objects.get_or_create(
                        session=session, volunteer=volunteer
                    )

            send_signup_confirmation_emails(request, signup)

            # opt_in
            if form.cleaned_data.get("opt_in"):
                mc_subscribe(
                    volunteer.email,
                    first_name=volunteer.first_name,
                    last_name=volunteer.last_name,
                    silent=True,
                )

            redirect = reverse(
                "volunteers:signup-complete",
                kwargs={"slug": slug, "signup_id": signup.pk},
            )
            return HttpResponseRedirect(redirect)
        else:
            # form has errors
            messages.add_message(
                request,
                messages.ERROR,
                "There was an error with your submission. Please check the "
                "form for errors.",
            )
    else:
        form = forms.VolunteerSignupForm(
            opportunity=opportunity,
            volunteer=volunteer,
            selected_clubs=selected_clubs,
            initial=initial,
        )

    context["form"] = form

    return render(request, "volunteers/signup.html", context)


def signup_complete(request, slug, signup_id):
    opportunity = get_object_or_404(models.VolunteerOpportunity, slug=slug)
    signup = get_object_or_404(models.VolunteerSignup, pk=signup_id)
    requirements = signup.requirements.pending().filter(
        requirement_type__responsible_party="VOLUNTEER"
    )
    assignments = signup.assignments.all()
    clubs = [s.club_id for s in assignments]
    team_captains = (
        models.VolunteerAssignment.objects.select_related("volunteer", "club")
        .filter(club_id__in=clubs, role="TEAM_CAPTAIN")
        .distinct()
    )

    context = {
        "opportunity": opportunity,
        "signup": signup,
        "assignments": assignments,
        "assignment": len(assignments) and assignments[0] or None,
        "team_captains": team_captains,
        "requirements": requirements,
        "training_sessions": signup.training_sessions,
    }
    return render(request, "volunteers/signup_complete.html", context)


def send_signup_confirmation_emails(request, signup):
    # Volunteer email (html & plaintext)
    subject = (
        "Your Bold Idea volunteer registration has been received! " "Details inside."
    )
    requirements = signup.requirements.pending().filter(
        requirement_type__responsible_party="VOLUNTEER"
    )
    dashboard_view_url = reverse(
        "dashboard:volunteer-details", kwargs={"volunteer_id": signup.volunteer.id}
    )
    admin_change_url = reverse(
        "admin:volunteers_volunteersignup_change", args=[signup.id]
    )
    assignments = signup.assignments.all()
    clubs = Club.objects.filter(pk__in=[s.club_id for s in assignments])
    team_captains = (
        models.VolunteerAssignment.objects.select_related("volunteer", "club")
        .filter(club__in=clubs, role="TEAM_CAPTAIN")
        .distinct()
    )
    context = {
        "no_js": True,
        "signup": signup,
        "assignments": assignments,
        "team_captains": team_captains,
        "requirements": requirements,
        "training_sessions": signup.training_sessions,
        "details_url_admin": request.build_absolute_uri(admin_change_url),
        "details_url_dashboard": request.build_absolute_uri(dashboard_view_url),
        "clubs": clubs,
    }
    plain_message = get_template("volunteers/signup_complete_email.txt").render(context)
    html_message = get_template("volunteers/signup_complete_email.html").render(context)
    send_mail(
        subject,
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [signup.volunteer.email],
        html_message=html_message,
    )

    # Admin notification (plaintext)
    notification_msg = get_template("volunteers/signup_notification_admin.txt").render(
        context
    )
    notification_subj = 'A volunteer has applied up for "{}"'.format(
        signup.opportunity.name
    )

    admin_notification_recipients = signup.opportunity.notification_recipients.all()
    if admin_notification_recipients.count() > 0:
        admin_notification_recipients = [
            "{0.contact.name} <{0.email}>".format(c)
            for c in admin_notification_recipients
        ]
    else:
        admin_notification_recipients = settings.REGISTRATION_NOTIFICATION_RECIPIENTS
    send_mail(
        notification_subj,
        notification_msg,
        settings.DEFAULT_FROM_EMAIL,
        admin_notification_recipients,
    )

    # Team Captain notification (plainttext)
    notification_msg = get_template(
        "volunteers/signup_notification_team_captain.txt"
    ).render(context)
    notification_subj = 'A volunteer has applied up for "{}"'.format(
        signup.opportunity.name
    )
    # Get team_captains for relevant club
    team_captain_roles = models.VolunteerAssignment.objects.filter(
        club__in=clubs, role="TEAM_CAPTAIN"
    )
    notification_recipients = set(r.volunteer.user for r in team_captain_roles)
    if len(notification_recipients) > 0:
        notification_recipients = [
            "{0.contact.name} <{0.email}>".format(c) for c in notification_recipients
        ]
        send_mail(
            notification_subj,
            notification_msg,
            settings.DEFAULT_FROM_EMAIL,
            notification_recipients,
        )
