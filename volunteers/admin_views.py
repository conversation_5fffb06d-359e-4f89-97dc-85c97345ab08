import calendar
import collections
import csv
from datetime import date
from decimal import Decimal

from django import http
from django.db.models import Count, ExpressionWrapper, F, <PERSON>loatField, Q, Sum
from django.shortcuts import render
from django.urls import reverse

from clubs.constants import LOCATION_TYPE_CHOICES, PERIOD_CHOICES
from clubs.models import Club, ClubCategory, Session, VolunteerAttendance
from clubs.utils import get_current_school_year
from hive.admin import register_appview
from hive.utils import percent
from volunteers.models import Volunteer
from volunteers.utils import find_duplicate_volunteers


@register_appview(
    "volunteers", "hours/$", "volunteer_hours", "Volunteer hours", "Reports"
)
def hours_view(request, *args, **kwargs):
    start_year = Session.objects.with_period_info().order_by("date")[0].fiscal_year
    end_year = Session.objects.with_period_info().order_by("-date")[0].fiscal_year

    current_fy = get_current_school_year() + 1
    if request.GET.get("fy") == "ALL":
        fy = None
    elif request.GET.get("fy"):
        fy = int(request.GET["fy"])
    else:
        fy = current_fy

    period = request.GET.get("period", None)

    clubs_list = Club.objects.for_period(fy, period, fiscal=True)
    category_list = ClubCategory.objects.filter(clubs__in=clubs_list).distinct()

    annotations = {}

    # Initial querysets
    attendances = (
        VolunteerAttendance.objects.with_period_info()
        .filter(
            present=True,
            session__type="CLUB",
            session__canceled=False,
            session__club__canceled=False,
        )
        .exclude(assignment__role__in=("SCHOOL_LIAISON", "GUEST"))
    )

    sessions = Session.objects.with_period_info().filter(
        type="CLUB", canceled=False, club__canceled=False
    )

    # Filter querysets based on selected filters
    if fy is not None:
        sessions = sessions.filter(fiscal_year=fy)
        attendances = attendances.filter(fiscal_year=fy)

    selected_category = None
    if request.GET.get("category"):
        selected_category = ClubCategory.objects.get(pk=request.GET["category"])
        attendances = attendances.filter(session__club__category=selected_category)
        clubs_list = clubs_list.filter(category=selected_category)
        sessions = sessions.filter(club__category=selected_category)

    location_types = LOCATION_TYPE_CHOICES
    selected_location_type = request.GET.get("location_type")
    if selected_location_type:
        attendances = attendances.filter(
            session__club__location__type=selected_location_type
        )
        clubs_list = clubs_list.filter(location__type=selected_location_type)
        sessions = sessions.filter(club__location__type=selected_location_type)

    selected_club = None
    if request.GET.get("club"):
        selected_club = Club.objects.get(pk=request.GET["club"])
        attendances = attendances.filter(session__club=selected_club)
        sessions = sessions.filter(club=selected_club)

    # Get list of periods based on selected timeframe
    valid_periods = sessions.order_by().values_list("period", flat=True).distinct()
    periods = collections.OrderedDict(
        (k, v) for k, v in PERIOD_CHOICES if k in valid_periods
    )

    # Get span of months based on selected filters
    months = []

    first_session = None
    last_session = None
    sessions_start_year = None
    year_span = None

    if sessions.count() > 0:
        first_session = sessions[0]
        last_session = sessions.order_by("-date")[0]
        sessions_start_year = first_session.date.year
        year_span = last_session.date.year - first_session.date.year

    if fy is not None and year_span is not None:
        for i in range(
            first_session.date.month, last_session.date.month + (year_span * 12) + 1
        ):
            month = i % 12 or 12
            year = sessions_start_year + ((i - 1) // 12)
            months.append((year, month))

    month_names = [calendar.month_abbr[m] for y, m in months]

    for year, month in months:
        month_end_day = calendar.monthrange(year, month)[1]
        month_start = date(year, month, 1)
        month_end = date(year, month, month_end_day)
        annotations[f"month_{year}_{month}"] = Sum(
            ExpressionWrapper(
                F("session__duration") / Decimal("60.0"), output_field=FloatField()
            ),
            filter=Q(session__date__gte=month_start, session__date__lte=month_end),
        )

    month_cols = [f"month_{y}_{m}" for y, m in months]
    month_total_cols = [f"total_month_{y}_{m}" for y, m in months]

    # total hours per volunteer
    annotations["total"] = Sum(
        ExpressionWrapper(
            F("session__duration") / Decimal("60.0"), output_field=FloatField()
        )
    )

    volunteer_fields = (
        "volunteer_id",
        "volunteer__first_name",
        "volunteer__last_name",
        "volunteer__organization__name",
        "volunteer__organization_other",
    )

    order_by = ("volunteer__first_name", "volunteer__last_name")
    if request.GET.get("o"):
        order_by = request.GET["o"].split(",")
    volunteers = (
        attendances.values(*volunteer_fields)
        .annotate(**annotations)
        .order_by(*order_by)
    )

    # total hours per month
    aggregates = {}
    for i, col in enumerate(month_cols):
        aggregates[month_total_cols[i]] = Sum(month_cols[i])

    month_totals = volunteers.aggregate(**aggregates)
    if fy is not None:
        year_total = sum(month_totals[col] or 0 for col in month_total_cols)
    else:
        year_total = sum(row["total"] for row in volunteers)

    # Handle CSV download
    if request.GET.get("download"):
        response = http.HttpResponse(content_type="text/csv")
        if fy:
            filename = f"Volunteer hours FY{fy}.csv"
        else:
            filename = "Volunteer hours (all).csv"
        response["Content-Disposition"] = f"attachment; filename={filename}"
        csv_writer = csv.writer(response)
        csv_writer.writerow(["volunteer", "company"] + month_names + ["total"])
        for volunteer in volunteers:
            organization = volunteer["volunteer__organization__name"]
            if organization is None:
                organization = volunteer["volunteer__organization_other"] or ""
            csv_writer.writerow(
                [
                    f'{volunteer["volunteer__first_name"]} {volunteer["volunteer__last_name"]}'
                ]
                + [organization]
                + [volunteer[col] for col in month_cols]
                + [volunteer["total"]]
            )
        csv_writer.writerow(
            ["Total", ""]
            + [month_totals[col] for col in month_total_cols]
            + [year_total]
        )
        return response

    # Set report title
    if fy:
        cal_year = str(fy - 1)
        if period == "SPRING":
            cal_year = str(fy)
        elif period == "SCHOOL_YEAR":
            cal_year = f"{cal_year}-{fy}"

    report_title = "Volunteer Hours: "
    if fy and period:
        report_title += f"{periods[period]} {cal_year}"
    elif fy:
        report_title += f"FY {fy}"
    else:
        report_title += "All time"

    if selected_club:
        report_title += f" - {selected_club.name}"
    elif selected_category:
        report_title += f" - {selected_category.name}"

    context = {
        "fy": fy,
        "fy2": str(fy)[2:],
        "volunteers": volunteers,
        "months": months,
        "month_names": month_names,
        "years": range(start_year, end_year + 1),
        "period": period,
        "periods": periods,
        "month_cols": month_cols,
        "month_total_cols": month_total_cols,
        "month_totals": month_totals,
        "year_total": year_total,
        "clubs_list": clubs_list,
        "category_list": category_list,
        "selected_club": selected_club,
        "selected_category": selected_category,
        "location_types": location_types,
        "selected_location_type": selected_location_type,
        "report_title": report_title,
    }

    return render(request, "admin/volunteers/hours.html", context)


@register_appview(
    "volunteers",
    "demographics/$",
    "volunteer_demographics",
    "Volunteer demographics",
    "Reports",
)
def demographics_view(request, *args, **kwargs):
    from clubs.models import Club, ClubCategory
    from contacts import constants
    from volunteers.models import VolunteerAssignment

    start_year = Session.objects.with_period_info().order_by("date")[0].fiscal_year
    end_year = Session.objects.with_period_info().order_by("-date")[0].fiscal_year

    current_fy = get_current_school_year() + 1
    if request.GET.get("fy"):
        fy = int(request.GET["fy"])
    else:
        fy = current_fy

    valid_periods = (
        Session.objects.with_period_info()
        .for_year(fy, fiscal=True)
        .order_by()
        .values_list("period", flat=True)
        .distinct()
    )
    periods = collections.OrderedDict(
        (k, v) for k, v in PERIOD_CHOICES if k in valid_periods
    )
    period = request.GET.get("period", None)

    clubs_list = Club.objects.for_period(fy, period, fiscal=True)
    category_list = ClubCategory.objects.filter(clubs__in=clubs_list).distinct()
    assignments = (
        VolunteerAssignment.objects.for_period(fy, period, fiscal=True)
        .exclude(role__in=("SCHOOL_LIAISON", "GUEST"))
        .filter(club__canceled=False)
        .select_related("volunteer")
    )

    selected_category = None
    if request.GET.get("category"):
        selected_category = ClubCategory.objects.get(pk=request.GET["category"])
        assignments = assignments.filter(club__category=selected_category)
        clubs_list = clubs_list.filter(category=selected_category)

    location_types = LOCATION_TYPE_CHOICES
    selected_location_type = request.GET.get("location_type")
    if selected_location_type:
        assignments = assignments.filter(club__location__type=selected_location_type)
        clubs_list = clubs_list.filter(location__type=selected_location_type)

    selected_club = None
    if request.GET.get("club"):
        selected_club = Club.objects.get(pk=request.GET["club"])
        assignments = assignments.filter(club=selected_club)

    counts = {}

    total_volunteers = assignments.count()

    # gender
    qs = assignments.values("volunteer__gender").annotate(count=Count("*"))
    results = []
    choices = dict(constants.GENDER_CHOICES)
    for row in qs:
        choice = row["volunteer__gender"]
        count = row["count"]
        results.append(
            {
                "value": choice,
                "label": choices.get(choice, "Unspecified"),
                "count": count,
                "percent": percent(count, total_volunteers),
            }
        )
    counts["gender"] = results

    # race & ethnicity breakdown
    results = []
    race_choices = constants.RACE_CHOICES + (("", "Unspecified"),)
    for race, race_label in race_choices:
        # KLUDGE: query strategy could be optimized here
        race_qs = assignments.filter(volunteer__race=race)
        count = race_qs.count()
        ethnicity_qs = race_qs.values("volunteer__ethnicity").annotate(count=Count("*"))
        ethnicities = {}
        for row in ethnicity_qs:
            ethnicities[row["volunteer__ethnicity"] or "UNSPECIFIED"] = row["count"]

        results.append(
            {
                "value": race,
                "label": race_label,
                "count": count,
                "percent": percent(count, total_volunteers),
                "ethnicities": ethnicities,
            }
        )

    def race_sort(item):
        # sort by count, but put "UNSPECIFIED" last
        if item["value"] == "":
            return -1
        return item["count"]

    counts["race_ethnicity"] = sorted(results, key=race_sort, reverse=True)

    # combined race & ethnicity
    # (this pulls out hispanic into its own category alongside race)
    combined_results = []
    hispanic_total = 0
    for result in results:
        hispanic_count = result["ethnicities"].get("HISPANIC", 0)
        count = result["count"] - hispanic_count
        combined_results.append(
            {
                "value": result["value"],
                "label": result["label"],
                "count": count,
                "percent": percent(count, total_volunteers),
            }
        )
        hispanic_total += hispanic_count

    combined_results.append(
        {
            "value": "HISPANIC",
            "label": "Hispanic",
            "count": hispanic_total,
            "percent": percent(hispanic_total, total_volunteers),
        }
    )
    counts["race_ethnicity_combined"] = sorted(
        combined_results, key=race_sort, reverse=True
    )

    # ages
    def get_age_range(age):
        if age < 20:
            return "under 20"
        if age < 30:
            return "20-29"
        if age < 40:
            return "30-39"
        if age < 50:
            return "40-49"
        if age < 60:
            return "50-59"
        return "over 60"

    ages = collections.Counter(get_age_range(a.volunteer.age) for a in assignments)
    results = []
    for age, count in sorted(ages.items(), key=lambda t: t[0]):
        results.append(
            {
                "value": age,
                "label": age,
                "count": count,
                "percent": percent(count, total_volunteers),
            }
        )
    counts["age"] = results

    cal_year = str(fy - 1)
    if period == "SPRING":
        cal_year = str(fy)
    elif period == "SCHOOL_YEAR":
        cal_year = f"{cal_year}-{fy}"

    report_title = "Volunteer Demographics: "
    if period:
        report_title += f"{periods[period]} {cal_year}"
    else:
        report_title += f"FY {fy}"

    if selected_club:
        report_title += f" - {selected_club.name}"
    elif selected_category:
        report_title += f" - {selected_category.name}"

    context = {
        "fy": fy,
        "years": sorted(range(start_year, end_year + 1), reverse=True),
        "period": period,
        "periods": periods,
        "report_title": report_title,
        "assignments": assignments,
        "clubs_list": clubs_list,
        "category_list": category_list,
        "selected_club": selected_club,
        "selected_category": selected_category,
        "location_types": location_types,
        "selected_location_type": selected_location_type,
        "total_volunteers": total_volunteers,
        "counts": counts,
    }

    return render(request, "admin/volunteers/demographics.html", context)


@register_appview(
    "volunteers",
    "duplicates/$",
    "find_duplicate_volunteers",
    "Find duplicate volunteers",
    "Data management",
)
def find_dupcliate_volunteers_view(request):
    if request.method == "POST" and request.POST.get("action_merge"):
        volunteer_ids = request.POST.getlist("volunteer_ids")
        next = reverse("admin:volunteers_find_duplicate_volunteers")
        url = reverse("admin:volunteers_volunteer_merge")
        url += "?volunteers={}&next={}".format(",".join(volunteer_ids), next)
        return http.HttpResponseRedirect(url)
    elif request.method == "POST" and request.POST.get("action_mark_not_duplicate"):
        volunteer_ids = set([int(id) for id in request.POST.getlist("volunteer_ids")])
        volunteers = Volunteer.objects.filter(pk__in=volunteer_ids)
        for volunteer in volunteers:
            not_duplicate = volunteer_ids ^ set([volunteer.id])
            volunteer.not_duplicate = list(set(volunteer.not_duplicate) | not_duplicate)
            volunteer.save()
        return http.HttpResponseRedirect(".")

    duplicates = find_duplicate_volunteers()
    context = {"duplicates": list(duplicates)}
    return render(request, "admin/volunteers/find_duplicate_volunteers.html", context)
