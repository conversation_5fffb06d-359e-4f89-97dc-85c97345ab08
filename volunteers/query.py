from django.db import models
from django.db.models import Q
from django.utils import timezone

from clubs.query import PeriodQuerySetMixin, SemesterQuerySetMixin


class VolunteerRequirementQuerySet(PeriodQuerySetMixin, models.QuerySet):
    club_lookup = "assignment__club"

    def objects(self):
        return self.select_related("requirement_type")

    def current(self, as_of=None):
        """
        Returns existing requirements that have not expired
        """
        as_of = as_of or timezone.now().date()
        return self.filter(Q(expire_date__isnull=True) | Q(expire_date__gt=as_of))

    def valid_and_current(self, as_of=None):
        """
        Returns exsting unixpired requirements excluding the INVALID status
        """
        return self.current(as_of=as_of).exclude(status="INVALID")

    def pending(self):
        """
        Returns existing requirements that must be completed by someone
        """
        return (
            self.valid_and_current()
            .annotate(num_assignments=models.Count("assignments"))
            .filter(num_assignments__gt=0)
            .exclude(status="COMPLETED")
        )


class VolunteerAssignmentQuerySet(
    SemesterQuerySetMixin, PeriodQuerySetMixin, models.QuerySet
):
    semester_date_lookup = "club__start_date"
    club_lookup = "club"

    def verified(self):
        return self.filter(verified=True)
