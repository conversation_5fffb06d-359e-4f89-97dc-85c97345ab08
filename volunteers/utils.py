from django.contrib.admin.utils import NestedObjects
from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Count, Value
from django.db.models.functions.text import Concat, Lower, Transform

from clubs.models import Club, VolunteerAssignment, VolunteerAttendance
from clubs.utils import parse_period
from users.models import User
from volunteers import verification
from volunteers.models import VolunteerRequirement


def get_club_requirements(volunteer, *clubs):
    """
    Used to determine volunteer requirements needed for the given club(s) based on latest
    requirements data. This also may include requirements that have already been completed.

    Note that if a volunteer failed a past requirement (status=INVALID), it will be included as a
    new (unsaved) requirement in the result set.

    Also, If an existing completed requirement will expire during one of the clubs, it will be
    assumed expired, and a new requirement object will be created.

    Returns a list of `VolunteerRequirement` instances based on requirements of the given clubs.
    If a `VolunteerRequirement` already exists in the database and has not expired, the saved
    instance will be included in the list. Otherwise, a new, unsaved instance will be included.

    Therefore, this method may return a mix of saved/unsaved VolunteerRequirement instances.
    This is useful for getting an updated list of requirements before the assignment has been
    created, eg, during signup.

    """
    requirements = {}

    # Get all existing requirements that are not rejected
    existing = volunteer.requirements.exclude(status="INVALID")

    # Put the given clubs into a queryset annotated with the end date
    clubs = Club.objects.filter(id__in=[p.pk for p in clubs]).with_end_date()

    for club in clubs:
        if verification.is_exempt(volunteer, club):
            continue

        # Loop through each requirement type needed by the club
        for requirement_type in club.volunteer_requirements.all():
            # Find valid requirement from matching existing requirements
            matching_requirements = [
                r for r in existing if r.requirement_type == requirement_type
            ]
            for matching_req in matching_requirements:
                # Don't include existing requirements that will expire during the club
                if matching_req.is_expired(as_of=club.end_date):
                    continue
                requirements[requirement_type.id] = matching_req
                break

            if requirement_type.id not in requirements:
                # We couldn't find an existing valid requirement, so add a new one
                requirements[requirement_type.id] = VolunteerRequirement(
                    requirement_type=requirement_type, volunteer=volunteer
                )

    return list(requirements.values())


def get_volunteer_clubs(volunteer, period_str=None, as_of=None):
    show_all = volunteer.user.is_superuser

    if period_str:
        year, period = parse_period(period_str)
        clubs = Club.objects.for_period(year, period)
    else:
        # Get current clubs
        clubs = Club.objects.with_period_info().current(as_of=as_of)

    # Annotate and filter the queryset
    clubs = (
        clubs.with_next_session_times().with_volunteer_stats().with_attendance_stats()
    )
    assignments = volunteer.assignments.filter(club__in=clubs)

    if not show_all:
        # Only get assigned clubs
        clubs = clubs.filter(volunteer_assignments__in=assignments)

    # get club verifications
    club_verifications = verification.get_club_verifications(volunteer, *clubs)

    # order clubs by next session, not-canceled first
    clubs = clubs.order_by("canceled", "next_session_end")

    # annotate the clubs with volunteer roles and verification status
    for club in clubs:
        club_assignments = [a for a in assignments if a.club == club]
        club.roles = [a.role for a in club_assignments]
        club.roles_display = ", ".join(a.get_role_display() for a in club_assignments)
        club.volunteer_roles = [a.role for a in club_assignments]
        club.is_verified = club_verifications.get(club, False)

    return clubs


class MergeError(Exception):
    pass


class MergeIntegrityError(MergeError):
    def __init__(self, related_objects):
        self.related_objects = set(related_objects)
        msg = (
            "Record being merged cannot be deleted due to existing related "
            "objects that would also be deleted"
        )
        super().__init__(msg)


@transaction.atomic()
def merge_volunteer(volunteer, to_volunteer):
    delete_objects = [volunteer]

    # these are related objects that are deemed OK to delete
    delete_check_ignore_objects = delete_objects + []

    # related contact and user may be safely deleted, so we ignore them during the safety check
    if volunteer.contact_ptr_id:
        delete_check_ignore_objects.append(volunteer.contact_ptr)
    try:
        user = User.objects.get(contact_id=volunteer.contact_ptr_id)
        delete_check_ignore_objects.append(user)
        # allow lti_user deletion
        if hasattr(user, "lti_user"):
            delete_check_ignore_objects.append(user.lti_user)
        # allow auth_token deletion
        for auth_token in user.auth_tokens.all():
            delete_check_ignore_objects.append(auth_token)
    except User.DoesNotExist:
        pass

    def is_empty(val):
        return val is None or val == ""

    # merge empty fields
    for field in volunteer._meta.fields:
        if field.name == "id" or hasattr(field, "rel"):
            continue
        keep_val = getattr(to_volunteer, field.name, None)
        merge_val = getattr(volunteer, field.name, None)
        if is_empty(keep_val) and not is_empty(merge_val):
            setattr(to_volunteer, field.name, merge_val)

    # merge extra_data into a special json field, because I'm lazy.
    if volunteer.extra_data:
        if not to_volunteer.extra_data:
            to_volunteer.extra_data = {}
        if "merged_extra_data" not in to_volunteer.extra_data:
            to_volunteer.extra_data["merged_extra_data"] = []
        to_volunteer.extra_data["merged_extra_data"].append(volunteer.extra_data)

    # move attendances
    for attendance in volunteer.session_attendances.all():
        # check to see if attendance already exists on the TO volunteer
        try:
            dup_attendance = to_volunteer.session_attendances.get(
                session_id=attendance.session_id
            )
        except VolunteerAttendance.DoesNotExist:
            dup_attendance = None

        # If the attendance already exists, delete the old one to avoid duplicate error
        if dup_attendance:
            attendance.delete()
        else:
            attendance.volunteer_id = to_volunteer.id
            attendance.save()

    # move requirements
    for requirement in volunteer.requirements.all():
        requirement.volunteer_id = to_volunteer.id
        requirement.save()

    # move signups
    for signup in volunteer.signups.all():
        signup.volunteer_id = to_volunteer.id
        signup.save()

    # move assignments
    for assignment in volunteer.assignments.all():
        # check to see if assignment already exists on the TO volunteer
        try:
            dup_assignment = to_volunteer.assignments.filter(club_id=assignment.club_id)
        except VolunteerAssignment.DoesNotExist:
            dup_assignment = None

        # if the assignment already exists, delete old one to avoid duplicate error
        if dup_assignment:
            assignment.delete()
        else:
            assignment.volunteer_id = to_volunteer.id
            assignment.save()

    # move attended_training_sessions
    for attended_training_session in volunteer.attended_training_sessions.all():
        attended_training_session.volunteer_id = to_volunteer.id
        attended_training_session.save()

    # move facilitated_training_sessions
    for facilitated_training_session in volunteer.facilitated_training_sessions.all():
        facilitated_training_session.volunteer_id = to_volunteer.id
        facilitated_training_session.save()

    # move roadmaps
    for roadmap in volunteer.user.course_roadmaps.all():
        roadmap.user_id = to_volunteer.user.id
        roadmap.save()

    # move badge assertions
    for assertion in volunteer.user.issued_badge_assertions.all():
        assertion.added_by = to_volunteer.user
        assertion.save()

    # Make sure other objects are not deleted that we didn't expect
    related_objects = []
    collector = NestedObjects(using="default")
    collector.collect(delete_objects)
    for model, obj in collector.instances_with_model():
        if obj not in delete_check_ignore_objects:
            related_objects.append((model.__name__, obj))
    if len(related_objects):
        raise MergeIntegrityError(related_objects)

    # Delete remaining objects
    for obj in delete_objects:
        obj.delete()


class Left(Transform):
    template = "%(function)s(%(expressions)s, %(n)s)"
    function = "LEFT"
    lookup_name = "left"


def find_duplicate_volunteers():
    """
    Find possible duplicates based on first/last initial and DOB
    """
    from volunteers.models import Volunteer

    search_q = Lower(
        Concat(
            Left("first_name", n=1),
            Left("last_name", n=1),
            Value("-"),
            "birth_date",
            output_field=CharField(),
        )
    )
    duplicates = (
        Volunteer.objects.annotate(search=search_q)
        .values("search")
        .order_by("search")
        .annotate(count=Count("search"))
        .filter(count__gt=1)
    )
    for dup in duplicates:
        objects = Volunteer.objects.annotate(search=search_q).filter(
            search=dup["search"]
        )
        obj_ids = [obj.pk for obj in objects]
        dup_objects = []
        for obj in objects:
            if set(obj.not_duplicate) ^ (set(obj_ids) ^ set((obj.pk,))):
                dup_objects.append(obj)
        if len(dup_objects):
            yield (dup["search"], dup_objects)
