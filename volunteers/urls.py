from django.conf.urls import url
from django.contrib.auth.views import PasswordChangeView

from clubs.views import webcal
from volunteers import views

app_name = "volunteers"

urlpatterns = [
    url("^$", views.opportunities, name="opportunities"),
    url("^profile/$", views.profile, name="profile"),
    url("^profile/password/$", PasswordChangeView.as_view(), name="change-password"),
    url(
        "^(?P<slug>[^/]+)/complete/(?P<signup_id>[^/]+)/$",
        views.signup_complete,
        name="signup-complete",
    ),
    url("^(?P<slug>[^/]+)/$", views.signup, name="signup"),
    url("^webcal/(?P<cal_type>[^/]+)/(?P<obj_id>[^/]+).ics$", webcal, name="webcal"),
]
