import re

from django import forms, http
from django.contrib import admin, messages
from django.contrib.admin.widgets import AutocompleteSelect
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q
from django.shortcuts import render
from django.urls import reverse
from django.utils.safestring import mark_safe
from import_export import fields as resource_fields
from import_export import resources
from import_export.admin import ExportActionModelAdmin
from markdown import markdown
from markdown3_newtab import NewTabExtension

from clubs.admin import ClubCategory<PERSON>ilter, ClubFilter, ClubLocationFilter, PeriodFilter
from clubs.models import Location, Session, VolunteerAssignment, VolunteerAttendance
from hive.admin import ListingStatusFilter, admin_site, register
from hive.utils import dict_find, short_description
from volunteers.models import VolunteerRequirementType

from . import models
from .constants import VOLUNTEER_ROLE_CHOICES


class VolunteerOpportunityAdminForm(forms.ModelForm):
    class Meta:
        widgets = {
            "notification_recipients": AutocompleteSelect(
                models.VolunteerOpportunity._meta.get_field(
                    "notification_recipients"
                ).remote_field,
                admin_site,
                attrs={"data-dropdown-auto-width": "true", "data-width": "500px"},
            ),
        }


@register(models.VolunteerOpportunity)
class VolunteerOpportunityAdmin(admin.ModelAdmin):
    form = VolunteerOpportunityAdminForm
    icon = '<i class="material-icons">announcement</i>'
    list_display = ["name", "order", "status"]
    list_filter = [ListingStatusFilter]
    prepopulated_fields = {"slug": ["name"]}
    search_fields = [
        "name__unaccent",
        "slug",
        "clubs__name__unaccent",
        "clubs__club_code",
    ]
    autocomplete_fields = ["clubs", "notification_recipients"]
    fields = [
        ("name", "slug"),
        ("role", "photo"),
        "short_description",
        "description",
        "description_url",
        "thankyou_message",
        ("order", "status"),
        ("clubs", "skip_club_selection"),
        ("questionnaire", "contact"),
        "notification_recipients",
    ]
    actions = ["publish_action", "archive_action"]

    def publish_action(self, request, queryset):
        updated = queryset.update(status="PUBLISHED")
        self.message_user(request, "Published {} items".format(updated))

    publish_action.short_description = "Publish selected items"

    def archive_action(self, request, queryset):
        updated = queryset.update(status="ARCHIVED")
        self.message_user(request, "archived {} items".format(updated))

    archive_action.short_description = "Archive selected items"


class VolunteerRequirementForm(forms.ModelForm):
    def has_changed(self):
        # Should return True if data differs from initial. By always returning
        # true even unchanged inlines will get validated and saved.
        return True

    class Meta:
        model = models.VolunteerRequirement
        fields = ["requirement_type", "status", "completed_date", "notes"]
        widgets = {"notes": forms.TextInput}


class VolunteerRequirementInline(admin.TabularInline):
    model = models.VolunteerRequirement
    form = VolunteerRequirementForm
    fields = ["requirement_type", "status", "completed_date", "expire_date", "notes"]
    extra = 0

    def formfield_for_dbfield(self, *args, **kwargs):
        formfield = super().formfield_for_dbfield(*args, **kwargs)
        formfield.widget.can_delete_related = False
        formfield.widget.can_change_related = False
        formfield.widget.can_add_related = False
        return formfield


class VolunteerClubCategoryFilter(ClubCategoryFilter):
    club_lookup = "clubs"


class VolunteerClubFilter(ClubFilter):
    club_lookup = "clubs"


class VolunteerClubLocationFilter(admin.SimpleListFilter):
    title = "Club location"
    parameter_name = "location_id"
    club_lookup = "clubs"

    def lookups(self, request, model_admin):
        return ((loc.pk, loc.name) for loc in Location.objects.all())

    def queryset(self, request, queryset):
        val = self.value()
        if val is None:
            return queryset
        filter = {self.club_lookup + "__location_id": self.value()}
        return queryset.filter(**filter).distinct()


class VolunteerResource(resources.ModelResource):
    age = resource_fields.Field()
    organization = resource_fields.Field()
    referred_by = resource_fields.Field()

    class Meta:
        model = models.Volunteer
        fields = [
            "first_name",
            "last_name",
            "full_legal_name",
            "birth_date",
            "age",
            "shirt_size",
            "organization",
            "title",
            "email",
            "phone",
            "alt_phone",
            "address1",
            "address2",
            "city",
            "state",
            "postal_code",
            "race",
            "ethnicity",
            "gender",
            "referred_by",
        ]
        export_order = fields

    def dehydrate_age(self, obj):
        return obj.age

    def dehydrate_organization(self, obj):
        return obj.organization_name

    def dehydrate_referred_by(self, obj):
        return dict_find(obj.extra_data, "questionnaire.referred-by.value")


@register(models.Organization)
class OrganizationAdmin(admin.ModelAdmin):
    icon = '<i class="material-icons">business</i>'
    list_display = ["name", "type"]
    list_filter = ["type"]
    fields = ["type", "name", "address1", "address2", "city", "state", "postal_code"]
    search_fields = ["name__unaccent"]


class VolunteerPendingRequirementFilter(admin.SimpleListFilter):
    title = "pending requirement"
    parameter_name = "requirement"

    def lookups(self, request, model_admin):
        return ((r.pk, r.name) for r in VolunteerRequirementType.objects.all())

    def queryset(self, request, queryset):
        val = self.value()
        if val is None:
            return queryset
        filter = Q(requirements__requirement_type=val) & ~Q(
            requirements__status="COMPLETED"
        )
        return queryset.filter(filter).distinct()


@short_description("Merge volunteers")
def merge_volunteers(modeladmin, request, queryset):
    selected = request.POST.getlist(admin.ACTION_CHECKBOX_NAME)
    url = reverse("admin:volunteers_volunteer_merge")
    return http.HttpResponseRedirect("{}?volunteers={}".format(url, ",".join(selected)))


@register(models.Volunteer)
class VolunteerAdmin(ExportActionModelAdmin):
    icon = '<i class="material-icons">people</i>'
    list_display = ["__str__", "email", "date_added"]
    search_fields = ["first_name__unaccent", "last_name__unaccent", "email__unaccent"]
    fields = ["first_name", "last_name", "email", "date_added"]
    readonly_fields = fields
    inlines = [VolunteerRequirementInline]
    resource_class = VolunteerResource  # for export/CSV

    list_filter = [
        PeriodFilter,
        "organization",
        VolunteerPendingRequirementFilter,
        ClubCategoryFilter.factory("clubs"),
        ClubFilter.factory("clubs"),
    ]

    actions = [merge_volunteers, "export_admin_action"]

    def get_urls(self):
        from django.conf.urls import url

        return [
            url(
                "^merge/$",
                self.admin_site.admin_view(self.merge_view),
                name="volunteers_volunteer_merge",
            ),
        ] + super().get_urls()

    def merge_view(self, request, *args, **kwargs):
        from volunteers import utils
        from volunteers.models import Volunteer

        if request.method == "POST":
            volunteer_ids = request.POST.getlist("volunteers")
            volunteers = Volunteer.objects.filter(id__in=volunteer_ids)
            keep_id = request.POST.get("keep")
            keep_volunteer = Volunteer.objects.get(pk=keep_id)
            merge_volunteers = Volunteer.objects.filter(id__in=volunteer_ids).exclude(
                id=keep_id
            )
            # merge volunteers by most recent first
            merge_volunteers = merge_volunteers.order_by("-id")
            try:
                with transaction.atomic():
                    for volunteer in merge_volunteers:
                        utils.merge_volunteer(volunteer, keep_volunteer)
            except utils.MergeError as error:
                context = {
                    "volunteers": volunteers,
                    "merge_error": error,
                }
                return render(request, "admin/volunteers/merge.html", context)
            self.message_user(
                request, "Successfully merged volunteer.", messages.SUCCESS
            )

            if request.POST.get("next"):
                return http.HttpResponseRedirect(request.POST["next"])
            return http.HttpResponseRedirect(
                reverse("admin:volunteers_volunteer_changelist")
            )

        volunteer_ids = (int(id) for id in request.GET.get("volunteers").split(","))
        volunteers = Volunteer.objects.filter(id__in=volunteer_ids).order_by("id")
        context = {"volunteers": volunteers, "next": request.GET.get("next")}
        return render(request, "admin/volunteers/merge.html", context)

    def has_add_permission(self, *args, **kwargs):
        return False


class VolunteerSignupResource(resources.ModelResource):
    class Meta:
        model = models.VolunteerSignup
        fields = [
            "date",
            "opportunity__name",
            "volunteer__first_name",
            "volunteer__last_name",
            "volunteer__full_legal_name",
            "volunteer__birth_date",
            "volunteer__shirt_size",
            "volunteer__company",
            "volunteer__title",
            "volunteer__email",
            "volunteer__phone",
            "volunteer__alt_phone",
            "volunteer__address1",
            "volunteer__address2",
            "volunteer__city",
            "volunteer__state",
            "volunteer__postal_code",
            "volunteer__race",
            "volunteer__ethnicity",
            "volunteer__gender",
        ]


@register(models.VolunteerSignup)
class VolunteerSignupAdmin(ExportActionModelAdmin):
    icon = '<i class="material-icons">assignment_ind</i>'
    list_display = ["volunteer", "opportunity", "date"]
    search_fields = [
        "volunteer__first_name__unaccent",
        "volunteer__last_name__unaccent",
        "volunteer__email__unaccent",
    ]
    list_filter = [
        PeriodFilter,
        ClubCategoryFilter.factory("assignments__club"),
        ClubLocationFilter.factory("assignments__club"),
    ]

    readonly_fields = ["volunteer", "opportunity", "form_data", "date"]
    resource_class = VolunteerSignupResource

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return True


class TrainingVideoInline(admin.StackedInline):
    model = models.TrainingVideo
    fields = ["order", "title", "url", "thumbnail_url"]
    extra = 0
    classes = ["collapse"]


@register(models.TrainingSession)
class TrainingSessionAdmin(admin.ModelAdmin):
    icon = '<i class="material-icons">event</i>'
    list_display = ["name", "date", "_time"]
    list_filter = ["location"]
    inlines = [TrainingVideoInline]
    autocomplete_fields = ["clubs", "opportunities", "facilitators"]

    fields = [
        "name",
        "description",
        "location",
        ("online", "training_url"),
        ("date", "start_time", "end_time"),
        "clubs",
        "opportunities",
        "facilitators",
    ]

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        q = ~Q(status="ARCHIVED")
        if obj:
            q |= Q(pk__in=obj.opportunities.all())
        opportunities = models.VolunteerOpportunity.objects.filter(q)
        form.base_fields["opportunities"].queryset = opportunities
        return form

    def _time(self, obj):
        if obj.start_time and obj.end_time:
            return "{:%l:%M %P} - {:%l:%M %P}".format(obj.start_time, obj.end_time)
        return "(TBD)"

    _time.short_description = "Time"


class VolunteerRequirementTypeForm(forms.ModelForm):
    def clean_included_requirements(self):
        included_requirements = self.cleaned_data["included_requirements"]
        obj = self.instance

        try:
            models.VolunteerRequirementType.check_circular_dependency(
                obj, included_requirements
            )
        except models.VolunteerRequirementType.CircularDependencyError:
            raise ValidationError("Circular reference detected")
        return included_requirements


@register(models.VolunteerRequirementType)
class VolunteerRequirementTypeAdmin(admin.ModelAdmin):
    list_display = ["name", "slug"]
    prepopulated_fields = {"slug": ["name"]}
    search_fields = ["name"]
    form = VolunteerRequirementTypeForm


"""
@register(models.VolunteerRequirement)
class VolunteerRequirementAdmin(admin.ModelAdmin):
    list_display = ['volunteer', 'requirement_type', 'status']
    list_filter = ['status', 'requirement_type']
    exclude = ['parent_requirement']
    fields = [
        'volunteer_link', 'requirement_type', 'status', 'completed_date', 'notes', 'expire_date'
    ]
    readonly_fields = ['volunteer_link', 'requirement_type', 'expire_date']

    def volunteer_link(self, obj):
        url = reverse('admin:volunteers_volunteer_change', kwargs={'object_id': obj.volunteer.id})
        return mark_safe(f'<a href="{url}">{obj.volunteer.name}</a>')
    volunteer_link.short_description = 'Volunteer'
"""


class VolunteerAssignmentResource(resources.ModelResource):
    verified = resource_fields.Field()

    class Meta:
        model = models.VolunteerAssignment
        # FIXME: excluding age/referred_by/etc here is kludgy
        fields = [
            "volunteer__" + f
            for f in VolunteerResource._meta.fields
            if f not in ("age", "referred_by", "organization")
        ] + ["club__club_code", "role", "verified"]

    def dehydrate_verified(self, obj):
        return obj.verified and "Yes" or "No"


class SelectedSessionChoiceWidget(forms.CheckboxSelectMultiple):
    def __init__(self, *args, **kwargs):
        assignment = kwargs.pop("assignment")
        self.past_sessions = (s.id for s in assignment.club.sessions.past())
        super().__init__(*args, **kwargs)

    def create_option(self, *args, **kwargs):
        option = super().create_option(*args, **kwargs)
        if option["value"] in self.past_sessions:
            option["attrs"]["disabled"] = True
        return option

    def render(self, *args, **kwargs):
        html = super().render(*args, **kwargs)
        return f"""
            <div class="selected-sessions">
                <p class="select-all">
                    <a id="select_all_sessions">select all</a> |
                    <a id="deselect_all_sessions">de-select all</a>
                </p>
                {html}
            </div>
        """


class VolunteerAssignmentForm(forms.ModelForm):
    selected_sessions = forms.ModelMultipleChoiceField(
        required=False, queryset=Session.objects.none()
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get("instance")
        kwargs["initial"] = kwargs.get("initial") or {}
        if instance:
            club = instance.club
            self.base_fields["selected_sessions"].queryset = club.sessions.filter(
                type="CLUB"
            )
            self.base_fields["selected_sessions"].widget = SelectedSessionChoiceWidget(
                assignment=instance
            )
            kwargs["initial"]["selected_sessions"] = Session.objects.filter(
                pk__in=instance.session_attendances.values("session")
            )
        super().__init__(*args, **kwargs)

    def clean(self):
        super().clean()
        club = self.cleaned_data["club"]
        if self.instance.pk:
            volunteer = self.instance.volunteer
        else:
            volunteer = self.cleaned_data["volunteer"]

        try:
            existing = VolunteerAssignment.objects.exclude(pk=self.instance.pk).get(
                volunteer=volunteer, club=club
            )
        except VolunteerAssignment.DoesNotExist:
            pass
        else:
            url = reverse(
                "admin:volunteers_volunteerassignment_change",
                kwargs={"object_id": existing.pk},
            )
            raise ValidationError(
                {
                    "club": mark_safe(
                        f"{volunteer.name} is already assigned to the selected club ("
                        f'<a href="{url}">view existing assignemnt</a>).'
                    )
                }
            )

        if self.instance and "club" not in self.changed_data:
            # Only allow removal from future sessions
            selected_sessions = self.cleaned_data["selected_sessions"]
            past_attendances = (
                self.instance.session_attendances.past()
                .filter(session__type="CLUB")
                .values("session")
            )
            removed_past_sessions = past_attendances.exclude(
                session__pk__in=selected_sessions
            )
            if removed_past_sessions.count() > 0:
                raise ValidationError("Cannot remove volunteer from past sessions")

    class Meta:
        # override width for autocomplete field
        widgets = {
            "club": AutocompleteSelect(
                VolunteerAssignment.club.field.remote_field,
                admin.site,
                attrs={"data-width": "400px"},
            ),
        }


@register(models.VolunteerAssignment)
class VolunteerAssignmentAdmin(ExportActionModelAdmin):
    icon = '<i class="material-icons">assignment_turned_in</i>'
    list_display = ["volunteer", "club", "role", "verified"]
    list_filter = [
        PeriodFilter,
        ClubFilter.factory("club"),
        "role",
        "verified",
        VolunteerPendingRequirementFilter,
    ]
    search_fields = [
        "volunteer__first_name__unaccent",
        "volunteer__last_name__unaccent",
    ]
    autocomplete_fields = ["club", "volunteer"]
    resource_class = VolunteerAssignmentResource

    form = VolunteerAssignmentForm

    def get_readonly_fields(self, request, obj=None):
        if obj is None:
            return []

        return ["_volunteer_link"]

    def get_fields(self, request, obj=None):
        if obj is None:
            return ["volunteer", "club", "role"]
        return ["_volunteer_link", "club", "role", "selected_sessions"]

    def render_change_form(self, request, context, *args, **kwargs):
        return super().render_change_form(request, context, *args, **kwargs)

    def _volunteer_link(self, obj=None):
        url = reverse("admin:volunteers_volunteer_change", args=[obj.volunteer.pk])
        return mark_safe(f'<a href="{url}">{obj.volunteer}</a>')

    _volunteer_link.short_description = "Volunteer"

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)

        assignment = form.instance
        club = assignment.club

        if "club" in form.changed_data:
            # Move the associated session attendances to the new club
            assignment.move_attendances(club)
        else:
            # Update attendances items based on selected sessions
            if assignment:
                volunteer = assignment.volunteer
                selected_sessions = form.cleaned_data["selected_sessions"]

                # Delete all attendances for this assignemnt that are not in selected_sessions
                assignment.session_attendances.exclude(
                    session_id__in=selected_sessions
                ).delete()

                # Ensure any selected sessions are saved as a VolunteerAttendance instance
                for session in selected_sessions:
                    VolunteerAttendance.objects.get_or_create(
                        session=session, volunteer=volunteer, assignment=assignment
                    )

        if "club" in form.changed_data:
            messages.add_message(
                request,
                messages.WARNING,
                "The club has been changed for this assignment. Please verify the attendance "
                "dates for this assignemnt.",
            )

        # Updated associated requirements & verification status
        assignment.update_requirements()
        assignment.update_verification()


class ChecklistItemForm(forms.ModelForm):
    roles = forms.MultipleChoiceField(
        choices=(c for c in VOLUNTEER_ROLE_CHOICES if c[0]),
        widget=forms.CheckboxSelectMultiple,
    )


@register(models.ChecklistItem)
class ChecklistItemAdmin(admin.ModelAdmin):
    list_display = ["slug", "_description", "order"]
    list_editable = ["order"]
    form = ChecklistItemForm

    def _description(self, obj):
        html = markdown(obj.description, extensions=[NewTabExtension()])
        html = re.sub(r"^<p>(.*?)</p>", r"\1", html)
        return mark_safe(html)
