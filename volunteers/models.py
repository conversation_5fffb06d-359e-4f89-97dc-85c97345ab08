import re
from collections import OrderedDict

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.postgres.fields import A<PERSON>y<PERSON>ield, JSO<PERSON>ield
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import Q
from django.template.defaultfilters import slugify
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from localflavor.us import models as lf_models

from clubs.query import PeriodQuerySet
from contacts.models import Contact, DemographicsModel
from hive.utils import format_date
from questionnaires.models import Questionnaire
from users.models import User

from . import constants
from .query import VolunteerAssignmentQuerySet, VolunteerRequirementQuerySet

YOUTUBE_URL_PATTERN = (
    r"^((?:https?:)?\/\/)?((?:www|m)\.)?((?:youtube\.com|youtu.be))"
    r"(\/(?:[\w\-]+\?v=|embed\/|v\/)?)([\w\-]+)(\S+)?$"
)


class Organization(models.Model):
    type = models.CharField(max_length=32, choices=constants.ORGANIZATION_TYPE_CHOICES)
    name = models.CharField(max_length=255)
    address1 = models.CharField(_("Address line 1"), max_length=255, blank=True)
    address2 = models.CharField(_("Address line 2"), max_length=255, blank=True)
    city = models.CharField(_("City"), max_length=255, blank=True)
    state = lf_models.USStateField(_("State"), blank=True)
    postal_code = lf_models.USZipCodeField(_("Postal code"), blank=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ["name"]


class Volunteer(DemographicsModel, Contact):
    last_updated = models.DateTimeField(null=True)
    shirt_size = models.CharField(
        max_length=5, choices=constants.SHIRT_SIZE_CHOICES, blank=True
    )
    full_legal_name = models.CharField(max_length=128, blank=True)
    emergency_contact = models.TextField(
        blank=True,
        help_text=(
            "Provide the name and phone number of the person we should "
            "contact in the event of an emergency"
        ),
    )
    # student here refers to a college/university student, not a club student.
    is_student = models.BooleanField(default=False)
    school = models.CharField(blank=True, max_length=255)
    organization = models.ForeignKey(
        Organization, null=True, blank=True, on_delete=models.PROTECT
    )
    organization_other = models.CharField(max_length=255, blank=True)
    last_agreement_date = models.DateTimeField(null=True, blank=True)
    extra_data = JSONField(null=True, blank=True)
    languages = JSONField(null=True, blank=True)
    clubs = models.ManyToManyField("clubs.Club", through="VolunteerAssignment")
    not_duplicate = ArrayField(models.PositiveIntegerField(), default=list, blank=True)

    objects = PeriodQuerySet.as_manager("clubs")

    # Note: the .user attribute comes from the fact that Volunteer is a Contact, and the User model
    # has a one-to-one with Contact with a related name of 'user'.

    class Meta:
        ordering = ["first_name", "last_name"]

    @property
    def agreement_expired(self):
        if not self.last_accepted_agreement:
            return True
        agreement_age = timezone.now() - self.last_accepted_agreement
        return agreement_age >= settings.VOLUNTEER_TERMS_RENEWAL_PERIOD

    def get_upcoming_training_sessions(self):
        sessions = TrainingSession.objects.filter(date__gte=timezone.now()).order_by(
            "date", "start_time"
        )

        if self.user.is_superuser:
            return sessions

        my_opportunities = VolunteerOpportunity.objects.exclude(
            status="ARCHIVED"
        ).filter(signups__volunteer=self)
        my_clubs = self.clubs.all()

        return sessions.filter(
            Q(clubs__in=my_clubs)
            | Q(clubs=None, opportunities__in=my_opportunities)
            | Q(facilitators=self)
        ).distinct()

    def get_assignment(self, club):
        """
        Get the primary role of the volunteer at a given club
        """
        priority = ["TEAM_CAPTAIN", "MENTOR", "SCHOOL_LIAISON", "FLOATER", "GUEST"]
        assignments = sorted(
            self.assignments.filter(club=club), key=lambda a: priority.index(a.role)
        )
        if len(assignments) == 0:
            return None
        return assignments[0]

    def create_assignment(self, club, role, requirements=None, signup=None):
        """
        Creates a new VolunteerAssignment with the appropriate requirements
        """
        # TODO: PRUNE: this method is not used anywhere
        assignment = VolunteerAssignment.objects.create(
            club=club, volunteer=self, role=role, signup=signup
        )
        assignment.update_requirements()
        return assignment

    @property
    def organization_name(self):
        if self.organization:
            return self.organization.name
        return self.organization_other


class VolunteerRequirementType(models.Model):
    name = models.CharField(max_length=64)
    slug = models.SlugField(unique=True)
    responsible_party = models.CharField(
        max_length=16,
        choices=(
            ("VOLUNTEER", "Volunteer"),
            ("TEAM_CAPTAIN", "Team Captain"),
            ("STAFF", "Staff"),
            ("SCHOOL_LIAISON", "School Liaison"),
        ),
        default="VOLUNTEER",
    )
    renewal_months = models.IntegerField(null=True, blank=True)
    instructions = models.TextField(help_text="markdown supported", blank=True)
    included_requirements = models.ManyToManyField(
        "VolunteerRequirementType",
        blank=True,
        help_text=(
            "Select any other requirements that are also covered by this requirement, for "
            "example, a background check."
        ),
    )

    def __str__(self):
        return self.name

    class CircularDependencyError(Exception):
        pass

    @staticmethod
    def check_circular_dependency(req, children, visited=None):
        visited = visited or []
        if req.slug in visited:
            if req.slug == visited[0]:
                raise VolunteerRequirementType.CircularDependencyError
            return
        visited.append(req.slug)
        for child in children:
            VolunteerRequirementType.check_circular_dependency(
                child, child.included_requirements.all(), visited
            )
        visited.remove(req.slug)

    def resolve_included_requirements(self, children=None):
        children = children or self.included_requirements.all()

        VolunteerRequirementType.check_circular_dependency(self, children)

        def yield_tree(req):
            yield req
            for child in req.included_requirements.all():
                yield from yield_tree(child)

        included = set(yield_tree(self))
        included.remove(self)
        return included

    class Meta:
        ordering = ["pk"]
        verbose_name = "Volunteer requirement"
        verbose_name_plural = "Volunteer requirements"


class VolunteerRequirement(models.Model):
    requirement_type = models.ForeignKey(
        VolunteerRequirementType,
        related_name="pending_or_completed_requirements",
        on_delete=models.PROTECT,
    )
    parent_requirement = models.ForeignKey(
        "VolunteerRequirement",
        null=True,
        blank=True,
        related_name="child_requirements",
        on_delete=models.CASCADE,
    )
    volunteer = models.ForeignKey(
        Volunteer, related_name="requirements", on_delete=models.CASCADE
    )
    status = models.CharField(
        max_length=16,
        choices=constants.VOLUNTEER_REQUIREMENT_STATUS_CHOICES,
        default="INCOMPLETE",
    )
    completed_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)
    expire_date = models.DateField(null=True, blank=True)

    objects = VolunteerRequirementQuerySet.as_manager()

    def save(self, *args, **kwargs):
        if (
            not self.expire_date
            and self.completed_date
            and self.requirement_type.renewal_months
        ):
            delta = relativedelta(months=self.requirement_type.renewal_months)
            self.expire_date = self.completed_date + delta

        super().save(*args, **kwargs)

        # When requirement is completed, auto-complete any included requirements. An included
        # requirement is created as a "child" of this requirement, marked as completed,
        # and has the same expiration date (regardless of the renewal_months on the included type).
        #
        # This is mostly in place for situations where a requirement also serves as a valid
        # background check.
        if self.status == "COMPLETED":
            included_requirements = (
                self.requirement_type.resolve_included_requirements()
            )
            for included_type in included_requirements:
                try:
                    self.child_requirements.get(
                        requirement_type=included_type, volunteer=self.volunteer
                    )
                except VolunteerRequirement.DoesNotExist:
                    VolunteerRequirement.objects.create(
                        parent_requirement=self,
                        requirement_type=included_type,
                        volunteer=self.volunteer,
                        status=self.status,
                        completed_date=self.completed_date,
                        expire_date=self.expire_date,
                        notes=f"Covered by {self.requirement_type.name}",
                    )

        # Update child requirement statuses to match this one
        for child_req in self.child_requirements.all():
            child_req.status = self.status
            child_req.save()

        # Update verification on any related assignments
        for assignment in self.assignments.all():
            assignment.update_verification()

    def is_expired(self, as_of=None):
        as_of = as_of or timezone.now().date()
        return self.expire_date is not None and self.expire_date < as_of

    @property
    def name(self):
        return self.requirement_type.name

    @property
    def instructions(self):
        return self.requirement_type.instructions

    def __str__(self):
        return self.name


class VolunteerOpportunity(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField()
    short_description = models.TextField(
        blank=True,
        help_text="Shown on volunteer opportunities listing page (markdown supported)",
    )
    description = models.TextField(
        blank=True,
        help_text="Shown on signup page for this volunteer opportunity (markdown supported)",
    )
    description_url = models.URLField(blank=True)
    role = models.CharField(
        max_length=32, choices=constants.VOLUNTEER_ROLE_CHOICES, blank=True
    )
    photo = models.ImageField(blank=True)
    status = models.CharField(
        max_length=16,
        choices=(
            ("DRAFT", "Draft"),
            ("UNLISTED", "Unlisted"),
            ("PUBLISHED", "Published"),
            ("ARCHIVED", "Archived"),
        ),
        default="DRAFT",
    )
    order = models.IntegerField(default=0)
    questionnaire = models.ForeignKey(
        Questionnaire, blank=True, null=True, on_delete=models.PROTECT
    )
    confirmation_extra_info = models.TextField(blank=True)
    clubs = models.ManyToManyField(
        "clubs.Club", blank=True, related_name="volunteer_opportunities"
    )
    skip_club_selection = models.BooleanField(
        "Skip club selection screen",
        default=False,
        help_text=("Skips club selection screen if there is only one club choice"),
    )
    requirements = models.ManyToManyField(VolunteerRequirementType, blank=True)
    contact = models.ForeignKey(
        User,
        null=True,
        blank=True,
        limit_choices_to={"is_staff": True},
        related_name="opportunities_contact",
        on_delete=models.SET_NULL,
    )
    notification_recipients = models.ManyToManyField(User, blank=True)
    thankyou_message = models.TextField(
        "Thank-you message",
        help_text="Message seen immediately after signing up (markdown supported)",
        blank=True,
    )

    objects = PeriodQuerySet.as_manager("clubs")

    def __str__(self):
        if self.status == "ARCHIVED":
            return self.name + " (archived)"
        if self.status == "DRAFT":
            return self.name + " (draft)"
        return self.name

    def clean(self):
        if not self.slug:
            self.slug = slugify(self.name)

        # raise validation error for slug_unique_if_not_archived cosntraint (defined below)
        existing = VolunteerOpportunity.objects.exclude(
            Q(pk=self.pk) | Q(status="ARCHIVED")
        ).filter(slug=self.slug)
        if existing.count():
            raise ValidationError(
                f'An opportunity with slug "{self.slug}" already exists. You can archive the '
                "existing opportunity, or choose a different slug."
            )

    @property
    def club_locations(self):
        locations = self.clubs.values_list("location__name", flat=True)
        return locations.distinct()

    @property
    def is_mentor_role(self):
        return self.role in constants.MENTOR_ROLES

    @property
    def has_attendances(self):
        """
        True if this opportunity should generate VolunteerAttendance objects.
        """
        return self.is_mentor_role

    class Meta:
        verbose_name_plural = "Volunteer opportunities"
        ordering = ["order"]

        # This constraint enforces unique slugs, except for archived objects.
        constraints = [
            models.UniqueConstraint(
                name="slug_unique_if_not_archived",
                fields=["slug"],
                condition=~Q(status="ARCHIVED"),
            )
        ]


class TrainingSession(models.Model):
    opportunities = models.ManyToManyField(
        VolunteerOpportunity,
        blank=True,
        related_name="training_sessions",
        help_text="This session will be for volunteers who have signed up for this opportunity",
    )
    clubs = models.ManyToManyField(
        "clubs.Club",
        blank=True,
        related_name="training_sessions",
        help_text="This session will be for volunteers assigned to these clubs.",
    )
    location = models.ForeignKey("clubs.Location", on_delete=models.PROTECT)
    online = models.BooleanField(default=False)
    training_url = models.URLField(blank=True)
    date = models.DateField(
        help_text=(
            "For online training, this will be the date the training link shows on the dashboard."
        )
    )
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    name = models.CharField(max_length=128)
    description = models.TextField(blank=True)
    facilitators = models.ManyToManyField(
        Volunteer, blank=True, related_name="facilitated_training_sessions"
    )
    attendees = models.ManyToManyField(
        Volunteer, blank=True, related_name="attended_training_sessions"
    )
    location_info = models.TextField("Location info / parking instructions", blank=True)
    notes = models.TextField(blank=True)

    objects = PeriodQuerySet.as_manager("clubs")

    @property
    def date_description(self):
        datestr = format_date(self.date, "N j, Y")
        if self.start_time:
            datestr += ", " + format_date(self.start_time, "P")
        if self.end_time:
            datestr += " - " + format_date(self.end_time, "P")
        return datestr

    @property
    def invitees(self):
        clubs = self.clubs.all()
        opportunities = self.opportunities.all()
        if opportunities and not clubs:
            # get volunteers who have signed up for the associated opportunity
            filter = Q(signups__opportunity__in=opportunities)
        elif clubs:
            # get volunteers who have signed up for the associated clubs
            filter = Q(clubs__in=clubs)

        # exclude volunteers who have already attended training for this opportunity
        filter &= ~Q(attended_training_sessions__opportunities__in=opportunities)

        # include facilitators
        filter |= Q(facilitated_training_sessions=self)

        # include attendees of THIS session
        filter |= Q(attended_training_sessions=self)

        return Volunteer.objects.filter(filter).distinct()

    def __str__(self):
        return self.name


class TrainingVideo(models.Model):
    training_session = models.ForeignKey(
        TrainingSession, on_delete=models.CASCADE, related_name="training_videos"
    )
    title = models.CharField(max_length=255)
    order = models.PositiveIntegerField(default=0)
    url = models.URLField()
    thumbnail_url = models.URLField(blank=True)

    def save(self, *args, **kwargs):
        yt_match = re.match(YOUTUBE_URL_PATTERN, self.url)
        if yt_match:
            video_id = yt_match.group(5)
            self.thumbnail_url = f"https://img.youtube.com/vi/{video_id}/hqdefault.jpg"
        super().save(*args, **kwargs)

    class Meta:
        ordering = ["order"]


class VolunteerSignup(models.Model):
    """
    Represents the event that a volunteer signed up for an opportunity.
    """

    date = models.DateTimeField(default=timezone.now, null=True)
    volunteer = models.ForeignKey(
        Volunteer, related_name="signups", on_delete=models.PROTECT
    )
    opportunity = models.ForeignKey(
        VolunteerOpportunity, related_name="signups", on_delete=models.PROTECT
    )
    form_data = JSONField(null=True, blank=True)

    objects = PeriodQuerySet.as_manager("opportunity__clubs")

    def __str__(self):
        return "{} - {}".format(self.opportunity, self.volunteer)

    @property
    def requirements(self):
        return VolunteerRequirement.objects.filter(
            assignments__in=self.assignments.all()
        )

    @property
    def training_sessions(self):
        q = models.Q(clubs=None, opportunities=self.opportunity)
        if self.assignments.count() > 0:
            clubs = self.assignments.values_list("club", flat=True)
            q |= models.Q(clubs__in=clubs)
        return TrainingSession.objects.filter(q).distinct()

    @property
    def attended_training_sessions(self):
        return self.training_sessions.filter(attendees=self.volunteer).distinct()

    @property
    def session_attendances(self):
        from clubs.models import VolunteerAttendance

        return VolunteerAttendance.objects.filter(
            volunteer=self.volunteer, session__club__in=self.opportunity.clubs.all()
        )


class VolunteerAssignment(models.Model):
    """
    Represents a volunteer's role for a given club. This is independent of the signup, although
    it can be created automatically during a signup, during which case the signup will be
    associated with the Assignment for reference purposes. Assignments can also be created manually
    by admins.
    """

    club = models.ForeignKey(
        "clubs.Club", related_name="volunteer_assignments", on_delete=models.CASCADE
    )
    volunteer = models.ForeignKey(
        Volunteer, related_name="assignments", on_delete=models.CASCADE
    )
    role = models.CharField(
        max_length=32, choices=constants.VOLUNTEER_ROLE_CHOICES, blank=True
    )
    requirements = models.ManyToManyField(
        VolunteerRequirement, related_name="assignments", blank=True
    )
    signup = models.ForeignKey(
        VolunteerSignup,
        related_name="assignments",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    verified = models.BooleanField(default=False)
    checklist = JSONField(blank=True, null=True, default=list)
    sessions = models.ManyToManyField(
        "clubs.Session", through="clubs.VolunteerAttendance"
    )

    objects = VolunteerAssignmentQuerySet.as_manager()

    class Meta:
        unique_together = ("club", "volunteer")

    def __str__(self):
        if self.pk:
            return "{} @ {} - {}".format(
                self.get_role_display(), self.club.club_code, self.volunteer
            )
        return "(unsaved assignment)"

    @transaction.atomic
    def update_requirements(self):
        """
        Updates this assignment with the latest requirements for the club.
        """
        from .utils import get_club_requirements

        # Get requirements (new or existing) specific to this club
        requirements = get_club_requirements(self.volunteer, self.club)

        # Update the assignment with the returned set of requirements
        for requirement in requirements:
            if requirement.pk is None:
                requirement.save()

        self.requirements.set(requirements)
        return requirements

    def update_verification(self):
        from . import verification

        try:
            verification.verify_requirements(self.requirements.all())
        except verification.VerificationFailed:
            self.verified = False
        else:
            self.verified = True
        self.save()

    @transaction.atomic
    def move_attendances(self, new_club):
        """
        Move future attendances associated with this assignment to the given club sessions based on
        the session number.
        """
        new_sessions = OrderedDict(
            (s.number, s)
            for s in new_club.sessions.filter(type="CLUB").order_by("number")
        )
        for attendance in self.session_attendances.future():
            if (
                attendance.session.number in new_sessions
                and attendance.session.is_future()
            ):
                # Switch the attendance to the new session
                attendance.session = new_sessions[attendance.session.number]
                attendance.save()
            else:
                # If there is no session with this number in the new club,
                # we just delete the attendance entry
                attendance.delete()

    def save(self, *args, **kwargs):
        super().save()

        # Add mentor chat group
        from chat.models import MemberGroup

        if self.club.enable_chat:
            group = MemberGroup.objects.get(club=self.club, slug="mentors")
            group.members.add(self.volunteer.user)

    @property
    def checklist_items(self):
        items = []
        filter = Q(roles=None) | Q(roles__contains=[self.role])
        for item in ChecklistItem.objects.filter(filter).order_by("order"):
            items.append(
                {
                    "slug": item.slug,
                    "description": item.description,
                    "more_info": item.more_info,
                    "completed": item.slug in self.checklist,
                }
            )
        return items

    @property
    def remaining_checklist_items(self):
        return [item for item in self.checklist_items if not item["completed"]]


class ChecklistItem(models.Model):
    order = models.PositiveIntegerField(default=0)
    slug = models.SlugField()
    description = models.CharField(max_length=255)
    more_info = models.TextField(blank=True)
    roles = ArrayField(
        models.CharField(max_length=32, choices=constants.VOLUNTEER_ROLE_CHOICES),
        default=list,
        blank=True,
        help_text="This item will apply to the selected roles only",
    )

    class Meta:
        ordering = ["order"]

    def __str__(self):
        return self.description
