from django.core.exceptions import PermissionDenied

from volunteers.models import VolunteerAssignment

from .utils import get_club_requirements

VIEW_CLUB_ROLES = ("MENTOR", "FLOATER", "TEAM_CAPTAIN", "SCHOOL_LIAISON")


class VerificationFailed(PermissionDenied):
    def __init__(self, incomplete_requirements=None):
        self.incomplete_requirements = incomplete_requirements or []


def verify_requirements(requirements):
    """
    Verifies whether a given set of requirements have been completed
    """
    incomplete = [r for r in requirements if r.status != "COMPLETED"]
    if len(incomplete) > 0:
        raise VerificationFailed(incomplete)


def verify_club_access(volunteer, club):
    """
    Verify access to a specific club. Pass in `requirements` and
    `team_captains` when calling from within a loop.
    """
    if is_exempt(volunteer, club):
        return True

    requirements = get_club_requirements(volunteer, club)
    verify_requirements(requirements)


def is_exempt(volunteer, club):
    """
    Returns `True` if the given volunteer is exempty from verification at the given club.
    """
    # Bold Idea staff are exempt
    if volunteer.user.is_staff:
        return True

    # School Liaisons are exempt
    try:
        club.volunteer_assignments.get(volunteer=volunteer, role="SCHOOL_LIAISON")
    except VolunteerAssignment.DoesNotExist:
        return False
    return True


def verify_student_access(volunteer, student, club=None):
    """
    Verify access to a specific student.
    """
    if club is None:
        clubs = student.clubs.current()
    else:
        clubs = [club]

    # safety measure -- if a student has no clubs, we must assume no access allowed
    if len(clubs) == 0:
        raise VerificationFailed()

    # Volunteer must be verified for all current clubs the student is registered in
    for club in clubs:
        verify_club_access(volunteer, club)


def get_club_verifications(volunteer, *clubs, update_requirements=True):
    """
    Given one or more clubs, returns a dictionary with clubs as keys and a boolean value
    indicating whether or not the volunteer is verified for that club.

    This is useful for when checkging verification on a page displaying multiple clubs, such as
    the dashboard home.
    """
    # Get all assignments associated with the given clubs
    verifications = {}
    assignments = volunteer.assignments.filter(club__in=clubs)

    if update_requirements:
        # Force-update the requirements for each assignment
        for assignment in assignments:
            assignment.update_requirements()

    for club in clubs:
        try:
            verify_club_access(volunteer, club)
        except VerificationFailed:
            verifications[club] = False
        else:
            if club not in verifications:
                verifications[club] = True

    return verifications
