import json

from django.contrib.auth.views import login_required
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from rest_framework import exceptions

from users.utils import is_volunteer
from volunteers.models import VolunteerAssignment


@login_required
@csrf_exempt
def checklist_item(request, assignment_id, slug):
    if not is_volunteer(request.user):
        return exceptions.PermissionDenied(
            "You do not have permission to access this resourse"
        )
    data = json.loads(request.body)
    completed = bool(data.get("completed"))
    assignment = get_object_or_404(VolunteerAssignment, pk=assignment_id)
    if assignment.volunteer != request.user.contact.volunteer:
        return exceptions.PermissionDenied(
            "You do not have permission to access this resourse"
        )
    if completed:
        assignment.checklist.append(slug)
    elif slug in assignment.checklist:
        assignment.checklist.remove(slug)
    assignment.save()

    return HttpResponse(json.dumps({"success": True}, indent=2))
