from rest_framework import permissions

from users.utils import is_volunteer
from volunteers import verification


class IsVolunteer(permissions.BasePermission):
    def has_permission(self, request, view):
        return is_volunteer(request.user)


class IsVerifiedForClub(permissions.BasePermission):
    """
    Allow access is user is allowed to view club student data
    """

    def __init__(self, club):
        self.club = club
        super().__init__()

    def has_permission(self, request, view):
        if not is_volunteer(request.user):
            return False
        volunteer = request.user.contact.volunteer
        try:
            verification.verify_club_access(volunteer, self.club)
        except verification.VerificationFailed:
            return False
        else:
            return True
