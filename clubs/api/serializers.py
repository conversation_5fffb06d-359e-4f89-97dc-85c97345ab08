from rest_framework import serializers
from rest_framework_nested.relations import NestedHyperlinkedR<PERSON>ted<PERSON><PERSON>
from rest_framework_nested.serializers import NestedHyperlinkedModelSerializer

from api.serializers import NamespacedUrlFieldMixin
from clubs.models import Club, Location, Session, StudentAttendance, VolunteerAttendance
from students.models import Student
from volunteers.models import Volunteer


class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = (
            "url",
            "id",
            "name",
            "nickname",
            "about",
            "address1",
            "address2",
            "city",
            "state",
            "postal_code",
            "phone",
            "extension",
            "instructions",
            "geo",
        )


class ClubSerializer(NamespacedUrlFieldMixin, serializers.HyperlinkedModelSerializer):
    url_namespace = "api:clubs"

    category = serializers.SlugRelatedField(read_only=True, slug_field="slug")
    sessions = NestedHyperlinkedRelatedField(
        many=True,
        read_only=True,
        view_name=f"{url_namespace}:session-detail",
        parent_lookup_kwargs={"club_pk": "club__pk"},
    )
    volunteer_requirements = serializers.SlugRelatedField(
        many=True, read_only=True, slug_field="slug"
    )

    class Meta:
        model = Club
        fields = (
            "url",
            "id",
            "name",
            "photo",
            "club_code",
            "category",
            "volunteer_requirements",
            "hide_on_registration",
            "hide_on_volunteer_signup",
            "registration_url",
            "fee",
            "allow_monthly_payments",
            "num_monthly_payments",
            "base_description",
            "location",
            "location_info",
            "min_age",
            "max_age",
            "min_grade",
            "max_grade",
            "num_student_seats",
            "num_volunteers_needed",
            "student_volunteer_ratio",
            "registration_open_date",
            "registration_close_date",
            "schedule_description",
            "sessions",
        )


class BaseAttendanceSerializer(
    NamespacedUrlFieldMixin, NestedHyperlinkedModelSerializer
):
    url_namespace = "api:clubs"

    def create(self, validated_data):
        request = self.context["request"]
        validated_data["session_id"] = request.data["session_id"]

        # It's possible for two users to update attendance without refreshing the page, causing two
        # "create" requests. State is not pushed to the clients, so we allow the "create" request
        # even if the object already exists.
        existing = self.get_existing(request, validated_data)
        if existing:
            return existing
        return super().create(validated_data)

    def get_existing(self, request, validated_data):
        """
        Checks for an existing record based on validated data, returns record if exists, otherwise
        returns None.
        """
        raise NotImplementedError


class VolunteerAttendanceSerializer(BaseAttendanceSerializer):
    volunteer_id = serializers.PrimaryKeyRelatedField(
        queryset=Volunteer.objects.all(), source="volunteer"
    )
    parent_lookup_kwargs = {"session_pk": "session__pk", "club_pk": "session__club__pk"}

    def get_existing(self, request, validated_data):
        try:
            return VolunteerAttendance.objects.get(
                volunteer_id=validated_data["volunteer"].id,
                session_id=request.data["session_id"],
            )
        except VolunteerAttendance.DoesNotExist:
            return None

    class Meta:
        model = VolunteerAttendance
        fields = ("url", "id", "volunteer_id", "present", "notes")
        extra_kwargs = {
            "notes": {"required": False, "allow_blank": True},
        }


class StudentAttendanceSerializer(BaseAttendanceSerializer):
    student_id = serializers.PrimaryKeyRelatedField(
        queryset=Student.objects.all(), source="student"
    )
    parent_lookup_kwargs = {"session_pk": "session__pk", "club_pk": "session__club__pk"}

    def get_existing(self, request, validated_data):
        try:
            return StudentAttendance.objects.get(
                student_id=validated_data["student"].id,
                session_id=request.data["session_id"],
            )
        except StudentAttendance.DoesNotExist:
            return None

    class Meta:
        model = StudentAttendance
        fields = ("url", "id", "student_id", "present", "notes")
        extra_kwargs = {"notes": {"required": False, "allow_blank": True}}


class SessionSerializer(NamespacedUrlFieldMixin, NestedHyperlinkedModelSerializer):
    url_namespace = "api:clubs"
    parent_lookup_kwargs = {"club_pk": "club__pk"}
    volunteer_attendances = VolunteerAttendanceSerializer(many=True)
    student_attendances = StudentAttendanceSerializer(many=True)

    class Meta:
        model = Session
        fields = (
            "url",
            "id",
            "club",
            "date",
            "start_time",
            "end_time",
            "description",
            "volunteer_attendances",
            "student_attendances",
        )
