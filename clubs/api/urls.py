from django.conf.urls import include, url
from rest_framework_nested import routers

from api.urls import base_router

from . import views

app_name = "clubs"

# register clubs/locations with base_router so that we can mount clubs_router and sessions_router
base_router.register(r"clubs", views.ClubViewSet)
base_router.register(r"locations", views.LocationViewSet)

clubs_router = routers.NestedSimpleRouter(base_router, r"clubs", lookup="club")
clubs_router.register(r"sessions", views.SessionViewSet)

sessions_router = routers.NestedSimpleRouter(
    clubs_router, r"sessions", lookup="session"
)
sessions_router.register(r"attendance", views.AttendanceViewSet, basename="attendance")
sessions_router.register(r"attendance/volunteers", views.VolunteerAttendanceViewSet)
sessions_router.register(r"attendance/students", views.StudentAttendanceViewSet)

urlpatterns = [
    url(r"^", include(base_router.urls)),
    url(r"^", include(clubs_router.urls)),
    url(r"^", include(sessions_router.urls)),
]
