from django.shortcuts import reverse
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.views import NestedModelViewSet
from clubs.models import Club, Location, Session, StudentAttendance, VolunteerAttendance

from . import permissions, serializers


class ClubViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.ClubSerializer
    queryset = Club.objects.all()


class SessionViewSet(NestedModelViewSet):
    parent = "club"
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.SessionSerializer
    queryset = Session.objects.all()


class AttendanceViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def list(self, request):
        return Response(
            {
                "students": reverse("api:clubs:studentattendance-list"),
                "volunteers": reverse("api:clubs:volunteerattendance-list"),
            }
        )


class SessionNestedModelViewSet(NestedModelViewSet):
    permission_classes = [IsAuthenticated]
    parent = "session"

    def get_permissions(self):
        permission_classes = self.permission_classes
        session = Session.objects.get(pk=self.kwargs[self.parent_kwarg])
        perms = [permission() for permission in permission_classes]
        perms.append(permissions.IsVerifiedForClub(club=session.club))
        return perms


class VolunteerAttendanceViewSet(SessionNestedModelViewSet):
    serializer_class = serializers.VolunteerAttendanceSerializer
    queryset = VolunteerAttendance.objects.all()


class StudentAttendanceViewSet(SessionNestedModelViewSet):
    serializer_class = serializers.StudentAttendanceSerializer
    queryset = StudentAttendance.objects.all()


class LocationViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.LocationSerializer
    queryset = Location.objects.all()
