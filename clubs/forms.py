from collections import OrderedDict

from django import forms
from django.core.exceptions import ValidationError
from django.db.models import Cha<PERSON><PERSON>ield
from django.db.models.functions import Lower
from django.forms.models import model_to_dict
from django.utils.translation import gettext_lazy as _

from accounts.models import AccountContact
from contacts.models import Contact
from hive.forms import BaseForm
from students.forms import StudentForm as BaseStudentForm
from users import forms as user_forms
from users.models import User

from . import constants

CharField.register_lookup(Lower)


class UserCreationForm(BaseForm, user_forms.UserCreationForm):
    signup_type = forms.CharField(widget=forms.HiddenInput)
    next = forms.CharField(widget=forms.HiddenInput, required=False)
    dob_year = forms.CharField(widget=forms.HiddenInput, required=False)
    dob_month = forms.CharField(widget=forms.HiddenInput, required=False)
    dob_day = forms.CharField(widget=forms.HiddenInput, required=False)

    class Meta(user_forms.UserCreationForm.Meta):
        fields = ("email", "signup_type")


class StudentForm(BaseStudentForm):
    def __init__(self, *args, **kwargs):
        club = kwargs.pop("club")
        super().__init__(*args, **kwargs)

        # Make school student id required for clubs at school
        # NOTE: if for some reason we want make this requirement more club-specific in the future,
        # we would need to add a boolean field to clubs for `require_school_student_id`, and check
        # that here.
        if club.location.type == "SCHOOL":
            self.fields["school_student_id"].required = True
        else:
            self.fields["school_student_id"].help_text += " (if applicable)"

        # make grade level required on clubs signup page
        self.fields["grade_level"].required = True

        self.update_fields()


class ContactForm(BaseForm, forms.ModelForm):
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        self.base_fields["email"].required = True

        super().__init__(*args, **kwargs)

    class Meta:
        model = Contact
        fields = [
            "first_name",
            "last_name",
            "address1",
            "address2",
            "city",
            "state",
            "postal_code",
            "email",
            "phone",
            "alt_phone",
            "company",
            "military_status",
            "preferred_language",
        ]

    def clean_email(self):
        email = self.cleaned_data["email"]
        if email is None:
            return
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            pass
        else:
            if user != self.user and user != getattr(self.instance, "user", None):
                raise ValidationError(
                    _("This email address is being used by another account")
                )
        return email


class AccountContactForm(ContactForm):
    def __init__(self, *args, **kwargs):
        self.base_fields["phone"].required = True

        initial = kwargs.get("initial") or {}
        instance = kwargs.get("instance")

        if not instance and initial.get("contact_ptr"):
            default_initial = model_to_dict(initial["contact_ptr"])
            initial = dict(initial, **default_initial)
            kwargs["initial"] = initial

        super().__init__(*args, **kwargs)

        # If the existing contact has an associated user account,
        # do not allow change of email address.
        if instance and hasattr(instance, "user"):
            self.fields["email"].disabled = True
            self.fields["email"].help_text = _(
                "This contact is associated with a user log-in. To change your email "
                'address, select "My Profile" from the main menu.'
            )

    class Meta:
        model = AccountContact
        fields = [
            "first_name",
            "last_name",
            "address1",
            "address2",
            "city",
            "state",
            "postal_code",
            "email",
            "phone",
            "alt_phone",
            "company",
            "military_status",
            "relationship",
            "legal_guardian",
            "preferred_language",
            "emergency_contact",
            "can_volunteer",
        ]


class RegistrationForm(BaseForm, forms.Form):
    payment_method = forms.ChoiceField(
        widget=forms.RadioSelect,
        choices=(
            ("ONE_TIME", _("Pay in full (${amount}) using credit card")),
            (
                "MONTHLY",
                _("Pay in {num} monthly payments (${amount}/month) using credit card"),
            ),
            ("ASSISTANCE", _("I would like to apply for financial assistance")),
        ),
    )
    accept_terms = forms.BooleanField(required=True)
    accept_attendance_agreement = forms.BooleanField(required=True)
    referred_by = forms.ChoiceField(
        label=_("How did you hear about us?"),
        widget=forms.RadioSelect,
        required=False,
        choices=constants.REFERRED_BY_CHOICES,
    )
    apply_for_laptops = forms.BooleanField(
        required=False,
        label=_(
            "I would like to apply to receive laptops for one or more of the above students."
        ),
    )
    opt_in = forms.BooleanField(
        required=False, label=_("Subscribe to our mailing list?")
    )

    def __init__(self, cart, *args, **kwargs):
        account = kwargs.pop("account", None)

        super().__init__(*args, **kwargs)

        if account.signup_type == "SELF":
            self.fields["accept_terms"].required = False

        if cart.total == 0:
            self.fields["payment_method"].required = False
            return

        # Everything below deals w/ payment fields
        self.fields["payment_method"].required = True

        # Put choices into an ordered dict so we can format the labels
        choices = OrderedDict(self.fields["payment_method"].choices)
        choices["ONE_TIME"] = choices["ONE_TIME"].format(amount=cart.total)

        # FIXME: Right now we assume all items in cart are the same with
        # regard to whether or not monthly payments are allowed (for example, we
        # allow monthly for ideaSpark, but a summer camp would likely be a
        # one-time payment). We may need a different solution if the cart
        # contains mixed registrations where some allow monthly and others don't
        self.num_monthly_payments = 1
        if any(item["club"].allow_monthly_payments for item in cart):
            self.num_monthly_payments = max(
                item["club"].num_monthly_payments or 1 for item in cart
            )
            self.monthly_price = cart.total / self.num_monthly_payments
            choices["MONTHLY"] = choices["MONTHLY"].format(
                num=self.num_monthly_payments, amount=self.monthly_price
            )

        # re-assign the update choice labels
        self.fields["payment_method"].choices = choices.items()

        self.update_fields()


class FinancialAssistanceForm(BaseForm, forms.Form):
    total_family_income = forms.ChoiceField(choices=constants.INCOME_CHOICES)
    num_people_supported_by_income = forms.IntegerField(widget=forms.NumberInput)
    num_dependent_children = forms.IntegerField(widget=forms.NumberInput)
    extraordinary_circumstances = forms.CharField(widget=forms.Textarea, required=False)
    amount_requested = forms.DecimalField(
        max_digits=6, decimal_places=2, required=False
    )
    received_previously = forms.ChoiceField(
        widget=forms.RadioSelect, choices=(("No", _("No")), ("Yes", _("Yes")))
    )
    has_family_or_school_issued_laptop = forms.ChoiceField(
        required=False,
        widget=forms.RadioSelect,
        choices=(("No", _("No")), ("Yes", _("Yes"))),
    )
    laptop_extra_info = forms.CharField(required=False, widget=forms.Textarea)

    def __init__(self, registration_form, *args, **kwargs):
        self._registration_form = registration_form
        super().__init__(*args, **kwargs)

    def clean_amount_requested(self):
        payment_method = self._registration_form.cleaned_data.get("payment_method")
        value = self.cleaned_data.get("amount_requested")
        if payment_method == "ASSISTANCE" and not value:
            raise ValidationError(_("This field is required."))
        return value


class InvoicePaymentForm(BaseForm, forms.Form):
    payment_amount = forms.DecimalField(max_digits=6, decimal_places=2)
