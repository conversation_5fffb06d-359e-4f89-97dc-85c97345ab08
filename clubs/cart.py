import decimal
import json

from django.contrib import messages
from django.core import serializers
from django.core.serializers.base import DeserializationError
from django.utils.translation import gettext_lazy as _

from clubs.models import Club, DiscountCode


def make_serializable(obj):
    return json.loads(json.dumps(obj, default=str))


class SessionCart:
    session_key = "cart"

    def __init__(self, request):
        self._request = request

    def apply_discount_code(self, code):
        try:
            discount = DiscountCode.objects.with_num_used().get(code__iexact=code)
        except DiscountCode.DoesNotExist:
            messages.add_message(
                self._request,
                messages.ERROR,
                _("The discount code you entered is not valid."),
            )
            return
        if not self.discount_is_available(discount):
            messages.add_message(
                self._request,
                messages.ERROR,
                _(f'We\'re sorry, discount "{discount.name}" is no longer available.'),
            )
            return
        messages.add_message(
            self._request,
            messages.INFO,
            _("Discount applied: {}".format(discount.name)),
        )
        self._request.session[self.session_key + "-discount"] = discount.pk
        return discount

    def discount_is_available(self, discount):
        return discount.limit is None or discount.num_used < discount.limit

    @property
    def discount(self):
        discount_pk = self._request.session.get(self.session_key + "-discount")
        if discount_pk:
            try:
                return DiscountCode.objects.with_num_used().get(pk=discount_pk)
            except DiscountCode.DoesNotExist:
                pass
        return None

    def remove_discount(self):
        self._request.session[self.session_key + "-discount"] = None

    def add(self, club, student, form_data, existing_studentregistration=None):
        cart_items = self._request.session.get(self.session_key, [])

        serialized_student = serializers.serialize("json", [student])
        cart_item = {
            "club_id": club.id,
            "student": serialized_student,
            "form_data": make_serializable(form_data),
            "existing_studentregistration": None,
        }
        if existing_studentregistration:
            cart_item["studentregistration_id"] = existing_studentregistration.id
        cart_items.append(cart_item)

        self._request.session[self.session_key] = cart_items
        return cart_items

    def remove(self, index):
        session_cart = self._data
        del session_cart[int(index)]
        self._data = session_cart

    def clear(self):
        self._request.session[self.session_key] = []
        self._request.session[self.session_key + "-discount"] = None

    @property
    def subtotal(self):
        return sum(item["club"].fee for item in self)

    @property
    def discount_amount(self):
        amount = decimal.Decimal("0.00")
        if self.discount:
            return self.discount.calculate(self.subtotal)
        return round(amount, 2)

    @property
    def total(self):
        return round(self.subtotal - self.discount_amount, 2)

    @property
    def _data(self):
        return self._request.session.get(self.session_key, [])

    @_data.setter
    def _data(self, value):
        self._request.session[self.session_key] = value

    def __iter__(self):
        for item in self._data:
            try:
                deserialized_student = next(
                    serializers.deserialize("json", item["student"])
                )
            except DeserializationError:
                continue
            yield {
                "club": Club.objects.get(pk=item["club_id"]),
                "student": deserialized_student.object,
                "form_data": item["form_data"],
                "studentregistration_id": item.get("studentregistration_id"),
            }

    def __len__(self):
        return len([i for i in self])
