from django.utils.translation import gettext_lazy as _

from students.constants import COUNTIES, INCOME_CHOICES, SHIRT_SIZE_CHOICES

PERIOD_CHOICES = (
    ("SCHOOL_YEAR", _("School Year")),
    ("SUMMER", _("Summer")),
    ("FALL", _("Fall")),
    ("SPRING", _("Spring")),
)

ENROLLMENT_TYPES = (("PUBLIC", "Public Enrollment"), ("PRIVATE", "Private Enrollment"))

PAYMENT_METHODS = (
    ("ONLINE", _("Online")),
    # ('INVOICE', _('Invoice')),
    ("ASSISTANCE", _("Financial Assistance")),
)

PAYMENT_FREQUENCIES = (("ONE_TIME", _("One-time")), ("MONTHLY", _("Monthly")))

PREVIOUS_ACTIVITIES_CHOICES = [
    (n, n)
    for n in (
        _("Code.org's Hour of Code"),
        _("Online tutorials, like Khan Academy, Code Academy, or Scratch"),
        _("Classes in school"),
        _("A previous Bold Idea club or workshop"),
        _("Workshops from another organization"),
    )
]

REFERRED_BY_CHOICES = [
    (n, n)
    for n in (
        _("Internet search"),
        _("Information Table"),
        _("School or Rec Center Activity/Program Guide"),
        _("Flyer"),
        _("Social Media"),
        _("Friend / Family"),
        _("Special Event"),
        "ATW ileadinSTEM",
        _("Other"),
    )
]

REGISTRATION_STATUS_CHOICES = (
    ("COMPLETE", _("Complete")),
    ("PENDING", _("Pending")),
    ("CANCELED", _("Canceled")),
)

LOCATION_TYPE_CHOICES = (
    ("SCHOOL", "School"),
    ("COMMUNITY", "Community center / youth org"),
    ("VIRTUAL", "Virtual club (online)"),
    ("OTHER", "Other"),
)
