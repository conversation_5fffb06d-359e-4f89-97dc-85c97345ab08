import decimal
from datetime import date, datetime

import vinaigrette
from django.conf import settings
from django.contrib.gis.db.models import PointField
from django.contrib.postgres.fields import J<PERSON>NField
from django.core.exceptions import ValidationError
from django.db import models
from django.template import Context, Template
from django.template.defaultfilters import slugify
from django.urls import reverse
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from localflavor.us import models as lf_models

from accounts.models import Account
from clubs.utils import (
    create_student_chat_channel,
    get_current_school_year,
    get_period_description,
    setup_chat,
)
from courses.constants import CODING_ENVIRONMENT_CHOICES
from courses.models import BaseResourceLink, Course, UserRoadmap
from hive import utils
from students.models import Student, StudentSegment
from students.utils import get_grade_name
from users.models import User
from volunteers.constants import MENTOR_ROLES
from volunteers.models import (
    Organization,
    Volunteer,
    VolunteerAssignment,
    VolunteerRequirementType,
)

from . import constants
from .query import (
    AnnouncementQuerySet,
    AttendanceQuerySet,
    ClubManager,
    DiscountCodeQuerySet,
    PeriodQuerySet,
    SessionQuerySet,
    StudentRegistrationQuerySet,
    VolunteerAttendanceQuerySet,
)


class Location(models.Model):
    date_added = models.DateTimeField(default=timezone.now)
    date_modified = models.DateTimeField(auto_now=True)
    name = models.CharField(max_length=255)
    nickname = models.CharField(max_length=255, blank=True)
    type = models.CharField(
        max_length=32, choices=constants.LOCATION_TYPE_CHOICES, default="OTHER"
    )
    about = models.TextField(blank=True)
    logo = models.ImageField(upload_to="clubs/location_logos", blank=True)
    photo = models.ImageField(upload_to="clubs/location_photos", blank=True)
    website = models.URLField(blank=True)
    address1 = models.CharField(max_length=255, blank=True)
    address2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=255, blank=True)
    state = lf_models.USStateField(blank=True)
    postal_code = lf_models.USZipCodeField(blank=True)
    phone = models.CharField(max_length=32, blank=True)
    extension = models.CharField(max_length=10, blank=True)
    instructions = models.TextField("Parking / other instructions", blank=True)
    student_segment = models.ForeignKey(
        StudentSegment,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        help_text=(
            "Students who register at this location will be automatically added to the"
            "selected segment"
        ),
    )
    notes = models.TextField(blank=True)
    geo = PointField(null=True, blank=True)

    def save(self, *args, **kwargs):
        self.date_modified = timezone.now()
        super().save(*args, **kwargs)

    @property
    def short_name(self):
        return self.nickname or self.name

    @property
    def full_address(self):
        if self.address1 and self.city and self.state and self.postal_code:
            address = self.address1
            if self.address2:
                address += "\n" + self.address2
            address += "\n{}, {} {}".format(self.city, self.state, self.postal_code)
            return address
        return None

    @property
    def short_address(self):
        if self.address1 and self.city:
            address = self.address1
            if self.address2:
                address += ", " + self.address2
            return address
        return None

    @property
    def coords(self):
        return self.geo.coords

    def __str__(self):
        return self.name

    class Meta:
        ordering = ["name"]


vinaigrette.register(Location, ["about", "instructions"])


class ClubCategory(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField()
    photo = models.ImageField(upload_to="clubs/category_photos", blank=True)
    short_description = models.TextField(
        blank=True, help_text="Shown on category selection page (markdown supported)"
    )
    description = models.TextField(
        blank=True,
        help_text="Shown at top of club selection page for this category (markdown supported)",
    )
    learn_more_url = models.URLField(blank=True)
    order = models.IntegerField(default=0)
    require_location_selection = models.BooleanField(
        default=False,
        help_text="Show a location selection screen before club selection",
    )
    hide_when_empty = models.BooleanField(
        default=False, help_text="Hide category when there are no more clubs to show"
    )
    status = models.CharField(
        max_length=16,
        choices=(
            ("DRAFT", "Draft"),
            ("UNLISTED", "Unlisted"),
            ("PUBLISHED", "Published"),
            ("ARCHIVED", "Archived"),
        ),
        default="DRAFT",
    )

    class Meta:
        ordering = ["order"]
        verbose_name_plural = "Club categories"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return utils.site_reverse(
            "clubs", "clubs:category-index", kwargs={"category": self.slug}
        )

    def get_age_range_description(self, include_hidden=False):
        values = {}
        clubs = self.clubs.open_for_registration()
        if not include_hidden:
            clubs = clubs.exclude(hide_on_registration=True)
        for club in clubs:
            min_grade = club.min_grade or 0
            max_grade = club.max_grade or 0
            min_age = club.min_age or 0
            max_age = club.max_age or 0
            if "min_grade" not in values or min_grade < values["min_grade"]:
                values["min_grade"] = min_grade
            if "max_grade" not in values or max_grade > values["max_grade"]:
                values["max_grade"] = max_grade
            if "min_age" not in values or min_age < values["min_age"]:
                values["min_age"] = min_age
            if "max_age" not in values or max_age > values["max_age"]:
                values["max_age"] = max_age

        return utils.get_age_range_description(**values)

    @property
    def age_range_description(self):
        return self.get_age_range_description()


vinaigrette.register(ClubCategory, ["name", "description"])


class Club(models.Model):
    category = models.ForeignKey(
        ClubCategory, related_name="clubs", on_delete=models.PROTECT
    )
    name = models.CharField(max_length=255)
    club_code = models.CharField(max_length=32, unique=True, db_index=True)
    courses = models.ManyToManyField(Course, related_name="clubs", blank=True)
    base_description = models.TextField(
        "Description",
        blank=True,
        help_text="template tags available: {{location}}, {{course}}",
    )
    location = models.ForeignKey(
        Location, related_name="clubs", on_delete=models.PROTECT, null=True, blank=True
    )
    location_info = models.TextField("Location info / parking instructions", blank=True)
    photo = models.ImageField(upload_to="clubs/club_photos", blank=True)
    registration_url = models.URLField(blank=True)
    hide_on_registration = models.BooleanField(default=False)
    fee = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    allow_monthly_payments = models.BooleanField()
    num_monthly_payments = models.IntegerField(null=True, blank=True)
    allow_self_signup = models.BooleanField(default=False)
    min_age = models.IntegerField(null=True, blank=True)
    max_age = models.IntegerField(null=True, blank=True)
    min_grade = models.IntegerField(null=True, blank=True)
    max_grade = models.IntegerField(null=True, blank=True)
    num_student_seats = models.IntegerField("# Student seats")
    student_volunteer_ratio = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=2.0,
        help_text="Ideal number of students per volunteer (for calculating coverage)",
    )
    num_volunteers_needed = models.PositiveIntegerField(
        "# Volunteers needed", help_text="Including mentors and team captains"
    )
    period = models.CharField(max_length=32, choices=constants.PERIOD_CHOICES)
    start_date = models.DateField(null=True, blank=True)
    registration_open_date = models.DateField(null=True, blank=True)
    registration_close_date = models.DateField(null=True, blank=True)
    schedule_description = models.CharField(
        max_length=255, blank=True, help_text="eg: Tuesdays at 2:00pm"
    )
    hide_on_volunteer_signup = models.BooleanField(default=False)
    volunteer_requirements = models.ManyToManyField(
        VolunteerRequirementType, blank=True, related_name="clubs"
    )
    min_volunteer_age = models.IntegerField(null=True, blank=True)
    volunteer_fall_signup_deadline = models.DateField(
        "Fall signup deadline",
        null=True,
        blank=True,
        help_text="After this date, volunteers can only sign up for the spring semester.",
    )
    students = models.ManyToManyField(Student, through="StudentRegistration")
    volunteers = models.ManyToManyField(
        Volunteer, through="volunteers.VolunteerAssignment"
    )
    enable_chat = models.BooleanField(default=False)  # not currently used
    canceled = models.BooleanField(default=False)
    coding_environment_override = models.CharField(
        choices=CODING_ENVIRONMENT_CHOICES, max_length=32, blank=True, null=True
    )
    online = models.BooleanField(default=False)
    laptop_program = models.BooleanField(default=False)
    partner_organizations = models.ManyToManyField(Organization, blank=True)

    objects = ClubManager()

    class Meta:
        ordering = ["-pk"]
        base_manager_name = "objects"

    def __str__(self):
        desc = "{0.club_code} - {0.name}".format(self)
        if self.canceled:
            desc += " (canceled)"
        return desc

    def clean(self):
        if self.allow_monthly_payments and self.num_monthly_payments is None:
            raise ValidationError(
                "num_monthly_payments is required when allow_monthly_payments "
                "is True"
            )

        if self.period == "SCHOOL_YEAR" and not self.volunteer_fall_signup_deadline:
            raise ValidationError(
                "Please specify the fall signup deadline for volunteers"
            )

    @property
    def description(self):
        context = Context({"location": self.location, "course": self.course})
        tpl = Template(self.base_description)
        return tpl.render(context)

    @property
    def schedule_description_with_date(self):
        if self.sessions.count() == 0:
            return self.schedule_description
        return "{schedule_description} - starts {start_date:%b %-d}".format(
            schedule_description=self.schedule_description, start_date=self.start_date
        )

    @property
    def course(self):
        if self.courses.count() == 1:
            return self.courses.all()[0]
        return None

    @property
    def default_photo(self):
        if self.photo:
            return self.photo
        if self.location and self.location.photo:
            return self.location.photo
        if self.course and self.course.photo:
            return self.course.photo
        return self.category.photo

    @property
    def is_full(self):
        if self.num_student_seats is not None:
            active_registrations = self.student_registrations.filter(
                status="COMPLETE"
            ).count()
            return active_registrations >= self.num_student_seats
        return False

    @property
    def first_session(self):
        if self.sessions.filter(type="CLUB").count() > 0:
            return self.sessions.filter(type="CLUB").order_by("date")[0]

    @property
    def last_session(self):
        if self.sessions.filter(type="CLUB").count() > 0:
            return self.sessions.filter(type="CLUB").order_by("-date")[0]

    @property
    def add_to_cart_url(self):
        return reverse(
            "clubs:cart-add",
            kwargs={"category": self.category.slug, "club_code": self.club_code},
        )

    @property
    def age_range_description(self):
        return utils.get_age_range_description(
            min_grade=self.min_grade,
            max_grade=self.max_grade,
            min_age=self.min_age,
            max_age=self.max_age,
        )

    @property
    def period_description(self):
        return get_period_description(self.year, self.period)

    def get_url(self):
        return reverse("dashboard:club", kwargs={"club_id": self.id})

    @property
    def student_registration_url(self):
        # FIXME: use URL resolver for club instead of hard-coding
        clubs_domain = settings.DOMAINS["clubs"]
        return f"http://{clubs_domain}/{self.category.slug}/{self.club_code}/"

    @property
    def student_slots(self):
        """
        Returns an array of "slots" a minimum size of `student_seats`, where
        each slot is either a `Registration` object or `None`.
        """
        slots = [None for i in range(0, self.num_student_seats or 0)]
        student_registrations = self.student_registrations.order_by("-student__gender")
        student_registrations = student_registrations.exclude(status="CANCELED")
        for i, studentreg in enumerate(student_registrations):
            if i >= len(slots):
                slots.append(studentreg)
            else:
                slots[i] = studentreg
        return slots

    @property
    def volunteer_slots(self):
        """
        Returns an array of "slots" with a minimum size of `num_volunteers_needed`,
        where each slot is either a `Volunteer` object or `None`. The volunteer
        objects are also annotated with `.signup`, which is the matching
        `VolunteerSignup` object associated with this club.

        This only includes volunteers who have signed up for specific sessions within
        the club.
        """
        # Show only regular mentors in volunteer slots
        volunteers = Volunteer.objects.filter(
            assignments__club=self, assignments__role__in=MENTOR_ROLES
        ).distinct()
        slots = [None for i in range(0, self.num_volunteers_needed or 0)]
        for i, volunteer in enumerate(volunteers):
            volunteer.assignment = volunteer.get_assignment(self)
            signups = volunteer.signups.filter(assignments__club=self)
            if signups.count() > 0:
                volunteer.signup = signups[0]
            if i >= len(slots):
                slots.append(volunteer)
            else:
                slots[i] = volunteer
        return slots

    @property
    def is_open_for_registration(self):
        today = timezone.now().date()
        return (
            self.registration_open_date is None or self.registration_open_date <= today
        ) and (
            self.registration_close_date is None or self.registration_close_date > today
        )

    @cached_property
    def team_captains(self):
        return self.get_volunteers_for_role("TEAM_CAPTAIN")

    @property
    def volunteer_start_date(self):
        """
        For SHOOL_YEAR clubs, if fall signup deadline has passed, give the date of the first
        session for spring. Otherwise return the club start date.
        """
        is_past_fall_deadline = (
            self.volunteer_fall_signup_deadline is not None
            and self.volunteer_fall_signup_deadline < timezone.now().date()
        )
        if self.period == "SCHOOL_YEAR" and is_past_fall_deadline:
            spring_sessions = self.sessions.with_semester().filter(semester="SPRING")
            if spring_sessions.count():
                return spring_sessions.order_by("date")[0].date
        return self.start_date

    def get_volunteers_for_role(self, role):
        assignments = self.volunteer_assignments.filter(role=role)
        return Volunteer.objects.filter(pk__in=assignments.values("volunteer"))

    @cached_property
    def total_hours(self):
        sessions = self.sessions.filter(type="CLUB")
        return sum(s.duration for s in sessions if s.duration is not None) / 60

    @cached_property
    def completed_hours(self):
        sessions = self.sessions.filter(type="CLUB", date__lt=timezone.now())
        return sum(s.duration for s in sessions if s.duration is not None) / 60

    # def get_resource_links(self, **filter_kwargs):
    #     # Combine course resource links with club resource links.
    #     club_links = self.club_resource_links.filter(**filter_kwargs)
    #     club_slugs = club_links.values_list('slug', flat=True)
    #     if self.course:
    #         course_links = self.course.resource_links.filter(**filter_kwargs)
    #         # Club links with the same slug override course links.
    #         course_links = course_links.exclude(slug__in=club_slugs)
    #         return club_links.union(course_links).order_by('type', 'order')
    #     return club_links.order_by('type', 'order')

    # @property
    # def coding_environment(self):
    #     if self.coding_environment_override:
    #         return self.coding_environment_override
    #     if self.course:
    #         return self.course.coding_environment
    #     return None

    # @property
    # def coding_environment_url(self):
    #     from courses.utils import get_coding_environment_url
    #     return get_coding_environment_url(self.coding_environment)

    # def get_coding_environment_display(self):
    #     if self.coding_environment_override:
    #         return self.get_coding_environment_override_display()
    #     if self.course:
    #         return self.course.get_coding_environment_display()
    #     return ''

    def get_absolute_url(self):
        return self.get_url()

    def natural_key(self):
        return self.club_code

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if self.enable_chat:
            setup_chat(self)

    def renumber_sessions(self):
        self.sessions.update(number=None)

        for i, session in enumerate(self.sessions.filter(type="CLUB").order_by("date")):
            session.number = i + 1
            session.save()
            if session.number == 1:
                session.club.start_date = session.date
                session.club.save()


vinaigrette.register(
    Club, ["base_description", "location_info", "schedule_description"]
)


class ResourceLink(BaseResourceLink):
    club = models.ForeignKey(
        Club, on_delete=models.CASCADE, related_name="club_resource_links"
    )

    class Meta:
        unique_together = ["club", "slug"]


class Announcement(models.Model):
    user = models.ForeignKey(User, on_delete=models.PROTECT)
    clubs = models.ManyToManyField(Club, related_name="announcements")
    publish_date = models.DateField(blank=True, default=timezone.now)
    archive_date = models.DateField(blank=True, null=True)
    pinned = models.BooleanField(default=False)
    visibility = models.CharField(
        max_length=32,
        choices=(
            ("ANYONE", "Anyone"),
            ("STUDENTS", "Students only"),
            ("VOLUNTEER", "Volunteers only"),
        ),
        default="ANYONE",
    )
    icon = models.CharField(max_length=32, blank=True)
    icon_color = models.CharField(max_length=32, blank=True)
    text = models.TextField()

    objects = AnnouncementQuerySet.as_manager()

    class Meta:
        ordering = ["pinned", "-publish_date"]

    @property
    def is_current(self):
        now = timezone.now()
        return (self.publish_date is None or self.publish_date <= now) and (
            self.archive_date is None or self.archive_date > now
        )


def _to_date(date_val):
    if hasattr(date_val, "hour"):
        return date_val.date()
    return date_val


class DateMixin:
    date_attr = "date"

    def _get_now(self):
        date_val = getattr(self, self.date_attr)
        if not hasattr(date_val, "hour"):
            return timezone.now().date()
        return timezone.now()

    def is_future(self):
        return getattr(self, self.date_attr) > self._get_now()

    def is_past(self):
        return getattr(self, self.date_attr) < self._get_now()

    def is_future_date(self):
        return _to_date(getattr(self, self.date_attr)) > _to_date(self._get_now())

    def is_past_date(self):
        return _to_date(getattr(self, self.date_attr)) < _to_date(self._get_now())

    def is_today(self):
        return _to_date(getattr(self, self.date_attr)) == _to_date(self._get_now())


class Session(DateMixin, models.Model):
    club = models.ForeignKey(Club, related_name="sessions", on_delete=models.CASCADE)
    type = models.CharField(
        max_length=32,
        choices=(("CLUB", "Club session"), ("NO_SESSION", "Holiday / No session")),
        default="CLUB",
    )
    number = models.PositiveIntegerField(null=True)
    date = models.DateField()
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    description = models.CharField(max_length=255, blank=True)
    canceled = models.BooleanField(default=False)
    conference_link = models.URLField(
        blank=True, max_length=settings.MAX_VARCHAR_LENGTH
    )
    conference_password = models.CharField(max_length=16, blank=True)
    duration = models.IntegerField(null=True, blank=True, editable=False)
    start_datetime = models.DateTimeField(null=True, blank=True)
    end_datetime = models.DateTimeField(null=True, blank=True)

    objects = SessionQuerySet.as_manager()

    date_attr = "start_datetime"  # see DateMixin

    class Meta:
        ordering = ["date"]
        unique_together = ["club", "type", "number"]

    def __str__(self):
        date = utils.format_date(self.date, "N j, Y")
        if self.number:
            return f"Session #{self.number} - {date}"
        return date

    @property
    def is_today(self):
        return self.date == timezone.now().date()

    @property
    def date_description(self):
        datestr = utils.format_date(self.date, "N j, Y")
        if self.start_time:
            datestr += ", " + self.time_description
        return datestr

    @property
    def time_description(self):
        if not self.start_time:
            return ""
        timestr = utils.format_date(self.start_time, "P")
        if self.end_time:
            timestr += " - " + utils.format_date(self.end_time, "P")
        return timestr

    @property
    def datetime(self):
        return self.start_datetime

    def get_remaining_volunteers_needed(self):
        """
        Number of remaining volunteers needed
        """
        if hasattr(self, "remaining_volunteers_needed"):
            return self.remaining_volunteers_needed
        needed = self.num_volunteers_needed - self.volunteer_attendances.count()
        return needed > 0 and needed or 0

    @cached_property
    def students_attended(self):
        return [a.student for a in self.student_attendances.filter(present=True)]

    @cached_property
    def volunteers_attended(self):
        return [a.volunteer for a in self.volunteer_attendances.filter(present=True)]

    @cached_property
    def volunteers(self):
        return [a.volunteer for a in self.volunteer_attendances.all()]

    @property
    def num_students_attended(self):
        return self.student_attendances.filter(present=True).count()

    @property
    def num_volunteers_attended(self):
        return self.volunteer_attendances.filter(present=True).count()

    @property
    def coverage_percent(self):
        """
        Returns coverage percentage based on num_student_seats, student_volunteer_ratio, and
        planned attendance. For actual coverage percentage that includes only attendances, use
        `attended_coverage_percent`.
        """
        num_volunteers = self.volunteer_attendances.count()
        num_volunteers_needed = (
            self.club.num_student_seats / self.club.student_volunteer_ratio
        )
        if num_volunteers_needed == 0:
            return 0
        percent = round(num_volunteers / num_volunteers_needed * 100)
        if percent >= 100:
            return 100
        return percent

    @property
    def attended_coverage_percent(self):
        if len(self.students_attended) == 0:
            return None
        if len(self.volunteers_attended) == 0:
            return 0
        mentors_needed = len(self.students_attended) / self.club.student_volunteer_ratio
        percent = round(len(self.volunteers_attended) / mentors_needed * 100)
        if percent >= 100:
            return 100
        return percent

    def save(self, *args, **kwargs):
        if self.start_time and self.end_time:
            end_dt = datetime.combine(date.today(), self.end_time)
            start_dt = datetime.combine(date.today(), self.start_time)
            self.duration = (end_dt - start_dt).total_seconds() / 60

        if self.date:
            start_time = self.start_time or datetime.min.time()
            end_time = self.end_time or datetime.min.time()
            self.start_datetime = timezone.make_aware(
                datetime.combine(self.date, start_time)
            )
            self.end_datetime = timezone.make_aware(
                datetime.combine(self.date, end_time)
            )

        super().save(*args, **kwargs)


class StudentAttendance(DateMixin, models.Model):
    """
    An attendance instance represents an expectation for a person to attend a session.
    """

    student = models.ForeignKey(
        Student, related_name="session_attendances", on_delete=models.PROTECT
    )
    session = models.ForeignKey(
        Session, related_name="student_attendances", on_delete=models.PROTECT
    )
    # orig_session is used when student has moved from another club where they have attendances,
    # but the attendances record needs to be reflected on the new club
    orig_session = models.ForeignKey(
        Session,
        null=True,
        blank=True,
        related_name="orig_student_attendances",
        on_delete=models.PROTECT,
    )
    present = models.BooleanField(default=False)
    notes = models.TextField()

    objects = AttendanceQuerySet.as_manager()

    @property
    def status(self):
        return self.present and "PRESENT" or "ABSENT"

    def get_status_display(self):
        return self.present and "Present" or "Absent"

    @property
    def date(self):
        return self.session.date

    class Meta:
        unique_together = ["student", "session"]


class VolunteerAttendance(DateMixin, models.Model):
    """
    An attendance instance represents an expectation for a person to attend a session.
    """

    volunteer = models.ForeignKey(
        Volunteer, related_name="session_attendances", on_delete=models.CASCADE
    )
    assignment = models.ForeignKey(
        VolunteerAssignment,
        related_name="session_attendances",
        on_delete=models.CASCADE,
    )
    session = models.ForeignKey(
        Session, related_name="volunteer_attendances", on_delete=models.CASCADE
    )
    present = models.BooleanField(default=False)
    notes = models.TextField()

    objects = VolunteerAttendanceQuerySet.as_manager()

    class Meta:
        unique_together = ["volunteer", "session"]
        ordering = ["session__date"]

    def save(self, *args, **kwargs):
        if not self.assignment_id:
            club = self.session.club
            self.assignment = self.volunteer.get_assignment(club)
        super().save(*args, **kwargs)

    @property
    def date(self):
        return self.session.date

    # @property
    # def assignment(self):
    #     return self.volunteer.get_assignment(self.session.club)

    def __str__(self):
        status = self.present and "PRESENT" or "ABSENT"
        if self.is_future_date():
            return "{:%Y-%m-%d} - {}".format(self.session.date, self.volunteer)
        else:
            return "{:%Y-%m-%d} - {}: {}".format(
                self.session.date, self.volunteer, status
            )


class Registration(models.Model):
    date = models.DateTimeField(default=timezone.now)
    account = models.ForeignKey(
        Account, related_name="registrations", on_delete=models.CASCADE
    )
    invoice_number = models.CharField(max_length=32, blank=True)
    payment_method = models.CharField(
        max_length=32, choices=constants.PAYMENT_METHODS, blank=True
    )
    num_payments = models.IntegerField(null=True, blank=True)
    payment_frequency = models.CharField(
        max_length=32, choices=constants.PAYMENT_FREQUENCIES, blank=True
    )
    payment_ref = models.CharField(max_length=128, blank=True)
    students = models.ManyToManyField(Student, through="StudentRegistration")
    clubs = models.ManyToManyField(Club, through="StudentRegistration")
    financial_assistance_requested = models.DecimalField(
        max_digits=6, decimal_places=2, null=True, blank=True
    )
    financial_assistance_granted = models.DecimalField(
        max_digits=6, decimal_places=2, null=True, blank=True
    )
    discount = models.ForeignKey(
        "DiscountCode",
        related_name="registrations",
        null=True,
        on_delete=models.SET_NULL,
    )
    discount_description = models.CharField(max_length=128, blank=True)
    form_data = JSONField(blank=True, null=True)

    objects = PeriodQuerySet.as_manager("clubs")

    def __str__(self):
        return "#{} - {}".format(self.registration_number, self.account.default_contact)

    @property
    def registration_number(self):
        return "{:0>6}".format(self.id)

    @property
    def invoice_total(self):
        return sum(s.club.fee for s in self.student_registrations.all())

    def save(self, *args, **kwargs):
        if self.discount and not self.discount_description:
            self.discount_description = str(self.discount)
        super().save(*args, **kwargs)

    def get_invoice_url(self):
        # FIXME: This should link to a local URL that requires QBO login to get the InvoiceID, then
        # redirect to the QBO link
        #
        # from billing.invoicing import get_invoice_url as get_url
        # if self.invoice_number:
        #     return get_url(self.invoice_number)
        return None

    class Meta:
        ordering = ["-date"]


class StudentRegistration(models.Model):
    date = models.DateTimeField(default=timezone.now)
    registration = models.ForeignKey(
        Registration,
        related_name="student_registrations",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    student = models.ForeignKey(
        Student, related_name="registrations", on_delete=models.CASCADE
    )
    club = models.ForeignKey(
        Club, related_name="student_registrations", on_delete=models.CASCADE
    )
    status = models.CharField(
        max_length=32, choices=constants.REGISTRATION_STATUS_CHOICES, default="COMPLETE"
    )
    survey_data = JSONField(blank=True, null=True)
    extra_data = JSONField(blank=True, null=True)

    # A student can select a new course in the mid-club. For example, if they finish a course
    # before the end of the year and want to start a new one. We need to keep track of two things:
    # a) which course roadmap they're *currently* working on, and b) which course roadmaps they
    # worked on during the club. This allows us to filter course-related info (eg, badges earned)
    # based on time period, club, etc.
    #
    # When a student joins a course, the `current_course` field is saved. When a student begins a
    # roadmap (by checking off at least one step), `current_roadmap` is set and is also added to
    # `roadmaps`.
    current_course = models.ForeignKey(
        Course, null=True, blank=True, on_delete=models.PROTECT
    )
    current_roadmap = models.ForeignKey(
        UserRoadmap, null=True, blank=True, on_delete=models.SET_NULL
    )
    roadmaps = models.ManyToManyField(UserRoadmap, related_name="student_registrations")

    # The following fields are copied from the `Student` model upon registration. The fields in
    # this model represent a historical record of certain student data at the time of registration.
    # See `save()` method below.
    student_segments = models.ManyToManyField(
        "students.StudentSegment", blank=True, serialize=False
    )
    student_postal_code = lf_models.USZipCodeField(_("Postal code"), blank=True)
    student_county = models.CharField(
        _("County"), max_length=255, blank=True, choices=constants.COUNTIES
    )
    student_school = models.ForeignKey(
        "clubs.Location",
        verbose_name=_("School"),
        blank=True,
        null=True,
        limit_choices_to={"type": "SCHOOL"},
        on_delete=models.PROTECT,
    )
    student_school_other = models.CharField(
        _("School other"), max_length=255, blank=True
    )
    school_student_id = models.CharField(
        _("School student id"), max_length=255, blank=True
    )
    student_grade = models.PositiveIntegerField(
        "Student grade",
        blank=True,
        null=True,
        help_text="Student grade at time of registration",
    )
    student_shirt_size = models.CharField(
        _("Shirt size"), max_length=5, choices=constants.SHIRT_SIZE_CHOICES, blank=True
    )
    annual_income_min = models.PositiveIntegerField(null=True, blank=True)
    annual_income_max = models.PositiveIntegerField(null=True, blank=True)
    num_household_members = models.PositiveIntegerField(null=True, blank=True)

    objects = StudentRegistrationQuerySet.as_manager()

    def __str__(self):
        return "{} - {}".format(self.club, self.student)

    def get_student_age(self):
        return self.student.get_age(as_of=self.date)

    @property
    def student_grade_name(self):
        return get_grade_name(self.student_grade)

    def save(self, *args, **kwargs):
        # If we changed the course, re-set the roadmap to None (the CourseHandler will update it)
        if (
            self.current_roadmap is not None
            and self.current_course != self.current_roadmap.course
        ):
            self.current_roadmap = None

        super().save(*args, **kwargs)

        if self.club.enable_chat:
            create_student_chat_channel(self.student, self.club)

    def set_demographic_info(self):
        """
        Copies demographic info fields from associated `Student` record
        """
        self.student_segments.set(self.student.segments.all())
        self.student_postal_code = self.student.postal_code
        self.student_grade = self.student.get_grade(
            school_year=get_current_school_year(as_of=self.date)
        )
        self.student_county = self.student.county
        self.student_school = self.student.school
        self.student_school_other = self.student.school_other
        self.school_student_id = self.student.school_student_id
        self.annual_income_min = self.student.annual_income_min
        self.annual_income_max = self.student.annual_income_max
        self.num_household_members = self.student.num_household_members

    def get_absolute_url(self):
        return reverse(
            "dashboard:student-details",
            kwargs={"club_id": self.club.id, "student_id": self.student.id},
        )


class DiscountCode(models.Model):
    name = models.CharField(max_length=128)
    code = models.CharField(max_length=32)
    type = models.CharField(
        max_length=16,
        choices=(
            ("FREE", "Free"),
            ("FIXED", "Fixed discount"),
            ("PERCENT", "Percentage discount"),
        ),
    )
    amount = models.DecimalField(max_digits=6, decimal_places=2, default=0.00)
    limit = models.IntegerField(
        null=True,
        blank=True,
        help_text=(
            "Limits the number of total redemptions for this discount code. "
            "Leave empty for no limit."
        ),
    )

    objects = DiscountCodeQuerySet.as_manager()

    def __str__(self):
        return "{} - {} ({}: {})".format(self.code, self.name, self.type, self.amount)

    def calculate(self, subtotal):
        amount = decimal.Decimal("0.00")
        if self.type == "FREE":
            amount = subtotal
        elif self.type == "FIXED":
            amount = self.amount
        elif self.type == "PERCENT":
            amount = subtotal * (self.amount / 100)
        return round(amount, 2)
