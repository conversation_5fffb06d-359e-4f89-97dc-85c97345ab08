from django.contrib import admin

from clubs import models, utils
from courses.models import Course
from hive.admin import ShowMoreFilterMixin


class PeriodFilter(ShowMoreFilterMixin, admin.SimpleListFilter):
    title = "period"
    parameter_name = "period"
    show_initial = 4

    def queryset(self, request, queryset):
        val = self.value()
        if val is None:
            return queryset
        year, period = val.split("-")
        return queryset.for_period(year, period)

    def lookups(self, request, model_admin):
        yield from utils.get_periods_lookup()


class ShowMoreIfNoPeriodFilterMixin(ShowMoreFilterMixin):
    """
    If a period is selected, show up more options
    """

    def __init__(self, request, params, *args, **kwargs):
        super().__init__(request, params, *args, **kwargs)
        if request.GET.get("period"):
            self.show_initial = 30


class ClubDrilldownFilter(admin.SimpleListFilter):
    club_lookup = "club"
    lookup = None

    def _get_clubs(self, request):
        clubs = models.Club.objects.all()
        if "period" in request.GET:
            year, period = request.GET["period"].split("-")
            clubs = clubs.for_period(year, period)
        return clubs

    def _get_lookup(self):
        if self.club_lookup is None:
            return self.lookup
        return f"{self.club_lookup}__{self.lookup}"

    def queryset(self, request, queryset):
        val = self.value()
        if val is None:
            return queryset
        return queryset.filter(**{self._get_lookup(): val}).distinct()

    @classmethod
    def factory(cls, club_lookup="club"):
        return type(cls.__name__, (cls,), {"club_lookup": club_lookup})


class ClubCategoryFilter(ShowMoreIfNoPeriodFilterMixin, ClubDrilldownFilter):
    title = "club category"
    parameter_name = "category_id"
    lookup = "category_id"

    def lookups(self, request, model_admin):
        clubs = self._get_clubs(request)
        categories = models.ClubCategory.objects.filter(clubs__in=clubs).distinct()
        return ((c.id, str(c)) for c in categories)


class CourseFilter(ClubDrilldownFilter):
    title = "course"
    parameter_name = "course_id"
    lookup = "courses"

    def lookups(self, request, model_admin):
        clubs = self._get_clubs(request)
        if "category_id" in request.GET:
            clubs = clubs.filter(category_id=request.GET["category_id"])
        courses = Course.objects.filter(clubs__in=clubs).distinct()
        return ((c.id, str(c)) for c in courses)


class ClubFilter(ShowMoreIfNoPeriodFilterMixin, ClubDrilldownFilter):
    title = "club"
    parameter_name = "club_id"
    lookup = "id"

    def lookups(self, request, model_admin):
        clubs = self._get_clubs(request)
        if "category_id" in request.GET:
            clubs = clubs.filter(category_id=request.GET["category_id"])
        return ((c.id, c.name) for c in clubs)


class ClubLocationFilter(ShowMoreIfNoPeriodFilterMixin, ClubDrilldownFilter):
    title = "Club location"
    parameter_name = "location_id"
    lookup = "location_id"

    def lookups(self, request, model_admin):
        clubs = self._get_clubs(request)
        locations = models.Location.objects.filter(clubs__in=clubs).distinct()
        return (("NONE", "No location"),) + tuple(
            (loc.pk, loc.name) for loc in locations
        )

    def queryset(self, request, queryset):
        val = self.value()
        if val is None:
            return queryset
        if val == "NONE":
            val = None
        return queryset.filter(**{self._get_lookup(): val}).distinct()
