import logging
from contextlib import contextmanager

from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver

from clubs.models import Session, StudentRegistration

logger = logging.getLogger(__name__)


@receiver(post_save, sender=StudentRegistration)
def studentregistration_post_save(sender, **kwargs):
    registration = kwargs.get("instance")
    location = registration.club.location
    if location and location.student_segment:
        logger.info(
            f'Adding student {registration.student} to segment "{location.student_segment}"'
        )
        registration.student.segments.add(location.student_segment)
        registration.student_segments.add(location.student_segment)
