import json
import logging
from datetime import date, datetime

import sentry_sdk
from dateutil.parser import parse as parse_date
from dateutil.relativedelta import relativedelta
from django import http
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate, login
from django.contrib.auth.views import login_required
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q
from django.forms import modelformset_factory
from django.http import Http404, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from quickbooks.exceptions import QuickbooksException

from accounts.models import Account, AccountContact
from billing import BillingError, BillingValidationError, accounts, invoicing, qb
from billing.merchants import braintree
from clubs import models
from hive.utils import decode_model_hashid, json_defaults, mc_subscribe
from students.models import Student
from students.utils import find_duplicate_students
from volunteers.models import Volunteer

from . import forms, utils
from .cart import SessionCart

logger = logging.getLogger(__name__)


def get_account(request):
    if request.user and request.user.is_authenticated:
        try:
            return Account.objects.get(user=request.user)
        except Account.DoesNotExist:
            pass

        # If the user exists as a volunteer, just create a new account linked to that user
        try:
            Volunteer.objects.get(user=request.user)
        except Volunteer.DoesNotExist:
            pass
        else:
            return Account.objects.create(user=request.user, signup_type="GUARDIAN")

    return None


@transaction.atomic
def create_account(request):
    request_vars = request.method == "POST" and request.POST or request.GET
    next = request_vars.get("next") or reverse("clubs:index")
    signup_type = request_vars.get("signup_type")
    dob_year = request_vars.get("dob_year")
    dob_month = request_vars.get("dob_month")
    dob_day = request_vars.get("dob_day")
    dob = None
    invalid_dob = False
    age = None
    underage = False

    if dob_year and dob_month and dob_day:
        try:
            dob = parse_date(f"{dob_month}/{dob_day}/{dob_year}")
        except ValueError:
            invalid_dob = True
        else:
            age = relativedelta(timezone.now().date(), dob).years
            underage = age < 13
            request.session["birth_date"] = dob.strftime("%Y-%m-%d")

    initial = {
        "next": next,
        "signup_type": signup_type,
        "dob_month": dob_month,
        "dob_year": dob_year,
        "dob_day": dob_day,
    }
    form = forms.UserCreationForm(initial=initial)

    if request.method == "POST":
        next = request.POST.get("next") or reverse("clubs:index")
        form = forms.UserCreationForm(request.POST, initial=initial)
        if form.is_valid():
            user = form.save()
            Account.objects.create(
                user=user, signup_type=form.cleaned_data["signup_type"]
            )
            user = authenticate(
                email=form.cleaned_data["email"],
                password=form.cleaned_data["password1"],
            )
            login(request, user)
            return HttpResponseRedirect(next)

    month_choices = (
        (f"{m:02d}", datetime.strptime(str(m), "%m").strftime("%B"))
        for m in range(1, 13)
    )

    day_choices = ((f"{d:02d}", str(d)) for d in range(1, 32))

    year_choices = list(range(timezone.now().year - 30, timezone.now().year))
    year_choices.reverse()

    context = {
        "form": form,
        "next": next,
        "dob": dob,
        "invalid_dob": invalid_dob,
        "signup_type": signup_type,
        "underage": underage,
        "dob_month": dob_month,
        "dob_day": dob_day,
        "dob_year": dob_year,
        "month_choices": month_choices,
        "day_choices": day_choices,
        "year_choices": year_choices,
    }

    return render(request, "clubs/create_account.html", context)


def show_private(request):
    if request.session.get("private"):
        return True

    if request.GET.get("private"):
        request.session["private"] = 1
        return True


def index(request):
    # Only show open enrollment locations
    clubs = models.Club.objects.open_for_registration()
    if not show_private(request):
        clubs = clubs.filter(hide_on_registration=False)

    # Are we showing all clubs, all summer camps, or both?
    periods = set(club.period for club in clubs)
    if len(periods) == 1:
        if "SUMMER" in periods:
            # only summer
            title = _("Summer Coding Camps")
        else:
            # only school-year
            title = _("ideaSpark Coding Clubs")
    elif "SUMMER" not in periods:
        # only school-year
        title = _("ideaSpark Coding Clubs")
    else:
        # mix of both?
        title = _("ideasPark Coding Clubs and Summer Camps")

    categories = models.ClubCategory.objects.exclude(status="ARCHIVED").filter(
        Q(clubs__in=clubs) | Q(hide_when_empty=False)
    )

    if "preview" in request.GET:
        categories = categories.filter(status__in=("PUBLISHED", "DRAFT"))
    else:
        categories = categories.filter(status="PUBLISHED")

    context = {
        "preview": "preview" in request.GET,
        "categories": categories.distinct().order_by("order"),
        "title": title,
    }

    return render(request, "clubs/index.html", context)


def category_index(request, category):
    category = get_object_or_404(models.ClubCategory, slug=category)
    account = get_account(request)

    location = None
    if request.GET.get("location_id"):
        location = get_object_or_404(models.Location, pk=request.GET["location_id"])

    # Only show open-enrollment locations
    if request.GET.get("preview"):
        as_of = request.GET["preview"]
    else:
        as_of = date.today()

    clubs = models.Club.objects.open_for_registration(as_of)

    if not show_private(request):
        clubs = clubs.filter(category=category, hide_on_registration=False)

    locations = None
    if category.require_location_selection:
        locations = models.Location.objects.filter(clubs__in=clubs).distinct()

    if location:
        clubs = clubs.filter(location=location)

    clubs = clubs.order_by("name")

    has_clubs_with_fees = any(c.fee > 0 for c in clubs)

    context = {
        "account": account,
        "category": category,
        "locations": locations,
        "location": location,
        "clubs": clubs,
        "has_clubs_with_fees": has_clubs_with_fees,
    }
    return render(request, "clubs/category_index.html", context)


def view_cart(request):
    cart = SessionCart(request)
    if request.GET.get("remove"):
        remove_index = int(request.GET["remove"]) - 1
        cart.remove(remove_index)
        return HttpResponseRedirect(".")

    if request.GET.get("remove-discount"):
        cart.remove_discount()

    # get a list of hidden-enrollment clubs in cart
    hidden_clubs = models.Club.objects.filter(
        pk__in=set(item["club"].pk for item in cart), hide_on_registration=True
    )
    context = {"hidden_clubs": hidden_clubs, "cart": cart}

    return render(request, "clubs/cart.html", context)


def view_club(request, club_code, **kwargs):
    club = get_object_or_404(models.Club, club_code=club_code)
    return HttpResponseRedirect(club.add_to_cart_url)


@login_required(login_url=settings.LOGIN_URL)
def cart_add(request, club_code, **kwargs):
    club = get_object_or_404(models.Club, club_code=club_code)
    account = get_account(request)
    student_id = request.GET.get("student") or request.POST.get("student")

    # if this is a self-signup, and the associated student exists, redirect with ?student_id=N
    if (
        not student_id
        and account.signup_type == "SELF"
        and account.students.count() > 0
    ):
        # there should be only one student on self-signup accounts
        student_id = account.students.order_by("pk")[0].pk
        return HttpResponseRedirect(f"?student={student_id}")

    allow_overflow = request.GET.get("allow_overflow") or request.POST.get(
        "allow_overflow"
    )

    # if account exists, show screen to select existing student to enroll
    student = None
    initial = {}
    account_students = None
    error = None
    confirm_info = False

    if student_id and student_id != "new":
        student = Student.objects.get(pk=student_id)
        initial = {"grade_level": student.grade}
        confirm_info = True
    elif account and account.students.count() > 0:
        account_students = account.students.all()

    if not student and request.session.get("birth_date"):
        initial["birth_date"] = datetime.strptime(
            request.session["birth_date"], "%Y-%m-%d"
        ).date()

    duplicates = None
    if request.method == "POST":
        if request.POST.get("is_duplicate"):
            # Selected student is a duplicate of the student submitted in the form. Redirect with
            # ?student=<selected duplicate id>
            return HttpResponseRedirect(f'?student={request.POST["is_duplicate"]}')

        form = forms.StudentForm(request.POST, instance=student, club=club)
        if form.is_valid():
            cart = SessionCart(request)
            student = form.save(commit=False)

            if request.POST.get("not_duplicate"):
                not_duplicate_pk = request.POST["not_duplicate"]
                # Mark this student as "not a duplicate" of the selected student
                student.not_duplicate = [not_duplicate_pk]
                # Mark the selected student as "not a duplicate" of this student
                Student.objects.filter(pk=not_duplicate_pk).update(
                    not_duplicate=[student.pk]
                )

            # If student is new, check for duplicates
            duplicates = []
            if not student.pk:
                duplicates = list(find_duplicate_students(student, account))
            if len(duplicates) == 0:
                # Check if student is already registered for this club
                existing_sreg = None
                existing_sregs = models.StudentRegistration.objects.filter(
                    club=club, student=student
                )
                if existing_sregs.count() > 0:
                    existing_sreg = existing_sregs[0]

                # If the StudentRegistration exists, and is already associated w/ a registration, show an error:
                if existing_sreg and existing_sreg.registration:
                    error = _(
                        'Error: {student_name} is already registered for "{club_name}".'
                    ).format(student_name=student.name, club_name=club.name)

                else:
                    form_data = form.cleaned_data
                    if account.signup_type == "GUARDIAN":
                        cart.add(
                            club,
                            student,
                            form_data,
                            existing_studentregistration=existing_sreg,
                        )
                        return HttpResponseRedirect(reverse("clubs:view-cart"))
                    else:
                        # a self-signup account does not use a cart, so we only allow one "item"
                        cart.clear()
                        cart.add(
                            club,
                            student,
                            form_data,
                            existing_studentregistration=existing_sreg,
                        )
                        return HttpResponseRedirect(reverse("clubs:checkout"))

    else:
        form = forms.StudentForm(instance=student, initial=initial, club=club)

    context = {
        "club": club,
        "allow_overflow": allow_overflow,
        "account": account,
        "account_students": account_students,
        "duplicates": duplicates,
        "form": form,
        "student_id": student_id,
        "error": error,
        "confirm_info": confirm_info,
    }
    return render(request, "clubs/cart_add.html", context)


@login_required
def checkout(request):
    account = get_account(request)
    cart = SessionCart(request)

    if len(cart) == 0:
        # Don't allow checkout if cart is empty
        return HttpResponseRedirect(reverse("clubs:view-cart"))

    post_data = None
    if request.method == "POST":
        post_data = request.POST

    contacts = AccountContact.objects.none()
    contacts_initial = []
    if account and account.contacts.count() > 0:
        contacts = account.contacts.all()
    elif account and request.user.contact_id:
        # User updated contact info before registering
        # (ie, the base `Contact` instance exists before the child `AccountContact` instance)
        contacts_initial = [{"contact_ptr": request.user.contact}]
    else:
        contacts_initial = [{"email": request.user.email}]

    if account.signup_type == "SELF":
        min_num_contacts = 0
    else:
        min_num_contacts = 1

    ContactFormSet = modelformset_factory(
        AccountContact, form=forms.AccountContactForm, min_num=min_num_contacts, extra=0
    )

    # make online/one-time the default payment method
    initial = {"payment_method": "ONE_TIME"}

    registration_form = forms.RegistrationForm(cart, post_data, account=account)
    contacts_formset = ContactFormSet(
        post_data,
        queryset=contacts,
        initial=contacts_initial,
        form_kwargs={"user": request.user},
    )
    financial_assistance_form = forms.FinancialAssistanceForm(
        registration_form, post_data
    )

    error = None
    validation_errors = {}
    billing_error = None
    created_invoice = None

    if request.method == "POST":
        # double-check that any applied discount is still available
        if cart.discount and not cart.discount_is_available(cart.discount):
            messages.add_message(
                request,
                messages.ERROR,
                _(
                    f'We\'re sorry, discount "{cart.discount.name}" is no longer available.'
                ),
            )
            cart.remove_discount()
            return redirect(reverse("clubs:view-cart"))

        # use ref to determine if a transaction was created
        payment_transaction = None
        try:
            with transaction.atomic():
                registration = process_registration_forms(
                    request,
                    account=account,
                    contacts_formset=contacts_formset,
                    registration_form=registration_form,
                    financial_assistance_form=financial_assistance_form,
                )
                if registration.invoice_total > 0:
                    # TODO: When we have a dependable connection to QBO, uncomment this to enable
                    # automatic invoice creation.
                    #
                    # created_invoice = invoicing.create_registration_invoice(registration)
                    if registration.payment_method == "ONLINE":
                        payment_transaction = braintree.process_registration_payment(
                            request, registration
                        )
        except ValidationError as e:
            error = "VALIDATION"
            if hasattr(e, "errors"):
                validation_errors = e.errors
            else:
                validation_errors = e.message
            logger.exception(repr(validation_errors))
        except (BillingError, BillingValidationError) as e:
            error = "BILLING"
            billing_error = str(e)
            logger.exception(e)  # report billing erorr to logger
        else:
            cart.clear()

            # Send emails
            utils.send_registration_confirmation(registration)
            utils.send_registration_notification(registration)

            # redirect to success url
            success_url = reverse(
                "clubs:checkout-complete", kwargs={"regid": registration.pk}
            )

            # unset payment_transaction and created_invoice so they don't get
            # voided in cleanup
            payment_transaction = None
            created_invoice = None

            if registration_form.cleaned_data.get("opt_in"):
                contact = registration.account.default_contact
                mc_subscribe(
                    contact.email,
                    first_name=contact.first_name,
                    last_name=contact.last_name,
                    silent=True,
                )

            return HttpResponseRedirect(success_url)
        finally:
            # NOTE: This will be always be run, even after returning above
            if payment_transaction:
                # A transaction was created that needs to be voided.
                braintree.cancel_transaction(payment_transaction)
            if created_invoice:
                # An invoice was created but needs to be deleted
                invoicing.void_invoice(created_invoice.DocNumber)

    else:
        registration_form = forms.RegistrationForm(
            cart, account=account, initial=initial
        )
        contacts_formset = ContactFormSet(
            queryset=contacts,
            initial=contacts_initial,
            form_kwargs={"user": request.user},
        )

    show_laptop_program = False
    if account.signup_type != "SELF":
        # KLUDGE: this really should be "Students eligible for laptop program", since technically
        # one could register in a club w/ `laptop_program` enabled, and another club without.
        # Though this situation isn't be likely to happen.
        show_laptop_program = any(item["club"].laptop_program for item in cart)

    context = {
        "settings": settings,
        "error": error,
        "validation_errors": json.dumps(validation_errors, indent=2),
        "billing_error": billing_error,
        "cart": cart,
        "cart_clubs": set(item["club"] for item in cart),
        "account": get_account(request),
        "contacts_formset": contacts_formset,
        "registration_form": registration_form,
        "financial_assistance_form": financial_assistance_form,
        "show_laptop_program": show_laptop_program,
    }
    return render(request, "clubs/checkout.html", context)


def process_registration_forms(request, **kwargs):
    account = kwargs.get("account")
    cart = SessionCart(request)
    contacts_formset = kwargs.get("contacts_formset")
    registration_form = kwargs.get("registration_form")
    financial_assistance_form = kwargs.get("financial_assistance_form")

    errors = {}
    if not contacts_formset.is_valid():
        errors["contacts"] = contacts_formset.errors
    if not registration_form.is_valid():
        errors["registration"] = registration_form.errors

    # validate financial assistance ifneeded
    registration_data = registration_form.data

    # payment_method on the form has simplified choices of ONE_TIME, MONTHLY, and ASSISTANCE which
    # need to be broken down into the format we use for the model.
    payment_method = None
    payment_freq = None

    if cart.total > 0:
        if registration_data.get("payment_method") == "ASSISTANCE":
            payment_method = "ASSISTANCE"
        else:
            payment_method = "ONLINE"
            if registration_data.get("payment_method") == "MONTHLY":
                payment_freq = "MONTHLY"
            else:
                payment_freq = "ONE_TIME"

    apply_for_laptops = registration_data.get("apply_for_laptops")
    financial_assistance = payment_method == "ASSISTANCE" or apply_for_laptops
    if financial_assistance and not financial_assistance_form.is_valid():
        errors["assistance"] = financial_assistance_form.errors

    if errors:
        e = ValidationError("")
        e.errors = errors
        raise e

    reg_data = registration_form.cleaned_data

    form_data = {
        "registration": reg_data,
        "contacts": contacts_formset.cleaned_data,
        "students": [item["form_data"] for item in cart],
    }

    if financial_assistance:
        form_data["financial_assistance"] = financial_assistance_form.cleaned_data

    if not account:
        account = Account.objects.create(user=request.user)

    # add/update account contacts
    contacts = contacts_formset.save(commit=False)

    for contact in contacts:
        contact.account = account
        contact.save()
        if not account.default_contact:
            account.default_contact = contact
            account.save()

    # create registration
    registration = models.Registration(
        account=account,
        payment_method=payment_method,
        discount=cart.discount,
        form_data=form_data,
    )

    # serialize form_data
    form_data = json.loads(json.dumps(form_data, default=json_defaults))
    registration.form_data = form_data

    if not registration.payment_method:
        registration.payment_method = ""

    if payment_method == "ONLINE":
        registration.payment_frequency = payment_freq
        if payment_freq == "MONTHLY":
            registration.num_payments = registration_form.num_monthly_payments

    if payment_method == "ASSISTANCE":
        fa_data = financial_assistance_form.cleaned_data
        registration.status = "PENDING"
        registration.financial_assistance_requested = fa_data["amount_requested"]

    registration.save()

    # add students to registration
    for item in cart:
        student = item["student"]
        grade_level = item["form_data"].get("grade_level")
        if grade_level:
            student.grade = int(grade_level)
        student.account = account
        extra_data = {
            "previous_activities": item["form_data"].get("previous_activities"),
            "possible_conflicts": item["form_data"].get("possible_conflicts"),
            "other_info": item["form_data"].get("other_info"),
        }

        # Update referred_by on student record if provided
        if reg_data.get("referred_by"):
            extra_data["referred_by"] = reg_data["referred_by"]

        student.extra_data = extra_data
        student.save()
        if item["studentregistration_id"]:
            student_registration = models.StudentRegistration.objects.get(
                pk=item["studentregistration_id"]
            )
            student_registration.extra_data = extra_data
            student_registration.registration = registration
        else:
            student_registration = models.StudentRegistration(
                registration=registration,
                student=student,
                extra_data=extra_data,
                club=item["club"],
            )
        student_registration.save()
        student_registration.set_demographic_info()
        student_registration.save()

    return registration


def checkout_complete(request, regid, **kwargs):
    context = {"registration": get_object_or_404(models.Registration, pk=regid)}
    return render(request, "clubs/checkout_complete.html", context)


def club_list(request, period=None):
    # TODO group by location
    year = None
    period = None
    period_description = None
    current = False

    if period is not None:
        year, period = period.split("-")
        period_description = utils.get_period_description(year, period)
        clubs = models.Club.objects.for_period(year, period)
    else:
        current = True
        clubs = models.Club.objects.current()

    clubs = clubs.order_by("location__name")

    return render(
        request,
        "clubs/club_list.html",
        {
            "year": year,
            "period": period,
            "period_description": period_description,
            "current": current,
            "clubs": clubs,
        },
    )


def club_info(request, club_code, **kwargs):
    club = get_object_or_404(models.Club, club_code=club_code)

    # get team captains
    team_captains = [
        a.volunteer for a in club.volunteer_assignments.filter(role="TEAM_CAPTAIN")
    ]

    tpl = "clubs/club_info.html"
    if request.GET.get("popup"):
        tpl = "clubs/club_info_popup.html"
    return render(
        request,
        tpl,
        {
            "club": club,
            "team_captains": team_captains,
            "sessions": club.sessions.order_by("date"),
        },
    )


def club_schedule(request, **kwargs):
    return redirect("clubs:club-info", **kwargs)


@login_required(login_url=settings.LOGIN_URL)
def my_profile(request):
    contact = request.user.contact
    form_kwargs = {"user": request.user, "instance": contact}

    form = forms.ContactForm(**form_kwargs)

    if request.method == "POST":
        form = forms.ContactForm(request.POST, **form_kwargs)
        if form.is_valid():
            contact = form.save()
            request.user.contact = contact
            request.user.save()
            messages.add_message(
                request, messages.INFO, _("Your profile was succecssfully updated.")
            )
            return redirect(reverse("clubs:my-profile"))

    context = {"contact": contact, "form": form}
    return render(request, "clubs/my_profile.html", context)


@login_required(login_url=settings.LOGIN_URL)
def my_invoices(request):
    # FIXME: This view is not used because the quickbooks connection is currently unreliable.
    account = get_account(request)
    if not account:
        raise Http404("Account not found")

    account_error = False
    total_outstanding = None
    total_overdue = None
    invoices = None

    is_connected = False
    try:
        qb.get_company_info()
    except qb.AuthError:
        pass
    else:
        is_connected = True

    if is_connected:
        try:
            qb_customer = accounts.get_customer(account)
        except QuickbooksException as err:
            # Capture the error, but show a friendly error message
            sentry_sdk.capture_exception(err)
            qb_customer = None

        account_error = qb_customer is None
        invoices = []
        total_outstanding = 0
        total_overdue = 0

        if qb_customer:
            qb_invoices = invoicing.get_invoices(qb_customer.Id)
            qb_invoices = [i for i in qb_invoices if "Voided" not in i.PrivateNote]

            for invoice in qb_invoices:
                # calculate total oustanding and overdue amounts
                total_outstanding += invoice.Balance
                if parse_date(invoice.DueDate) < timezone.now():
                    total_overdue += invoice.Balance
                invoices.append(
                    {
                        "Id": invoice.Id,
                        "DocNumber": invoice.DocNumber,
                        "TxnDate": invoice.TxnDate,
                        "TotalAmt": invoice.TotalAmt,
                        "AmountPaid": invoice.TotalAmt - invoice.Balance,
                        "Balance": invoice.Balance,
                    }
                )

    return render(
        request,
        "clubs/invoices.html",
        {
            "account_error": account_error or not is_connected,
            "account": account,
            "total_outstanding": total_outstanding,
            "total_overdue": total_overdue,
            "invoices": invoices,
        },
    )


def view_invoice(request, invoice_id):
    account = get_account(request)
    qb_invoice = invoicing.get_invoice_by_id(invoice_id)

    error = None
    billing_error = None
    validation_errors = None

    if request.method == "POST":
        form = forms.InvoicePaymentForm(request.POST)
        if form.is_valid():
            try:
                invoicing.submit_payment(
                    request, account, qb_invoice, form.cleaned_data["payment_amount"]
                )
            except (BillingError, BillingValidationError) as e:
                error = "BILLING"
                billing_error = str(e)
            else:
                messages.add_message(
                    request,
                    messages.INFO,
                    "Thank you! Your payment is being processed. Please allow 2-3 business days "
                    "for your payment to be posted to your account.",
                )

                if account:
                    return HttpResponseRedirect(reverse("clubs:my-account"))
                else:
                    return HttpResponseRedirect(reverse("clubs:index"))

        else:
            error = "VALIDATION"
            validation_errors = form.errors

    else:
        initial = {"payment_amount": qb_invoice.Balance}
        form = forms.InvoicePaymentForm(initial=initial)

    invoice = {
        "Id": qb_invoice.Id,
        "DocNumber": qb_invoice.DocNumber,
        "TxnDate": qb_invoice.TxnDate,
        "TotalAmt": qb_invoice.TotalAmt,
        "AmountPaid": qb_invoice.TotalAmt - qb_invoice.Balance,
        "Balance": qb_invoice.Balance,
    }
    return render(
        request,
        "clubs/view_invoice.html",
        {
            "account": account,
            "invoice": invoice,
            "form": form,
            "error": error,
            "billing_error": billing_error,
            "validation_errors": validation_errors,
            "settings": settings,
        },
    )


def view_invoice_pdf(request, invoice_id):
    invoice = invoicing.get_invoice_pdf(invoice_id)
    response = http.HttpResponse(invoice, content_type="application/pdf")
    if request.GET.get("download"):
        response["Content-Disposition"] = 'attachment; filename="invoice.pdf"'
    return response


def webcal(request, cal_type, obj_id):
    if cal_type not in ("club", "assignment"):
        raise Http404("invalid type")

    if cal_type == "club":
        club = get_object_or_404(models.Club, pk=obj_id, canceled=False)
        sessions = club.sessions.all()
        name = f"Bold Idea - {club.name}"
    elif cal_type == "assignment":
        assignment_id = decode_model_hashid(models.VolunteerAssignment, obj_id)
        assignment = get_object_or_404(models.VolunteerAssignment, pk=assignment_id)
        sessions = assignment.sessions.all()
        name = f"Bold Idea - {assignment.club.name}"

    calendar = utils.generate_session_calendar(sessions, name=name)

    response = http.HttpResponse(calendar, content_type="text/calendar")
    response["Content-Disposition"] = 'attachment; filename="Bold Idea Schedule.ics"'
    return response


# vim: set tw=120
