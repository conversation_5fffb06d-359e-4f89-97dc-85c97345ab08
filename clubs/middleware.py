from django.shortcuts import redirect

from clubs.cart import SessionCart


class DiscountCodeMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        code = request.GET.get("code")
        if not code:
            return response

        cart = SessionCart(request)
        cart.apply_discount_code(code)

        return redirect(request.path_info)
