import re

from django.conf import settings
from django.template import Library
from django.urls import reverse

from clubs.cart import SessionCart
from hive.utils import model_hashid
from volunteers.constants import MENTOR_ROLES

register = Library()


@register.inclusion_tag("clubs/includes/add_to_calendar.html", takes_context=True)
def add_to_calendar_links(context, type, assignment):
    request = context.get("request")

    if settings.SITE == "clubs":
        app_name = "clubs"
    elif settings.SITE == "hive":
        app_name = "volunteers"
    else:
        raise NotImplementedError()

    if type == "assignment":
        id = model_hashid(assignment)

    http_url = request.build_absolute_uri(
        reverse(f"{app_name}:webcal", kwargs={"cal_type": type, "obj_id": id})
    )
    webcal_url = re.sub(r"^https?://", "webcal://", http_url)
    google_url = f"https://calendar.google.com/calendar/render?cid={webcal_url}"

    return {
        "no_js": context.get("no_js", False),
        "http_url": http_url,
        "webcal_url": webcal_url,
        "google_url": google_url,
    }


@register.filter
def is_mentor_role(role):
    return role in MENTOR_ROLES


@register.filter
def has_mentor_role(roles):
    return any(role in MENTOR_ROLES for role in roles)


@register.inclusion_tag("clubs/includes/club_fee_description.html", takes_context=True)
def club_fee_description(context, club):
    # Check for first applied discount code
    request = context["request"]
    cart = SessionCart(request)
    fee = club.fee
    discount_name = None
    if cart.discount:
        discount_name = cart.discount.name
        discount_amount = cart.discount.calculate(fee)
        fee = round(fee - discount_amount, 2)
    return {
        "discount_name": discount_name,
        "discounted": cart.discount and fee < club.fee,
        "base_fee": club.fee,
        "fee": fee,
        "allow_monthly_payments": club.allow_monthly_payments,
    }
