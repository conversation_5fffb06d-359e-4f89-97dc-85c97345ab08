from django.conf.urls import url
from django.contrib.auth.views import PasswordChangeView

from clubs import views

app_name = "clubs"

urlpatterns = [
    url("^$", views.index, name="index"),
    url("^cart/$", views.view_cart, name="view-cart"),
    url("^checkout/$", views.checkout, name="checkout"),
    url("^create-account/$", views.create_account, name="create-account"),
    url(
        "^registration-complete/(?P<regid>[^/]+)/$",
        views.checkout_complete,
        name="checkout-complete",
    ),
    url("^my-profile/$", views.my_profile, name="my-profile"),
    url("^my-profile/password/$", PasswordChangeView.as_view(), name="change-password"),
    url("^current-clubs/$", views.club_list, name="current-clubs"),
    url("^past-clubs/(?P<period>[^/])/$", views.club_list, name="past-clubs"),
    url("^invoice/(?P<invoice_id>[^/]+)/$", views.view_invoice, name="view-invoice"),
    url(
        "^invoice/(?P<invoice_id>[^/]+)/pdf/$",
        views.view_invoice_pdf,
        name="view-invoice-pdf",
    ),
    url("^(?P<category>[^/]+)/$", views.category_index, name="category-index"),
    url("^(?P<category>[^/]+)/(?P<club_code>[^/]+)/$", views.view_club, name="club"),
    url(
        "^(?P<category>[^/]+)/(?P<club_code>[^/]+)/add/$",
        views.cart_add,
        name="cart-add",
    ),
    url(
        "^(?P<category>[^/]+)/(?P<club_code>[^/]+)/info/$",
        views.club_info,
        name="club-info",
    ),
    url(
        "^(?P<category>[^/]+)/(?P<club_code>[^/]+)/schedule/$",
        views.club_schedule,
        name="club-schedule",
    ),
    url(
        "^webcal/(?P<cal_type>[^/]+)/(?P<obj_id>[^/]+).ics$",
        views.webcal,
        name="webcal",
    ),
]
