import json

from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin.widgets import AutocompleteSelect
from django.core.exceptions import ValidationError
from django.db.models import Count, F
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from import_export import fields as ie_fields
from import_export import resources
from import_export.admin import ExportActionModelAdmin

from billing import invoicing
from clubs import constants, models, utils
from clubs.admin_filters import (
    ClubCategoryFilter,
    ClubFilter,
    ClubLocationFilter,
    PeriodFilter,
)
from courses.admin import ResourceLinkInline as BaseResourceLinkInline
from hive.admin import ListingStatusFilter, register
from hive.utils import GeocodeError, dict_find, format_date, geocode


@register(models.ClubCategory)
class ClubCategoryAdmin(admin.ModelAdmin):
    icon = '<i class="material-icons">folder_open</i>'
    list_display = ["name"]
    list_filter = [ListingStatusFilter]
    fields = [
        "name",
        "slug",
        "order",
        "photo",
        "short_description",
        "description",
        "require_location_selection",
        "hide_when_empty",
        "status",
    ]
    prepopulated_fields = {
        "slug": ("name",),
    }


class SessionInline(admin.TabularInline):
    model = models.Session
    fields = ["type", "date", "start_time", "end_time", "description"]
    classes = ["collapse"]
    extra = 0


class ResourceLinkInline(BaseResourceLinkInline):
    model = models.ResourceLink
    classes = ["collapse"]

    def save_formset(self, request, form, formset, change):
        instances = formset.save(commit=False)
        for instance in instances:
            instance.course = instance.club.course
        return super().save_formset(self, request, form, formset, change)


@register(models.Club)
class ClubAdmin(admin.ModelAdmin):
    icon = '<i class="material-icons">import_contacts</i>'
    list_display = [
        "name",
        "club_code",
        "location",
        "_ages",
        "schedule_description",
        "_start_date",
        "_num_sessions",
        "_total_hrs",
        "canceled",
    ]
    list_filter = [
        PeriodFilter,
        "hide_on_registration",
        "canceled",
        ClubCategoryFilter.factory(club_lookup=None),
        ClubLocationFilter.factory(club_lookup=None),
    ]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("name", "photo"),
                    ("club_code", "category"),
                    ("period", "schedule_description"),
                    ("location",),
                    ("courses",),
                    (
                        "online",
                        "hide_on_registration",
                        "hide_on_volunteer_signup",
                        "laptop_program",
                        "canceled",
                    ),
                ),
            },
        ),
        (
            "General Info",
            {
                "classes": ("collapse", "open"),
                "fields": ("base_description", "location_info"),
            },
        ),
        (
            "Students",
            {
                "classes": ("collapse", "open"),
                "fields": (
                    ("min_age", "max_age"),
                    ("min_grade", "max_grade"),
                    ("num_student_seats", "student_volunteer_ratio"),
                ),
            },
        ),
        (
            "Registration",
            {
                "classes": ("collapse", "open"),
                "fields": (
                    ("fee", "allow_monthly_payments", "num_monthly_payments"),
                    ("registration_open_date", "registration_close_date"),
                ),
            },
        ),
        (
            "Volunteers",
            {
                "classes": ("collapse",),
                "fields": (
                    (
                        "num_volunteers_needed",
                        "volunteer_fall_signup_deadline",
                        "volunteer_requirements",
                        "min_volunteer_age",
                        "partner_organizations",
                    )
                ),
            },
        ),
    )
    inlines = [SessionInline, ResourceLinkInline]
    search_fields = ["name__unaccent", "club_code"]
    autocomplete_fields = [
        "location",
        "courses",
        "partner_organizations",
        "volunteer_requirements",
    ]

    def _get_extra_context(self, request, extra_context=None):
        # provide data for default location instructions when location is selected
        extra_context = extra_context or {}
        extra_context["location_json"] = json.dumps(
            {loc.pk: loc.instructions for loc in models.Location.objects.all()},
            indent=2,
        )

        return extra_context

    def view_on_site(self, obj):
        return reverse("dashboard:club", kwargs={"club_id": obj.pk})

    def add_view(self, request, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context.update(self._get_extra_context(request, extra_context))
        return super().add_view(request, form_url, extra_context)

    def change_view(self, request, object_id, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context.update(self._get_extra_context(request, extra_context))
        return super().change_view(request, object_id, form_url, extra_context)

    def get_form(self, request, obj, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.base_fields["location"].required = True
        return form

    def save_formset(self, request, form, formset, change):
        super().save_formset(request, form, formset, change)
        if issubclass(formset.model, models.Session):
            club = formset.instance
            club.renumber_sessions()

    def get_view_on_site_url(self, obj):
        return super().get_view_on_site_url(obj)

    def _ages(self, obj):
        return obj.age_range_description

    _ages.short_description = "Ages"

    def _start_date(self, obj):
        return obj.start_date

    _start_date.short_description = "Start date"
    _start_date.admin_order_field = "start_date"

    def _num_sessions(self, obj):
        return obj.sessions.filter(type="CLUB").count()

    _num_sessions.short_description = "# Sessions"

    def _total_hrs(self, obj):
        return obj.total_hours

    _total_hrs.short_description = "Total hrs"


class RegistrationResource(resources.ModelResource):
    account = ie_fields.Field()
    club = ie_fields.Field()
    parent_first_name = ie_fields.Field()
    parent_last_name = ie_fields.Field()
    parent_email = ie_fields.Field()
    parent_phone = ie_fields.Field()
    address = ie_fields.Field()
    students = ie_fields.Field()
    payment_method = ie_fields.Field()
    referred_by = ie_fields.Field()

    class Meta:
        model = models.Registration
        fields = [
            "id",
            "date",
            "account",
            "club",
            "parent_first_name",
            "parent_last_name",
            "parent_email",
            "students",
            "address",
            "payment_method",
            "financial_assistance_requested",
            "financial_assistance_granted",
            "invoice_number",
            "referred_by",
        ]
        export_order = fields

    def dehydrate_account(self, obj):
        return "#" + obj.account.account_number

    def dehydrate_club(self, obj):
        return obj.club_name

    def dehydrate_parent_first_name(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.first_name
        return ""

    def dehydrate_parent_last_name(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.last_name
        return ""

    def dehydrate_parent_email(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.email
        return ""

    def dehydrate_parent_phone(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.phone
        return ""

    def dehydrate_address(self, obj):
        if obj.account and obj.account.default_contact:
            return obj.account.default_contact.formatted_address
        return ""

    def dehydrate_students(self, obj):
        students = obj.student_registrations.filter(club__id=obj.club_id)
        names = [s.student.first_name for s in students]
        if len(names) == 0:
            return ""
        elif len(names) == 1:
            return names[0]
        elif len(names) == 2:
            return " and ".join(names)
        else:
            names[-1] = "and " + names[-1]
            return ", ".join(names)

    def dehydrate_payment_method(self, obj):
        return obj.get_payment_method_display()

    def dehydrate_referred_by(self, obj):
        return dict_find(obj.form_data, "registration.referred_by")


class LaptopRequestFilter(admin.SimpleListFilter):
    title = "laptop requests"
    parameter_name = "laptops"

    def lookups(self, request, model_admin):
        return (("1", "Laptop requested"),)

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(form_data__registration__apply_for_laptops=True)
        return queryset


class RegistrationAdminForm(forms.ModelForm):
    def clean_financial_assistance_granted(self):
        financial_assistance_granted = self.cleaned_data["financial_assistance_granted"]

        if self.instance:
            changed_financial_assistance = (
                self.instance.financial_assistance_granted is None
                and financial_assistance_granted is not None
            )

            if changed_financial_assistance:
                from billing import qb

                if not qb.is_connected():
                    raise ValidationError(
                        "Cannot update invoice: Quickbooks is not currently connected."
                    )

                if not self.instance.invoice_number:
                    raise ValidationError(
                        "An invoice has not yet been created for this registration. Run the "
                        "`process_registrations` command to create it, or wait for it to run "
                        "automatically on the next sync."
                    )

        return financial_assistance_granted


@register(models.Registration)
class RegistrationAdmin(ExportActionModelAdmin):
    icon = '<i class="material-icons">assignment_turned_in</i>'
    """
    This ModelAdmin is mostly just used to view the registration details and
    grant a financial aid amount.
    """
    form = RegistrationAdminForm

    list_display = [
        "_regnum",
        "_date",
        "_account",
        "_club",
        "_parent_name",
        "_num_students",
        "_payment_method",
    ]

    search_fields = [
        "student_registrations__student__first_name__unaccent",
        "student_registrations__student__last_name__unaccent",
        "account__default_contact__first_name__unaccent",
        "account__default_contact__last_name__unaccent",
    ]

    list_filter = [
        PeriodFilter,
        "payment_method",
        "account__signup_type",
        LaptopRequestFilter,
        ClubCategoryFilter.factory("student_registrations__club"),
        ClubLocationFilter.factory("student_registrations__club"),
        ClubFilter.factory("student_registrations__club"),
    ]

    fields = ["financial_assistance_granted"]

    resource_class = RegistrationResource

    def has_add_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.annotate(
            club_id=F("student_registrations__club__id"),
            club_name=F("student_registrations__club__name"),
            num_students=Count("student_registrations", distinct=True),
        )

    def get_object(self, request, object_id, from_field=None):
        model = self.model
        queryset = model.objects.all()
        if from_field is None:
            field = model._meta.pk
        else:
            field = model._meta.get_field(from_field)
        try:
            object_id = field.to_python(object_id)
            return queryset.get(**{field.name: object_id})
        except (model.DoesNotExist, ValidationError, ValueError):
            return None

    def get_readonly_fields(self, request, obj=None):
        if obj.financial_assistance_granted is None:
            return []
        return ["financial_assistance_granted"]

    def _regnum(self, obj):
        return "#" + obj.registration_number

    _regnum.short_description = "Reg. #"
    _regnum.admin_order_field = "registration__id"

    def _date(self, obj):
        return format_date(obj.date, settings.DATE_FORMAT)

    _date.short_description = "Date"
    _date.admin_order_field = "date"

    def _account(self, obj):
        return "#" + obj.account.account_number

    _account.short_description = "Account #"
    _account.admin_order_field = "account__id"

    def _club(self, obj):
        return obj.club_name

    _club.short_description = "Club"
    _club.admin_order_field = "student_registrations__club__name"

    def _parent_name(self, obj):
        if obj.account.signup_type == "SELF":
            return "(self-signup)"
        if not obj.account.default_contact:
            return "ERROR"
        return "{0.first_name} {0.last_name}".format(obj.account.default_contact)

    _parent_name.short_description = "Parent name"
    _parent_name.admin_order_field = "account__default_contact__first_name"

    def _num_students(self, obj):
        return obj.num_students

    _num_students.short_description = "# Students"
    _num_students.admin_order_field = "num_students"

    def _parent_email(self, obj):
        return format_html(
            '<a href="mailto:{}" target="_blank">{}</a>',
            obj.account.default_contact.email,
        )

    _parent_email.short_description = "Parent email"
    _parent_name.admin_order_field = "account__default_contact__email"

    def _payment_method(self, obj):
        return obj.get_payment_method_display()

    _payment_method.short_description = "Payment method"
    _payment_method.admin_order_field = "payment_method"

    def change_view(self, request, object_id, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context["registration_status_choices"] = (
            constants.REGISTRATION_STATUS_CHOICES
        )

        # Check for field changes on StudentRegistrations
        # FIXME: this is a quick hack, a better solution would be to use formsets
        if request.method == "POST":
            reg = models.Registration.objects.get(pk=object_id)
            for sreg in reg.student_registrations.all():
                status = request.POST.get(
                    "studentregistration__{}__status".format(sreg.pk)
                )
                if status:
                    sreg.status = status
                    sreg.save()

        return super().change_view(request, object_id, form_url, extra_context)

    def save_model(self, request, obj, form, change):
        existing = models.Registration.objects.get(pk=obj.pk)
        changed_financial_assistance = (
            existing.financial_assistance_granted is None
            and obj.financial_assistance_granted is not None
        )

        if changed_financial_assistance:
            # quickbooks should be connected at this point, this is checked by form validation
            # The instance should have an invoice number at this point, checked by form validation
            invoicing.add_discount(
                obj.invoice_number,
                obj.financial_assistance_granted,
                "Financial assistance discount",
            )

            obj.student_registrations.filter(status="PENDING").update(
                status="COMPLETED"
            )

            messages.add_message(
                request,
                messages.INFO,
                "Added discount for invoice #{}".format(obj.invoice_number),
            )

        return super().save_model(request, obj, form, change)


@register(models.Location)
class LocationAdmin(admin.ModelAdmin):
    icon = '<i class="material-icons">pin_drop</i>'
    list_display = ["__str__", "type"]
    list_filter = ["type", "student_segment"]
    search_fields = [
        "name",
        "nickname",
        "address1",
        "address2",
        "city",
        "state",
        "postal_code",
    ]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "nickname",
                    "type",
                    "student_segment",
                    "about",
                    "logo",
                    "photo",
                    "website",
                    "phone",
                    "extension",
                    "notes",
                )
            },
        ),
        (
            "Address",
            {
                "fields": (
                    ("address1", "address2", "city", "state", "postal_code"),
                    "instructions",
                )
            },
        ),
    )

    def save_model(self, request, obj, form, change):
        if obj.address1:
            try:
                geo = geocode(
                    street=f"{obj.address1} {obj.address2}",
                    city=obj.city,
                    state=obj.state,
                    country="United States",
                    postalcode=obj.postal_code,
                    raise_error=True,
                )
            except GeocodeError:
                self.message_user(
                    request,
                    "Warning: Unable to verify the address for this location. The updated address "
                    "may not show on embedded maps.",
                    level=messages.WARNING,
                )
                obj.geo = None  # invalid address
            else:
                obj.geo = geo
        super().save_model(request, obj, form, change)


class StudentRegistrationsAdminForm(forms.ModelForm):
    def clean(self):
        cleaned_data = self.cleaned_data
        status = cleaned_data.get("status")
        if status == "COMPLETE" and not self.instance.student.account:
            # Status may not be set to COMPLETE if student has no account.
            raise ValidationError(
                'Cannot set status to "Complete": Student must first be associated with an account. '
                "Click the student name below to edit the student and assign the account. Then, come back to this page "
                "to update the status."
            )
        return cleaned_data

    class Meta:
        widgets = {
            "club": AutocompleteSelect(
                models.StudentRegistration._meta.get_field("club").remote_field,
                admin.site,
                attrs={"style": "width: 600px"},
            ),
        }


@register(models.StudentRegistration)
class StudentRegistrationAdmin(admin.ModelAdmin):
    form = StudentRegistrationsAdminForm
    search_fields = [
        "student__first_name__unaccent",
        "student__last_name__unaccent",
        "student__account__default_contact__first_name__unaccent",
        "student__account__default_contact__last_name__unaccent",
    ]
    list_filter = [
        PeriodFilter,
        "status",
        ClubCategoryFilter.factory("club"),
        ClubLocationFilter.factory("club"),
        ClubFilter.factory("club"),
    ]
    list_display = [
        "_student",
        "_acct_linked",
        "_regnum_linked",
        "student_grade",
        "date",
        "club",
        "_location",
    ]
    fields = [
        "_acct_linked",
        "_regnum_linked",
        "_student_linked",
        "date",
        "_student_age",
        "_student_grade",
        "club",
        "status",
        "current_course",
    ]
    readonly_fields = [
        "date",
        "_student_grade",
        "_student_age",
        "_student_linked",
        "_acct_linked",
        "_regnum_linked",
    ]

    def get_queryset(self, *args, **kwargs):
        return (
            super().get_queryset(*args, **kwargs).select_related("club", "registration")
        )

    def _regnum_linked(self, obj):
        reg = obj.registration
        if not reg:
            return "(manually registered)"
        url = reverse("admin:clubs_registration_change", args=[reg.id])
        return format_html('<a href="{}">{}</a>', url, reg.registration_number)

    _regnum_linked.short_description = "Registration"

    def _acct_linked(self, obj):
        acct = getattr(obj.student, "account", None)
        if acct is None:
            return "(no account)"
        url = reverse("admin:accounts_account_change", args=[acct.id])
        return format_html('<a href="{}">{}</a>', url, acct.account_number)

    _acct_linked.short_description = "Account"

    def _student_linked(self, obj):
        url = reverse("admin:students_student_change", args=[obj.student.id])
        return format_html('<a href="{}">{}</a>', url, obj.student.name)

    _student_linked.short_description = "Student"

    def _student(self, obj):
        return obj.student.name

    _student.short_description = "Student"
    _student.admin_order_field = "student__first_name"

    def _student_age(self, obj):
        school_year = utils.get_current_school_year(as_of=obj.date)
        return format_html(
            "{} <small><em>(as of {}-{} school year)</em></small>",
            obj.get_student_age(),
            school_year,
            school_year + 1,
        )

    _student_age.short_description = "Student age"

    def _student_grade(self, obj):
        if obj.student_grade is None:
            return "unknown"
        school_year = utils.get_current_school_year(as_of=obj.date)
        return format_html(
            "{} <small><em>(as of {}-{} school year)</em></small>",
            obj.student_grade_name,
            school_year,
            school_year + 1,
        )

    _student_grade.short_description = "Grade level"

    def _regnum(self, obj):
        reg = obj.registration
        if not reg:
            return "(manually registered)"
        return reg.registration_number

    _regnum.short_description = "Reg. #"
    _regnum.admin_order_field = "registration__id"

    def _category(self, obj):
        return obj.club.category

    _category.short_description = "Category"
    _category.admin_order_field = "club__category__name"

    def _location(self, obj):
        return str(obj.club.location)

    _location.short_description = "Location"
    _location.admin_order_field = "club__location__name"


@register(models.DiscountCode)
class DiscountCodeAdmin(admin.ModelAdmin):
    fields = ["name", "code", "type", "amount", "limit", "_num_used"]
    list_display = ["name", "code", "type", "amount", "limit", "_num_used"]
    readonly_fields = ["_num_used"]

    def get_queryset(self, request):
        return super().get_queryset(request).with_num_used()

    def _num_used(self, obj):
        return obj.num_used

    _num_used.short_description = "# used"


class VisibilityFilter(admin.SimpleListFilter):
    title = "visibility"
    parameter_name = "visibility"

    def lookups(self, request, model_admin):
        return (("public", "Public registration"), ("private", "Private registration"))

    def queryset(self, request, queryset):
        val = self.value()
        if val == "public":
            return queryset.filter(hide_on_registration=False)
        if val == "private":
            return queryset.filter(hide_on_registration=True)
        return queryset
