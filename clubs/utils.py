from datetime import datetime

import ics
from django.conf import settings
from django.core.mail import send_mail
from django.template import loader
from django.utils import timezone
from django.utils.translation import get_language
from ics.grammar.parse import ContentLine

from clubs.constants import PERIOD_CHOICES
from hive import utils
from users.models import User


def get_latest_period(as_of=None):
    from clubs.models import Club

    clubs = Club.objects.exclude(start_date=None).order_by("-start_date")
    if not clubs.count():
        return (utils.get_current_fiscal_year(as_of), "SCHOOL_YEAR")
    if as_of:
        clubs = clubs.filter(start_date__lte=as_of)
    return (clubs[0].year, clubs[0].period)


def parse_period(period_string):
    year, period = period_string.split("-")
    error_message = (
        "string must be in the format of 'YYYY-PERIOD', where PERIOD is one of "
        "'FALL', 'SPRING', 'SUMMER', or 'SCHOOL_YEAR'"
    )
    try:
        year = int(year)
    except ValueError:
        raise ValueError(error_message)

    if period not in ("FALL", "SPRING", "SUMMER", "SCHOOL_YEAR"):
        raise ValueError(error_message)

    return year, period


def get_grade_choices():
    GRADE_CHOICES = []
    if get_language() == "es":
        GRADE_CHOICES = [(n, (str(n) + ".º")) for n in range(1, 13)]
    else:
        GRADE_CHOICES = [(n, utils.ordinal(n)) for n in range(1, 13)]

    return GRADE_CHOICES


def grammatical_join(items):
    items = list(items)
    return ", ".join(items[:-2] + [" and ".join(items[-2:])])


def get_current_school_year(as_of=None):
    """
    Returns the current school year assuming the year ends June 31.

    School year is referenced by its starting year.
    """
    today = as_of or timezone.now()
    if today.month < 7:
        return today.year - 1
    return today.year


def get_current_school_year_description(as_of=None):
    year = get_current_school_year(as_of)
    return "{} - {}".format(year, year + 1)


def send_registration_confirmation(registration):
    subject = "Your registration has been received"
    student_names = grammatical_join(
        s.first_name for s in registration.students.distinct()
    )
    club_names = grammatical_join(
        p.category.name for p in registration.clubs.distinct()
    )
    current_school_year = get_current_school_year_description()

    registration_msg = loader.get_template(
        "clubs/emails/registration_email.txt"
    ).render(
        {
            "contact": registration.account.default_contact,
            "current_school_year": current_school_year,
            "student_names": student_names,
            "club_names": club_names,
            "registration": registration,
        }
    )
    send_mail(
        subject,
        registration_msg,
        settings.DEFAULT_FROM_EMAIL,
        [registration.account.user.email],
    )


def send_registration_notification(registration):
    notification_subj = "A registration has been submitted"
    notification_msg = loader.get_template(
        "clubs/emails/registration_notification.txt"
    ).render({"registration": registration})

    if recipient_list := settings.REGISTRATION_NOTIFICATION_RECIPIENTS:
        send_mail(
            notification_subj,
            notification_msg,
            settings.DEFAULT_FROM_EMAIL,
            recipient_list,
        )


def get_periods_lookup(start_year=None):
    """
    Returns a two-tuple of year-period combinations, eg: `("2019-SUMMER", "Summer 2019")`
    """
    from clubs.models import Club, Session

    if start_year is None:
        first_session = Session.objects.order_by("date")[0]
        start_year = first_session.date.year

    lookup = ()

    qs = (
        Club.objects.exclude(start_date=None)
        .with_period_info()
        .order_by("-year", "-period_number")
        .values("year", "period")
        .distinct()
    )

    for obj in qs:
        year = obj["year"]
        period = obj["period"]
        period_label = dict(PERIOD_CHOICES).get(period, period)
        year_label = year
        if period == "SCHOOL_YEAR":
            year_label = f"{year}-{year+1}"
        lookup += (
            (
                f"{year}-{period}",
                f"{period_label} {year_label}",
            ),
        )

    return lookup


def get_period_description(start_year, period):
    period_display = dict(PERIOD_CHOICES).get(period, period)

    if period == "SCHOOL_YEAR":
        return f"{start_year}-{start_year + 1} {period_display}"
    else:
        return f"{period_display} {start_year}"


def setup_chat(club):
    """
    Creates the initial configuration for chat
    """
    from chat.models import Channel, MemberGroup

    # create the mentors member group
    mentor_group, group_created = MemberGroup.objects.get_or_create(
        slug="mentors", name="Mentors", club=club
    )

    # create the default channel
    channel, created = Channel.objects.get_or_create(
        name=settings.CHAT_DEFAULT_CHANNEL, club=club
    )

    # create the mentors channel
    mentor_channel, created = Channel.objects.get_or_create(
        name="mentors", club=club, private=True
    )

    if mentor_channel.group_members.filter(slug="mentors").count() == 0:
        mentor_channel.group_members.add(mentor_group)


def create_student_chat_channel(student, club):
    from chat.models import Channel, MemberGroup

    channel_name = student.user.student_channel_name
    channel, created = Channel.objects.get_or_create(
        name=channel_name,
        club=club,
        defaults={
            "type": "STUDENT",
            "private": True,
            "metadata": {"student_id": student.id},
        },
    )

    # Add student to channel
    if channel.members.filter(pk=student.user.pk).count() == 0:
        channel.members.add(student.user)

    # Add mentors member group
    if not channel.group_members.filter(slug="mentors"):
        mentor_group = MemberGroup.objects.get(club=club, slug="mentors")
        channel.group_members.add(mentor_group)


def generate_session_calendar(sessions, name=None):
    """
    Returns an `ics.Calendar` object generated from the given sessions. This object can be
    converted to a string to get the raw ics format.
    """
    calendar = ics.Calendar()
    if name:
        calendar.extra.append(ContentLine(name="X-WR-CALNAME", value=name))
    for session in sessions.order_by("date", "start_time"):
        if not session.start_time:
            continue
        start_dt = timezone.make_aware(
            datetime.combine(session.date, session.start_time)
        )
        end_dt = timezone.make_aware(datetime.combine(session.date, session.end_time))
        event = ics.Event()
        event.name = f"Bold Idea {session.description}"
        event.begin = start_dt
        event.end = end_dt
        calendar.events.add(event)

    # KLUDGE: see here: https://github.com/ics-py/ics-py/issues/251
    calendar.events = sorted(calendar.events)
    return calendar
