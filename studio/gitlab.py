import logging

import gitlab
from django.conf import settings

from .utils import get_pubkey_fingerprint

logger = logging.getLogger(__name__)


PARENT_GROUP_PATH = "boldidea/studio"


def get_client():
    return gitlab.Gitlab(
        settings.GITLAB_SERVER, private_token=settings.GITLAB_ACCESS_TOKEN
    )


def get_project(project):
    if not project.gitlab_id:
        raise ValueError("Project has no gitlab_id")
    gl = get_client()
    return gl.projects.get(project.gitlab_id)


def create_project(project):
    gl = get_client()
    namespace = gl.namespaces.get(PARENT_GROUP_PATH)
    params = {
        "path": project.slug,
        "name": project.slug,
        "visibility": "private",
        "namespace_id": namespace.id,
        "pages_access_level": "public",
    }
    gl_project = gl.projects.create(params)
    try:
        update_project_deploy_key(gl_project, project.ssh_pubkey)
    except Exception:
        # if deploy key update failed, delete the project
        gl_project.delete()
        raise
    return gl_project


def get_or_create_project(project):
    if not project.gitlab_id:
        return create_project(project)
    try:
        return get_project(project)
    except gitlab.GitlabGetError:
        return create_project(project)


def update_project(project, orig_project):
    changed_fields = []
    if orig_project.slug != project.slug:
        changed_fields.append("slug")
    if orig_project.name != project.name:
        changed_fields.append("name")

    gl = get_client()
    if not project.gitlab_id:
        # The API will freak out if we call .get() w/o an id
        raise ValueError("Project has no gitlab_id")
    gl_project = gl.projects.get(project.gitlab_id)

    if len(changed_fields):
        if "slug" in changed_fields:
            gl_project.path = project.slug
            gl_project.name = project.slug

        gl_project.save()

    update_project_deploy_key(gl_project, project.ssh_pubkey)

    return gl_project


def update_project_deploy_key(gl_project, pubkey):
    for key in gl_project.keys.list():
        key_bytes = key.key.encode()
        if get_pubkey_fingerprint(key_bytes) == get_pubkey_fingerprint(pubkey):
            # pubkey already attached to project
            return key

    return gl_project.keys.create(
        {"title": "Codio deploy key", "can_push": True, "key": pubkey}
    )
