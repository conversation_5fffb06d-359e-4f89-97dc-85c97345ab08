from django.contrib import admin
from django.utils.safestring import mark_safe

from hive.admin import register

from . import models


@register(models.Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ["name", "owner", "_public_url", "created", "updated"]
    search_fields = [
        "name",
        "owner__username",
        "owner__contact__first_name__unaccent",
        "owner__contact__last_name__unaccent",
        "owner__student__first_name__unaccent",
        "owner__student__last_name__unaccent",
    ]
    fields = [
        "slug",
        "uuid",
        "created",
        "updated",
        "_public_url",
        "name",
        "description",
        "owner",
        "thumbnail",
    ]
    readonly_fields = ["slug", "uuid", "created", "updated", "_public_url"]
    autocomplete_fields = ["owner"]
    ordering = ["-updated"]

    def _public_url(self, obj):
        return mark_safe(f'<a href="{obj.public_url}" target="_blank">open</a>')
