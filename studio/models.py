import json

from django.conf import settings
from django.db import models
from taggit.managers import TaggableManager

from users.models import User

from . import utils


class Project(models.Model):
    uuid = models.UUIDField(unique=True)
    owner = models.ForeignKey(
        User, related_name="owned_studio_projects", on_delete=models.PROTECT
    )
    contributors = models.ManyToManyField(User, related_name="studio_projects")
    ssh_key = models.BinaryField(blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    thumbnail = models.ImageField(blank=True, upload_to="studio/thumbnails")
    gitlab_id = models.CharField(max_length=16, blank=True)
    tags = TaggableManager(blank=True)

    def __str__(self):
        return self.slug

    def save(self, *args, **kwargs):
        if not self.ssh_key:
            self.ssh_key = memoryview(utils.generate_ssh_key())
        super().save(*args, **kwargs)

    @property
    def ssh_pubkey(self):
        """
        Returns SSH public key (with project/owner in comment)
        """
        if not self.ssh_key:
            return b""
        try:
            pubkey = utils.get_ssh_public_key(self.ssh_key.tobytes())
            keydata = json.dumps(
                {"project": self.uuid.hex, "owner": self.owner.email}
            ).encode()
            return pubkey + b" " + keydata
        except ValueError:
            return b""

    @property
    def ssh_pubkey_fingerprint(self):
        if not self.ssh_pubkey:
            return b""
        return utils.get_pubkey_fingerprint(self.ssh_pubkey)

    @property
    def public_url(self):
        return settings.STUDIO_PROJECT_URL_TEMPLATE.format(project=self)

    @property
    def repo_url(self):
        return f"**************:boldidea/studio/{self.slug}.git"
