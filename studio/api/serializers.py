from rest_framework import serializers
from taggit_serializer.serializers import TaggitSerializer, TagListSerializerField

from api.serializers import NamespacedUrlFieldMixin

from .. import models


class SSH<PERSON>eyField(serializers.Field):
    def to_representation(self, value):
        return value.tobytes().decode()

    def to_internal_value(self, data):
        return data.encode()


class ProjectSerializer(
    TaggitSerializer, NamespacedUrlFieldMixin, serializers.HyperlinkedModelSerializer
):
    url_namespace = "api:studio"
    tags = TagListSerializerField(required=False)
    ssh_key = SSHKeyField(required=False)
    is_owner = serializers.SerializerMethodField()
    owner_display = serializers.SerializerMethodField()
    owner_name = serializers.SerializerMethodField()
    owner_email = serializers.SerializerMethodField()

    class Meta:
        model = models.Project
        fields = (
            "url",
            "uuid",
            "slug",
            "name",
            "thumbnail",
            "description",
            "tags",
            "created",
            "updated",
            "public_url",
            "ssh_key",
            "ssh_pubkey",
            "repo_url",
            "is_owner",
            "owner_display",
            "owner_name",
            "owner_email",
        )
        read_only_fields = (
            "created",
            "updated",
            "is_owner",
            "owner_email",
            "owner_name",
        )
        lookup_field = "uuid"
        extra_kwargs = {
            "url": {"lookup_field": "uuid"},
            "ssh_pubkey": {"read_only": True},
        }

    def get_owner_display(self, obj):
        return str(obj.owner)

    def get_is_owner(self, obj):
        request = self.context["request"]
        return obj.owner == request.user

    def get_owner_name(self, obj):
        return obj.owner.get_full_name()

    def get_owner_email(self, obj):
        return obj.owner.email


class PublicProjectSerializer(ProjectSerializer):
    class Meta(ProjectSerializer.Meta):
        fields = (
            "url",
            "uuid",
            "slug",
            "name",
            "thumbnail",
            "description",
            "tags",
            "created",
            "updated",
            "public_url",
            "is_owner",
            "owner_email",
            "owner_name",
        )
