from django.db import transaction
from rest_framework import viewsets
from rest_framework.permissions import SAFE_METHODS, BasePermission, IsAuthenticated

from .. import gitlab, models
from . import serializers


class IsProjectOwner(BasePermission):
    def has_object_permission(self, request, view, obj):
        if request.method in SAFE_METHODS:
            return True
        return obj.owner == request.user


class ProjectViewSet(viewsets.ModelViewSet):
    serializer_class = serializers.PublicProjectSerializer
    queryset = models.Project.objects.all()
    lookup_field = "uuid"

    @transaction.atomic
    def perform_create(self, serializer):
        project = serializer.save(owner=self.request.user)

        # Create (or get) the repository and save the repo id
        gl_project = gitlab.get_or_create_project(project)
        project.gitlab_id = gl_project.id
        project.save()

    @transaction.atomic
    def perform_update(self, serializer):
        orig = self.get_object()
        project = serializer.save()

        # Update the repository if anything has changed
        if project.gitlab_id:
            gitlab.update_project(project, orig)
        else:
            # in cases where the gitlab_id may not have been recorded
            gitlab.create_project(project)

    def get_permissions(self):
        permission_classes = [IsAuthenticated]
        if self.action not in ("list", "retrieve", "create"):
            permission_classes.append(IsProjectOwner)
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        qs = self.queryset
        tag = self.request.GET.get("tag")
        if tag:
            qs = qs.filter(tags__name=tag)
        slug = self.request.GET.get("slug")
        if slug:
            qs = qs.filter(slug=slug)
        return qs

    def get_serializer(self, instance=None, **kwargs):
        # Use ProjectSerializer for instance owners (or during creation),
        # otherwise use PublicProjectSerializer
        context = self.get_serializer_context()
        kwargs["context"] = context
        serializer_class = self.get_serializer_class()
        is_owner = False

        if kwargs.get("many"):
            return serializer_class(instance, **kwargs)

        is_owner = instance and instance.owner == self.request.user
        if is_owner or self.action == "create":
            serializer_class = serializers.ProjectSerializer

        return serializer_class(instance, **kwargs)

    def get_object(self):
        obj = super().get_object()
        self.check_object_permissions(self.request, obj)
        return obj
