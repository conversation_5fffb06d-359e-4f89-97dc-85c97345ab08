from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from hive.utils import get_search_results, model_hashid

from ..models import User
from ..utils import is_student, is_volunteer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def search(request):
    search_term = request.GET.get("q")
    if not search_term:
        return Response({"users": []})

    search_fields = [
        "contact__first_name__unaccent",
        "contact__last_name__unaccent",
        "student__first_name__unaccent",
        "student__last_name__unaccent",
    ]

    users = User.objects.all()
    results = []
    for user in get_search_results(users, search_term, search_fields):
        if is_student(user):
            name = user.student.name
            first_name = user.student.first_name
            last_name = user.student.last_name
        else:
            name = user.contact.name
            first_name = user.contact.first_name
            last_name = user.contact.last_name
        results.append(
            {
                "id": user.id,
                "name": name,
                "first_name": first_name,
                "last_name": last_name,
                "canonical_username": user.canonical_username,
                "is_student": is_student(user),
                "is_volunteer": is_volunteer(user),
            }
        )

    return Response({"results": results, "count": len(results)})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_info(request):
    """
    Used by external services to retrive user info with a given token
    """
    user_is_student = is_student(request.user)
    user_is_volunteer = is_volunteer(request.user)

    if request.user.canonical_username:
        username = request.user.canonical_username
    elif user_is_student:
        username = request.user.student.student_id
    else:
        username = None

    email = request.user.email
    if not email and user_is_student:
        student = request.user.student
        email = f"{student.student_id}@{settings.STUDENT_USER_EMAIL_DOMAIN}"

    response = {
        "id": model_hashid(request.user),
        "username": username,
        "email": email,
        "name": str(request.user.get_full_name()),
        "is_student": user_is_student,
        "is_volunteer": user_is_volunteer,
    }

    if user_is_student:
        response["student_id"] = request.user.student.student_id
        response["birth_date"] = request.user.student.birth_date
    elif user_is_volunteer:
        response["birth_date"] = request.user.contact.volunteer.birth_date
        photo = request.user.contact.photo
        response["photo"] = photo and request.build_absolute_uri(photo.url) or None

    return Response(response)
