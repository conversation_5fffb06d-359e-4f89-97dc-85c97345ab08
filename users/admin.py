from django.contrib import admin

from hive.admin import admin_site

from . import models
from .forms import UserCreationForm


class AdminUserCreationForm(UserCreationForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["is_staff"].help_text = (
            "WARNING: This will grant the user permission to access/change any data."
        )

    class Meta(UserCreationForm.Meta):
        fields = ("email", "is_staff")


class UserTypeFilter(admin.SimpleListFilter):
    title = "User type"
    parameter_name = "type"

    def queryset(self, request, queryset):
        val = self.value()
        if val is None:
            return queryset
        if val == "volunteer":
            return queryset.filter(contact__volunteer__isnull=False)
        if val == "account":
            return queryset.filter(account__isnull=False)
        if val == "staff":
            return queryset.filter(is_staff=True)

    def lookups(self, request, model_admin):
        return (
            ("volunteer", "Volunteer"),
            ("account", "Registration account"),
            ("staff", "Staff"),
        )


class UserAdmin(admin.ModelAdmin):
    form = AdminUserCreationForm
    list_display = ["name", "email", "is_active", "is_staff"]
    list_filter = [UserTypeFilter]
    search_fields = [
        "email__unaccent",
        "username__unaccent",
        "contact__first_name__unaccent",
        "contact__last_name__unaccent",
        "student__first_name__unaccent",
        "student__last_name__unaccent",
    ]

    def name(self, obj):
        return obj.get_full_name(default_attr="username")

    def has_add_permission(self, request, *args, **kwargs):
        # New users should create an account through the volunteer portal:
        # https://hive.boldidea.org/volunteer/profile/
        return False

    def save_model(self, request, obj, form, change):
        # Currently we make no distinction between staff/superuser. We allow
        # anyone on staff to access/change anything in the system.
        if obj.is_staff:
            obj.is_superuser = True
        super().save_model(request, obj, form, change)

    def get_queryset(self, request, *args, **kwargs):
        queryset = super().get_queryset(request, *args, **kwargs)
        # exclude students from the user admin, they are managed separately
        queryset = queryset.exclude(student__isnull=False)
        return queryset


admin_site.register(models.User, UserAdmin)
