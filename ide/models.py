import json

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from django.db import models

from hive.utils import decrypt, encrypt
from users.models import User

from . import utils


class ProjectType(models.Model):
    name = models.CharField(max_length=256)
    description = models.TextField()
    slug = models.SlugField()
    image = models.ImageField()
    template = models.CharField(max_length=256)
    defaults = JSONField(default=dict, blank=True)

    def __str__(self):
        return self.name


class Project(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.PROTECT, related_name="ide_projects"
    )
    type = models.ForeignKey(
        ProjectType, on_delete=models.PROTECT, related_name="projects"
    )
    name = models.CharField(max_length=256)
    description = models.TextField(blank=True)
    slug = models.SlugField()
    config = J<PERSON><PERSON>ield(default=dict, blank=True)
    archived = models.Bo<PERSON>anField(default=False)
    date_created = models.DateTime<PERSON>ield(auto_now_add=True)
    last_accessed = models.DateTimeField(auto_now_add=True)
    share_token = models.CharField(max_length=128, null=True, unique=True)
    shared_with = models.ManyToManyF<PERSON>(
        User, related_name="shared_projects", blank=True
    )
    workspace_name = models.CharField(max_length=256, blank=True)

    class Meta:
        unique_together = ["user", "slug"]
        ordering = ["-last_accessed"]

    def __str__(self):
        return self.name

    @property
    def hashid(self):
        # 8-character hash representation of self.id
        return utils.hashid(self.id, salt=None)  # empty salt for backward compat

    @property
    def certificate(self):
        if self.certificate_data:
            return json.loads(decrypt(self.encrypted_default_password).decode())
        return {}

    @certificate.setter
    def certificate(self, value):
        self.certificate_data = encrypt(json.dumps(value).encode())
