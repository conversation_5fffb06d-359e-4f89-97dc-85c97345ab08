from django.core.management.base import BaseCommand

from ide import acme_utils


class Command(BaseCommand):
    help = "Create an ACME account (used for issuing SSL certificates)"

    def handle(self, *args, **options):
        acme_utils.create_acct_key()
        self.stdout.write(
            self.style.SUCCESS(f"Created new account key at {acme_utils.ACCT_KEY_PATH}")
        )
        acme_utils.create_registration()
        self.stdout.write(
            self.style.SUCCESS("Successfully created ACME account registration")
        )
