import logging
import os
import pathlib
import shutil

from django.core.management.base import BaseCommand, CommandError

from ide import utils
from ide.models import Project
from users.utils import is_student


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "output_dir",
            metavar="OUTPUT_DIR",
            type=str,
            help="Directory to put downloaded files",
        )
        parser.add_argument(
            "--username",
            dest="username",
            metavar="USERNAME",
            type=str,
            default=None,
            help="Download projects for a user",
        )
        parser.add_argument(
            "--project",
            dest="project_slug",
            metavar="PROJECT_SLUG",
            type=str,
            default=None,
            help="Download a single project",
        )
        parser.add_argument(
            "--overwrite",
            action="store_true",
            default=False,
            help="Overwrite existing .zip files in output dir",
        )

    def handle(self, *args, **options):
        logger = logging.getLogger(__name__)

        projects = Project.objects.all()

        if options.get("username"):
            projects = projects.filter(
                user__canonical_username__iexact=options["username"]
            )

        if options.get("project_slug"):
            if not options.get("username"):
                raise CommandError("You must provide --username when using --project")
            projects = projects.filter(slug=options["project_slug"])

        if projects.count() == 0:
            raise CommandError("No projects found")

        if not os.path.exists(options["output_dir"]):
            raise CommandError(f'Path does not exist: {options["output_dir"]}')

        logger.info(f"Downloading {projects.count()} projects...")
        for project in projects:
            # create backup by user
            user = project.user
            user_type = is_student(user) and "students" or "volunteers"
            user_dir_name = f"{user.canonical_username} - {user.get_full_name()}"
            user_dir = os.path.join(user_type, user_dir_name)
            project_dir = os.path.join(user_dir, project.slug)
            abs_project_dir = os.path.join(options["output_dir"], project_dir)
            archive_basename = os.path.join(user_dir, project.slug)
            archive_path = f"{archive_basename}.zip"
            abs_archive_basename = os.path.join(options["output_dir"], archive_basename)
            abs_archive_path = f"{abs_archive_basename}.zip"

            pathlib.Path(abs_project_dir).mkdir(parents=True, exist_ok=True)

            exists = os.path.exists(abs_archive_path)
            if not options["overwrite"] and exists:
                logger.info(f"Exists: {archive_path}")
                shutil.rmtree(abs_project_dir)
                continue

            utils.create_project_archive(project, archive_path)

            action = exists and "Updated" or "Created"
            logger.info(f"{action}: {archive_path}")
