from django.urls import path

from ide import views

app_name = "ide"

urlpatterns = [
    path("", views.projects, name="projects"),
    path("username/", views.choose_username, name="choose-username"),
    path("new-project/", views.project_details, name="new-project"),
    path(
        "projects/<slug:project_slug>/edit/", views.project_details, name="edit-project"
    ),
    path(
        "projects/<slug:project_slug>/healthcheck/",
        views.healthcheck,
        name="healthcheck",
    ),
    path("projects/<slug:project_slug>/", views.ide, name="project"),
    path(
        "projects/<slug:project_slug>/download/",
        views.download_project,
        name="project-download",
    ),
    path(
        "projects/<slug:project_slug>/delete/",
        views.delete_project,
        name="delete_project",
    ),
    path(
        "projects/shared/<str:username>/<slug:project_slug>/",
        views.ide,
        name="shared-project",
    ),
    path(
        "projects/shared/<str:username>/<slug:project_slug>/download/",
        views.download_project,
        name="shared-project-download",
    ),
    path(
        "projects/shared/<str:username>/<slug:project_slug>/remove/",
        views.remove_shared_project,
        name="remove-shared-project",
    ),
    path(
        "projects/shared/<str:username>/<slug:project_slug>/healthcheck/",
        views.healthcheck,
        name="shared-healthcheck",
    ),
]
