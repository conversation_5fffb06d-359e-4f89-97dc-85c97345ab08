import json
import logging
import os
import shutil
import subprocess
import tempfile
from datetime import datetime

import requests
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.urls import reverse
from django.utils import timezone
from jsonmerge import merge

from hive.utils import decode_hashid, hashid  # noqa
from sso.utils import get_auth_token, register_authorization_plugin
from users.utils import is_student, is_volunteer
from volunteers.verification import VerificationFailed, verify_student_access

logger = logging.getLogger(__name__)

SCHEME = settings.SCHEME
STUDENT_DOMAIN = settings.DOMAINS["student"]
HIVE_DOMAIN = settings.DOMAINS["hive"]
STORAGE_ACCT_NAME = "ideworkspacestorage"
AZ_FILE_BASE_URL = f"https://{STORAGE_ACCT_NAME}.file.core.windows.net"
AZ_STORAGE_KEY = settings.AZURE_STORAGE_ACCT_KEY


def get_project_config(project, course=None):
    assert project.user.canonical_username, (
        "Cannot generate resource manifest: "
        f'User "{project.user}" does not have a canonical username.'
    )

    # Construct the workspace config
    defaults = project.type.defaults or {}
    defaults = defaults.copy()
    defaults = merge(defaults, course and course.ide_config or {})

    # If roadmap is specified in docs, use the correct URL based on which site we're using
    # FIXME: This causes issues when a mentor tries to access a student's workspace
    if defaults.get("docs", {}).get("roadmap"):
        embed_url = reverse("courses:embed-roadmap")
        if settings.SITE == "student":
            defaults["docs"]["roadmap"][
                "url"
            ] = f"{SCHEME}://{STUDENT_DOMAIN}{embed_url}"
        else:
            defaults["docs"]["roadmap"]["url"] = f"{SCHEME}://{HIVE_DOMAIN}{embed_url}"

    config = merge(
        defaults,
        {
            "projectId": project.hashid,
            "projectSlug": project.slug,
            "projectType": project.type.slug,
            "username": project.user.canonical_username,
            "userId": hashid(
                project.user.id, salt=None
            ),  # Empty salt for backward compat
            "uid": settings.IDE_STARTING_UID + project.user.id,
            "gid": settings.IDE_STARTING_GID + project.user.id,
        },
    )

    config.update(project.config or {})

    return config


def init_workspace(project, course=None):
    project.last_accessed = timezone.now()

    auth_token = get_auth_token(project.user, "ide")
    config = get_project_config(project, course)

    # Set authorization
    headers = {"Authorization": f"AuthToken {auth_token}"}

    # Make request
    url = f"{settings.IDE_CONTROLLER_URL}/api/workspaces"
    response = requests.post(url, json=config, headers=headers)
    response.raise_for_status()
    response_data = response.json()

    # Save the workspace name on the project
    project.workspace_name = response_data["name"]
    project.save()

    return response_data


def format_error(error):
    print(json.dumps(json.loads(error.body), indent=2))


@register_authorization_plugin("ide")
def authorize_for_ide(request):
    """
    The ingress endpoint will send an auth request to the SSO endpoint with the headers:

       X-Authorize-For: ide
       X-Project-Id: abcd1234

    X-Authorize-For: ide triggers the sso user-info endpoint to call this function.
    This ensures that the user sending the request is authorized to view the given project.
    """
    from . import models

    if request.META.get("HTTP_X_PROJECT_ID"):
        logger.info(f'Got project ID: {request.META["HTTP_X_PROJECT_ID"]}')
        hashid = request.META["HTTP_X_PROJECT_ID"]
        project_id = decode_hashid(hashid, salt=None)  # Empty salt for backwards compat
        logger.info(f"Decoded project ID: {project_id}")
        try:
            project = models.Project.objects.get(pk=project_id)
        except models.Project.DoesNotExist:
            logger.error(f"Project not found (pk={project_id})")
            raise PermissionDenied

        if (
            project.share_token
            and request.META.get("HTTP_X_SHARE_TOKEN") == project.share_token
        ):
            return

        if request.META.get("HTTP_X_SHARE_TOKEN"):
            logger.info(
                f'Share token "{request.META.get("HTTP_X_SHARE_TOKEN")}" '
                "does not match project share token"
            )

        # allow user to access their own project
        if request.user == project.user:
            logger.info(f"User {request.user.canonical_username} is project owner")
            return

        # allow user to access other projects that are shared
        if request.user in project.shared_with.all():
            logger.info(f"User {request.user.canonical_username} is in shared users")
            return

        # allow volunteers to access student projects
        if is_volunteer(request.user) and is_student(project.user):
            try:
                verify_student_access(
                    request.user.contact.volunteer, project.user.student
                )
            except VerificationFailed:
                pass
            else:
                logger.info(
                    f"Volunteer {request.user.canonical_username} may access student info"
                )
                return

        # allow super user
        if request.user.is_superuser:
            logger.info(f"User {request.user.canonical_username} is a superuser")
            return

        logger.info(
            f"User {request.user.canonical_username} does not have access to project"
        )
    else:
        logger.debug("X-PROJECT-ID header was not given")

    raise PermissionDenied


def generate_sas(share_name):
    exp = datetime.now() + relativedelta(days=2)

    # generate SAS
    sas_cmd = (
        "az",
        "storage",
        "share",
        "generate-sas",
        "--account-key",
        AZ_STORAGE_KEY,
        "--account-name",
        STORAGE_ACCT_NAME,
        "--name",
        share_name,
        "--permissions",
        "lrw",
        "--expiry",
        exp.strftime(exp.strftime("%Y-%m-%dT00:00Z")),
        "--https-only",
    )

    result = subprocess.run(sas_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if result.returncode != 0:
        output = result.stderr and result.stderr.decode("utf-8")
        if not output:
            output = result.stdout.decode("utf-8")
        raise Exception(
            f"Could not generate SAS token (returncode={result.returncode}): {output}"
        )
    return result.stdout.decode("utf-8").strip().strip('"')


def get_project_archive_path(project):
    return os.path.join(
        settings.IDE_PROJECT_EXPORT_DIR, f"{project.slug}-{project.hashid}.zip"
    )


def create_project_archive(project, archive_path):
    if not AZ_STORAGE_KEY:
        raise EnvironmentError("settings.AZURE_STORAGE_ACCT_KEY must be set")

    user = project.user
    hash_id = hashid(user.id, salt=None)  # empty salt for backward compat
    share_name = f"ide-user-storage-{hash_id}"
    sas_token = generate_sas(share_name)
    url = f"{AZ_FILE_BASE_URL}/{share_name}/workspace-{project.hashid}/*?{sas_token}"

    with tempfile.TemporaryDirectory() as temp_dir:
        cmd = ("azcopy", "copy", url, temp_dir, "--recursive")
        # logger.debug(f'{share_name}/workspace-{project.hashid} -> {project_dir}')
        proc = subprocess.Popen(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=False
        )
        (stdout, stderr) = proc.communicate()

        logfile = f"/tmp/ide-download-{project.hashid}.error.log"
        if proc.returncode != 0:
            # store error details in file
            with open(logfile, "w") as f:
                if stdout:
                    f.write(stdout.decode("utf-8"))
                if stderr:
                    f.write(stderr.decode("utf-8"))
            raise Exception(
                f"Error while downloading project id {project.id}"
                f"(returncode={proc.returncode}). See {logfile}."
            )

        archive_basename, ext = os.path.splitext(archive_path)
        zip_dir_path = os.path.join(temp_dir, "project-files")
        if not os.path.exists(zip_dir_path):
            zip_dir_path = temp_dir
        shutil.make_archive(archive_basename, "zip", zip_dir_path)

        return archive_path
