import logging
import os

import acme.client
import acme.crypto_util
import acme.errors
import acme.messages
import acme.standalone
import josepy
import OpenSSL
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.serialization import load_pem_private_key
from django.conf import settings

conf = settings.ACME

USER_AGENT = conf.get("USER_AGENT", "boldidea-ide-certmanager")
ACCT_KEY_BITS = conf.get("ACCT_KEY_BITS", 2048)  # Account key size
CERT_PKEY_BITS = conf.get("CERT_PKEY_BITS", 2048)  # Certificate private key size
STAGING_URL = "https://acme-staging-v02.api.letsencrypt.org/directory"
DIRECTORY_URL = conf.get("DIRECTORY_URL", STAGING_URL)
ACCT_KEY_PATH = conf["ACCT_KEY_PATH"]
ACCT_REGR_PATH = conf["ACCT_REGR_PATH"]
ACCT_EMAIL = conf["ACCT_EMAIL"]

logger = logging.getLogger(__name__)


def get_client(acct_key=None, new_account=False):
    if acct_key is None:
        acct_key = get_acct_key()

    regr = None
    if not new_account:
        # load the saved registration
        with open(ACCT_REGR_PATH, "r") as regr_file:
            regr = acme.messages.RegistrationResource.json_loads(regr_file.read())

    net = acme.client.ClientNetwork(acct_key, account=regr, user_agent=USER_AGENT)
    if DIRECTORY_URL == STAGING_URL:
        logger.warn(
            "Using ACME staging url for issuing certs "
            "(only use this in development environments)."
        )
    directory = acme.messages.Directory.from_json(net.get(DIRECTORY_URL).json())
    client = acme.client.ClientV2(directory, net=net)
    return client


def _get_registration(client, acct_key):
    """
    only use this if you've lost the regr file but still have the acct_key
    """
    # from https://community.letsencrypt.org/t/103452/3
    reg_data = acme.messages.NewRegistration(
        key=acct_key.public_key(), only_return_existing=True
    )
    response = client._post(client.directory["newAccount"], reg_data)
    regr = client._regr_from_response(response)
    client.net.account = regr  # sets the account for future requests
    return regr


def create_registration():
    acct_key = get_acct_key()
    client = get_client(acct_key, new_account=True)
    # Register and agree to TOS
    regr = client.new_account(
        acme.messages.NewRegistration.from_data(
            email=ACCT_EMAIL, terms_of_service_agreed=True
        )
    )
    # Save registration
    with open(ACCT_REGR_PATH, "w") as regr_file:
        regr_file.write(regr.json_dumps())
    return regr


def create_acct_key():
    # Generating acct key
    key = rsa.generate_private_key(
        public_exponent=65537, key_size=2048, backend=default_backend()
    )

    # Convert to pem
    pem = key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.TraditionalOpenSSL,
        encryption_algorithm=serialization.NoEncryption(),
    )

    with open(ACCT_KEY_PATH, "wb") as pem_out:
        pem_out.write(pem)


def get_acct_key():
    if not os.path.exists(ACCT_KEY_PATH):
        raise OSError(
            "ACME Account Key does not exist. Run `manage.py create_acme_account`."
        )

    with open(ACCT_KEY_PATH, "rb") as pem_in:
        pemlines = pem_in.read()
    key = load_pem_private_key(pemlines, None, default_backend())

    return josepy.JWKRSA(key=key)


def new_pkey():
    # Create private key.
    pkey = OpenSSL.crypto.PKey()
    pkey.generate_key(OpenSSL.crypto.TYPE_RSA, CERT_PKEY_BITS)
    return OpenSSL.crypto.dump_privatekey(OpenSSL.crypto.FILETYPE_PEM, pkey)


def new_csr(domains, pkey_pem):
    """Create certificate signing request."""
    return acme.crypto_util.make_csr(pkey_pem, domains)


def select_dns_challenges(order):
    for authz in order.authorizations:
        for challenge in authz.body.challenges:
            if isinstance(challenge.chall, acme.challenges.DNS01):
                yield challenge


def get_challenge_tokens(order, acct_key):
    challenges = {}
    for authz in order.authorizations:
        for challenge in authz.body.challenges:
            if isinstance(challenge.chall, acme.challenges.DNS01):
                domain = authz.body.identifier.value
                token = challenge.chall.validation(acct_key)
                if not challenges.get(domain):
                    challenges[domain] = []
                challenges[domain].append(token)
    return challenges.items()
