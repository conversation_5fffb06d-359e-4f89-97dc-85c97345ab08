import os
from urllib.parse import quote as quote_url
from urllib.parse import urljoin

import requests
import requests.exceptions
from django.conf import settings
from django.contrib.auth.views import login_required
from django.core import validators
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpResponse, HttpResponseNotFound, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect, render, reverse
from django.utils import timezone

from users.models import User

from . import forms, models, utils


@login_required
def projects(request):
    # get the currently selected course
    from courses.views import RedirectException, get_course_handler

    handler = get_course_handler()
    try:
        course = handler.get_course(request)
    except RedirectException as e:
        e.next = quote_url(request.get_full_path())
        return e.response

    # deprecating old IDE, but making projects still available
    auth_token = "NOT_IMPLEMENTED"

    # auth_token = utils.get_auth_token(request.user, 'ide')
    context = {
        "course": course,
        "projects": request.user.ide_projects.exclude(archived=True),
        "shared_projects": request.user.shared_projects.exclude(archived=True),
        "controller_url": settings.IDE_CONTROLLER_URL,
        "auth_token": auth_token,
    }
    return render(request, "ide/projects.html", context)


@login_required
@transaction.atomic
def project_details(request, project_slug=None):
    if project_slug is not None:
        instance = get_object_or_404(
            models.Project, slug=project_slug, user=request.user
        )
        tpl = "ide/edit_project.html"
    else:
        instance = None
        tpl = "ide/new_project.html"

    initial = {"user": request.user}
    if request.method == "POST":
        form = forms.ProjectForm(request.POST, initial=initial, instance=instance)
        if form.is_valid():
            instance = form.save()
            if project_slug is None:
                url = reverse("ide:project", kwargs={"project_slug": instance.slug})
            else:
                url = reverse("ide:projects")
            return redirect(url + "#form")
    else:
        form = forms.ProjectForm(initial=initial, instance=instance)

    context = {
        "form": form,
        "project": instance,
        "project_types": models.ProjectType.objects.all(),
    }
    return render(request, tpl, context)


@login_required
def delete_project(request, project_slug):
    instance = get_object_or_404(models.Project, slug=project_slug, user=request.user)

    if request.method == "POST" and request.POST.get("confirm_delete"):
        instance.delete()
        url = reverse("ide:projects")
        return redirect(url)

    context = {"project": instance}
    return render(request, "ide/delete_project.html", context)


@login_required
def ide(request, project_slug, username=None):
    # ensure user has canonical username
    if not request.user.canonical_username:
        next = quote_url(request.get_full_path())
        return HttpResponseRedirect(
            "{}?next={}".format(reverse("ide:choose-username"), next)
        )

    if username is None:
        user = request.user
    else:
        user = User.objects.get(canonical_username=username)

    project = get_object_or_404(models.Project, slug=project_slug, user=user)

    # get the currently selected course
    from courses.views import RedirectException, get_course_handler

    handler = get_course_handler()
    try:
        course = handler.get_course(request)
    except RedirectException as e:
        e.next = quote_url(request.get_full_path())
        return e.response

    # Initialize the workspace on page load
    workspace = utils.init_workspace(project, course=course)
    auth_token = utils.get_auth_token(request.user, "ide")

    project.last_accessed = timezone.now()
    project.workspace_name = workspace["name"]
    project.save()

    context = {
        "project": project,
        "controller_url": settings.IDE_CONTROLLER_URL,
        "token": auth_token,
    }
    response = render(request, "ide/ide.html", context)

    return response


@login_required
def remove_shared_project(request, username, project_slug):
    project = get_object_or_404(
        models.Project, user__canonical_username=username, slug=project_slug
    )
    project.shared_with.remove(request.user)
    return HttpResponseRedirect(reverse("ide:projects"))


@login_required
def workspace_status(request, project_slug):
    from ide.api.views import workspace_status as api_workspace_status

    project = get_object_or_404(models.Project, slug=project_slug, user=request.user)
    return api_workspace_status(request, project.hashid)


@login_required
def choose_username(request):
    next = request.POST.get("next", request.GET.get("next"))
    user = request.user
    error = None

    if request.method == "POST":
        username = request.POST.get("username")
        try:
            validators.validate_slug(username)
        except ValidationError:
            error = (
                "Username may only contain letters, numbers, underscores or hyphens."
            )

        if User.objects.filter(canonical_username=username).exists():
            error = f'Username "{username}" is taken. Please choose another.'

        if error is None:
            user.canonical_username = username
            user.save()
            return redirect(next)

    context = {"user": user, "error": error, "next": next}
    return render(request, "ide/choose_username.html", context)


@login_required
def healthcheck(request, project_slug, username=None):
    """
    Temporary stand-in for workspace healthcheck until we can get CORS to work
    """
    if username is None:
        user = request.user
    else:
        user = User.objects.get(canonical_username=username)

    project = get_object_or_404(models.Project, slug=project_slug, user=user)
    auth_token = utils.get_auth_token(request.user, "ide")

    if not project.workspace_name:
        return HttpResponseNotFound("Project has no workspace name")

    # Set authorization
    headers = {"Authorization": f"AuthToken {auth_token}"}

    # Get workspace data
    url = f"{settings.IDE_CONTROLLER_URL}/api/workspaces/{project.workspace_name}"
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    workspace = response.json()

    if workspace["status"]["code"] != "READY":
        return HttpResponseNotFound(f"Workspace {project.workspace_name} is not ready.")

    # Ping healthcheck URL
    healthcheck_url = urljoin(workspace["url"], "healthcheck")
    try:
        response = requests.get(healthcheck_url, headers=headers, timeout=5)
        if response.status_code in (404, 502, 503):
            return HttpResponseNotFound(
                f"Workspace {project.workspace_name} is not ready (endpoint unavailable)."
            )
    except requests.exceptions.Timeout:
        return HttpResponseNotFound(
            f"Workspace {project.workspace_name} is not ready (endpoint unavailable)."
        )

    response.raise_for_status()

    return HttpResponse(response.text)


@login_required
def download_project(request, project_slug, username=None):
    if username is None:
        user = request.user
    else:
        user = get_object_or_404(User, canonical_username=username)

    project = get_object_or_404(models.Project, slug=project_slug, user=user)

    context = {
        "project": project,
    }

    return render(request, "ide/download_project.html", context)

    # # Note: Old IDE projects have been archived to Google Drive
    #
    #  export_file = utils.get_project_archive_path(project)
    #  basename = os.path.basename(export_file)
    #
    #  if os.path.exists(export_file):
    #      os.remove(export_file)
    #
    #  utils.create_project_archive(project, export_file)
    #
    #  download_url = f'{settings.IDE_PROJECT_EXPORT_URL}{basename}'
    #
    #  return HttpResponseRedirect(download_url)
