from django import forms
from django.template.defaultfilters import slugify

from hive.forms import BaseForm

from . import models


class ProjectForm(BaseForm, forms.ModelForm):
    class Meta:
        model = models.Project
        widgets = {"type": forms.HiddenInput, "user": forms.HiddenInput}
        fields = ["name", "description", "type", "user", "shared_with"]

    def clean(self):
        user = self.cleaned_data["user"]
        name = self.cleaned_data["name"]
        if len(name) > 50:
            raise forms.ValidationError(
                {"name": ["Project name must be no longer than 50 characters."]}
            )
        slug = slugify(name)
        if models.Project.objects.filter(user=user, slug=slug).exists():
            raise forms.ValidationError(
                {
                    "name": [
                        "You already have a project with this name. Plese choose a different name."
                    ]
                }
            )
        self.cleaned_data["slug"] = slug

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.slug = slugify(instance.name)
        return super().save(commit=commit)
