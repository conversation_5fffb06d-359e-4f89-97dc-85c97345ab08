from django.contrib import admin
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from django_json_widget.widgets import JSONEditorWidget

from hive.admin import register

from . import models, utils


@register(models.ProjectType)
class ProjectTypeAdmin(admin.ModelAdmin):
    list_display = ["name", "slug"]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget}}


@register(models.Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ["_name", "name", "user", "type", "date_created", "last_accessed"]
    search_fields = ["name", "user__canonical_username"]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget}}
    autocomplete_fields = ["shared_with"]

    def _name(self, project):
        username = utils.hashid(
            project.user.id, salt=None
        )  # empty salt for backward compat
        if project.user.canonical_username:
            username = project.user.canonical_username.lower()
        return f"{username}--{project.slug}"
