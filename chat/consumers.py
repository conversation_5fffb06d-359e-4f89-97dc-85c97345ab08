import json
import logging

from asgiref.sync import async_to_sync
from channels.generic.websocket import WebsocketConsumer
from django.conf import settings
from django.utils import timezone

from clubs.models import Club

from . import models, status, utils

logger = logging.getLogger(__name__)


class ChatConsumer(WebsocketConsumer):
    def connect(self):
        self.club = None
        self.team_name = "global"

        self.user = self.scope["user"]

        club_code = self.scope["url_route"]["kwargs"].get("club_code")
        if club_code:
            self.club = self.get_club(club_code)
            self.team_name = club_code

        self.group_name = f"chat_{self.team_name}"

        # when channel name is passed, we are using a single-channel chat session,
        # such as with the chrome extension.
        channel_name = self.scope["url_route"]["kwargs"].get("channel_name")
        if channel_name:
            self.group_name = f"chat_{self.team_name}_{channel_name}"

        # Join channel group
        async_to_sync(self.channel_layer.group_add)(
            self.group_name, self.channel_name  # internally-generated channel name
        )

        self.accept()

        self.connection_id = status.add_connection(self.user)
        self._broadcast_status()

    def disconnect(self, close_code):
        # Leave channel group
        async_to_sync(self.channel_layer.group_discard)(
            self.group_name, self.channel_name  # internally-generated channel name
        )

        status.remove_connection(self.user, self.connection_id)
        self._broadcast_status()

    # Receive message from WebSocket
    def receive(self, text_data):
        event = json.loads(text_data)
        type = event["type"]

        if type == "chat_message":
            # Make sure user is allowed to send message to channel
            user_channels = utils.get_user_channels(self.user, self.club)
            if user_channels.filter(name=event["channel"]).count() == 0:
                return

            # Client would like to send a message to the group
            message_instance = self.save_message(event)
            message = utils.render_markdown(event["message"])
            user_info = utils.get_user_info(self.user)
            time = timezone.now()

            # Send message to the group
            data = {
                "type": "chat_message",
                "channel": event["channel"],
                "messageId": message_instance.id,
                "userId": message_instance.user_id,
                "message": message,
                "time": time.isoformat(),
            }
            data.update(user_info)
            async_to_sync(self.channel_layer.group_send)(self.group_name, data)

        elif type == "user_status":
            # Client would like to update the user status and broadcast the
            # status change to the group.
            allowed_keys = settings.CHAT_STATUS_TYPES
            status_data = {k: v for k, v in event.items() if k in allowed_keys}
            status.update_user_status(self.user, **status_data)
            self._broadcast_status()

    def _broadcast_status(self):
        # update the user_info
        user_info = utils.get_user_info(self.user)

        # broadcast the status change to other consumers
        data = {
            "type": "user_status",
        }
        data.update(user_info)
        async_to_sync(self.channel_layer.group_send)(self.group_name, data)

    def chat_message(self, text_data):
        # This consumer received a status update. Send message to WebSocket client.
        self.send(text_data=json.dumps(text_data))

    def user_status(self, text_data):
        # This consumer received a status update. Send status update to WebSocket client.
        self.send(text_data=json.dumps(text_data))

    def get_club(self, club_code):
        return Club.objects.get(club_code=club_code)

    def save_message(self, event):
        channel = models.Channel.objects.get(name=event["channel"])
        return models.Message.objects.create(
            user=self.user, channel=channel, data=event
        )
