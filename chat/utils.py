from django.conf import settings
from django.db.models import Q
from django.templatetags.static import static
from django.urls import reverse
from django.utils.safestring import mark_safe
from easy_thumbnails.files import get_thumbnailer
from markdown import markdown

from users.models import User

from . import status


def get_client_settings(request, club, club_code=None):
    url_kwargs = {}
    if club_code:
        url_kwargs = {"club_code": club_code}
    history_url_kwargs = {"channel_name": "__channel__"}
    history_url_kwargs.update(url_kwargs)
    history_endpoint = reverse("chat:history", kwargs=history_url_kwargs)
    users_endpoint = reverse("chat:users", kwargs=url_kwargs)

    return {
        "historyEndpoint": history_endpoint,
        "usersEndpoint": users_endpoint,
        "websocketUrl": f"ws://{request.get_host()}/ws/chat/{club.club_code}/",
        "defaultChannel": settings.CHAT_DEFAULT_CHANNEL,
    }


def get_club_users(club):
    return User.objects.filter(
        Q(contact__volunteer__club__in=[club])
        | Q(student__clubs__in=[club])
        | Q(is_staff=True)
    )


def get_user_info(user):
    # get default profile photo
    n = (user.id % 38) + 1
    photo_url = static(f"chat/images/avatars/generic-user-{n}.png")
    photo_thumb = static(f"chat/images/avatars/generic-user-{n}-48.png")
    if user.photo:
        try:
            user.photo.file
        except FileNotFoundError:
            pass
        else:
            photo_url = user.photo.url
            thumbnailer = get_thumbnailer(user.photo)
            thumb = thumbnailer.get_thumbnail({"size": (48, 48)})
            photo_thumb = thumb.url

    return {
        "userId": user.id,
        "name": user.get_full_name(),
        "username": user.username,
        "avatar": photo_url,
        "avatarthumb": photo_thumb,
        "status": status.get_user_status(user),
    }


def render_markdown(text):
    html = markdown(
        text, extensions=["markdown.extensions.codehilite", "urlize", "nl2br"]
    )
    return mark_safe(html)


def get_user_channels(user, club=None):
    from .models import Channel

    if club:
        channels = Channel.objects.filter(club=club)
    else:
        channels = Channel.objects.filter(club=None)

    if not user.is_staff and not user.is_superuser:
        channels = channels.filter(
            Q(private=False) | Q(members=user) | Q(group_members__members=user)
        ).distinct()

    return channels
