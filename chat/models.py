from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from django.db import models

from clubs.models import Club
from students.models import Student
from users.models import User

CHANNEL_TYPES = getattr(
    settings,
    "CHAT_CHANNEL_TYPES",
    (("CHANNEL", "Channel"), ("DIRECT_MESSAGE", "Direct message")),
)

CHANNEL_TYPES_PLURAL = getattr(settings, "CHAT_CHANNEL_TYPES_PLURAL", {})


DEFAULT_TYPE = getattr(settings, "CHAT_DEFAULT_TYPE", "CHANNEL")


class MemberGroup(models.Model):
    club = models.ForeignKey(Club, null=True, blank=True, on_delete=models.CASCADE)
    slug = models.SlugField()
    name = models.CharField(max_length=64)
    members = models.ManyToManyField(User, related_name="chat_groups")

    class Meta:
        unique_together = ("slug", "club")


class Channel(models.Model):
    name = models.SlugField()
    type = models.CharField(max_length=32, choices=CHANNEL_TYPES, default=DEFAULT_TYPE)
    club = models.ForeignKey(Club, null=True, blank=True, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    private = models.BooleanField(default=False)
    members = models.ManyToManyField(User, related_name="chat_channels")
    group_members = models.ManyToManyField(MemberGroup, related_name="chat_groups")
    archived = models.BooleanField(default=False)
    metadata = JSONField(default=dict)

    class Meta:
        unique_together = ["name", "club"]

    @property
    def internal_name(self):
        if self.club:
            return f"{self.club.club_code.lower()}-{self.name}"
        return self.name

    @property
    def label(self):
        if self.metadata.get("student_id"):
            return Student.objects.get(pk=self.metadata["student_id"]).name
        return self.name

    def get_type_display_plural(self):
        return CHANNEL_TYPES_PLURAL.get(self.type, self.get_type_display() + "s")

    @property
    def verbose_name(self):
        if not self.private and not self.direct:
            return "#" + self.name
        return self.name

    def __str__(self):
        return self.name


class Message(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="chat_messages"
    )
    channel = models.ForeignKey(
        Channel, on_delete=models.PROTECT, related_name="messages"
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    data = JSONField()
