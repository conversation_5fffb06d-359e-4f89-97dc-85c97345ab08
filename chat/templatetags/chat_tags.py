from django.template import Library

from chat.utils import get_client_settings
from clubs.models import Club

register = Library()


@register.inclusion_tag("chat/includes/chat_overlay.html", takes_context=True)
def chat_overlay(context, channel_name=None, parent=None):
    request = context["request"]
    club = context.get("club")

    if club is None and request.session.get("club_id"):
        club = Club.objects.get(request.session["club_id"])

    if club is None or not club.enable_chat:
        return context

    context["enabled"] = True
    context["client_settings"] = get_client_settings(request, club)
    context["channel_name"] = channel_name
    context["parent"] = parent
    return context
