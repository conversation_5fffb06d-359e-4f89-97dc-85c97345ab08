import json
import uuid

from django.core.cache import cache
from django.utils import timezone


def get_user_status(user):
    data = cache.get(f"user-status-{user.id}")
    if not data:
        return {}
    return json.loads(data)


def save_user_status(user, data):
    if not data:
        data = {}
    cache.set(f"user-status-{user.id}", json.dumps(data))


def update_user_status(user, **data):
    status_data = get_user_status(user)
    status_data.update(data)
    save_user_status(user, data)


def add_connection(user, extra_data=None):
    data = extra_data or {}
    id = str(uuid.uuid4())
    status_data = get_user_status(user)
    connections = status_data.get("connections") or {}
    data["time"] = timezone.now().isoformat()
    connections[id] = data
    update_user_status(user, connections=connections)
    return id


def remove_connection(user, id):
    status_data = get_user_status(user)
    connections = status_data.get("connections") or {}
    if id in connections:
        del connections[id]
    update_user_status(user, connections=connections)
