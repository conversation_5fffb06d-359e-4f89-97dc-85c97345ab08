import json

from django import http
from django.conf import settings
from django.contrib.auth.views import login_required
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse

from clubs.models import Club
from users.models import User

from . import models, utils


def club_required(view):
    """
    Requires a club to be selected (for student portal). If club has not been selected,
    student is redirected to the home page where they can select a club.
    """

    def wrapper(request, club_code=None, **kwargs):
        if settings.SITE == "student" and not request.session.get("club_id"):
            return redirect(reverse("student_portal:home"))
        return view(request, club_code=club_code, **kwargs)

    return wrapper


def get_club(request, club_code=None):
    if club_code is not None:
        return get_object_or_404(Club, club_code=club_code)
    if request.session.get("club_id"):
        return get_object_or_404(Club, pk=request.session["club_id"])
    return None


@login_required
@club_required
def main(request, club_code=None):
    club = get_club(request, club_code)

    channels = utils.get_user_channels(request.user, club)

    if settings.SITE == "student":
        base_tpl = "student_portal/base.html"
    else:
        base_tpl = "dashboard/base.html"

    return render(
        request,
        "chat/main.html",
        {
            "chat_main": True,
            "client_settings": utils.get_client_settings(request, club, club_code),
            "channels": channels,
            "club": club,
            "club_code": club.club_code,
            "base_template": base_tpl,
        },
    )


@login_required
def history(request, channel_name, club_code=None):
    club = get_club(request, club_code)
    channel = get_object_or_404(models.Channel, name=channel_name, club=club)
    after = request.GET.get("after", None)
    before = request.GET.get("before", None)
    limit = request.GET.get("limit", 100)

    if after and before:
        return http.HttpResponseBadRequest(
            'You may specify only one of "after" or "before", not both.'
        )

    messages = channel.messages.select_related("user")
    if after:
        messages = messages.filter(pk__gt=after)[:limit]
    elif before:
        messages = messages.filter(pk__lt=before)[:limit]
    else:
        messages = messages.order_by("-id")[:limit:-1]

    response = {"messages": []}

    for message in messages:
        if not message.data.get("message"):
            continue

        response["messages"].append(
            {
                "messageId": message.id,
                "userId": message.user_id,
                "message": utils.render_markdown(message.data["message"]),
                "time": message.timestamp.isoformat(),
            }
        )

    return http.HttpResponse(
        json.dumps(response, indent=2), content_type="application/json"
    )


@login_required
def users(request, club_code=None):
    response = {"users": {}}
    club = get_club(request, club_code)
    users = utils.get_club_users(club)

    for user in users:
        response["users"][user.id] = utils.get_user_info(user)

    return http.HttpResponse(
        json.dumps(response, indent=2), content_type="application/json"
    )
