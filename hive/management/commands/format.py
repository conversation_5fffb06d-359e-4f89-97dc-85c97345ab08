import logging
import subprocess

from django.core.management.base import BaseCommand


log = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Formats the codebase using black and isort"

    def handle(self, *args, **options):
        self.stdout.write("Formatting codebase...")

        files = (
            subprocess.check_output(["git", "ls-files"]).decode("utf-8").splitlines()
        )
        python_files = [f for f in files if f.endswith(".py") and "migrations" not in f]

        try:
            subprocess.run(["isort", *python_files], check=True)
            self.stdout.write(self.style.SUCCESS("Successfully ran isort"))
        except subprocess.CalledProcessError as e:
            self.stderr.write(self.style.ERROR(f"isort failed: {e}"))
            return

        try:
            subprocess.run(["black", *python_files], check=True)
            self.stdout.write(self.style.SUCCESS("Successfully ran black"))
        except subprocess.CalledProcessError as e:
            self.stderr.write(self.style.ERROR(f"black failed: {e}"))
            return

        self.stdout.write(self.style.SUCCESS("Codebase formatted successfully."))
