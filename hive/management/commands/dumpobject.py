import json
import sys
from itertools import chain

from django.apps import apps
from django.contrib.admin.utils import NestedObjects
from django.core import serializers
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("app", metavar="app_label")
        parser.add_argument("model", metavar="model_name")
        parser.add_argument("pk", nargs="+", type=int)
        parser.add_argument("--indent", dest="indent", type=int)
        parser.add_argument("--no-pk", dest="no_pk", action="store_true")

    def handle(self, *args, **options):
        Model = apps.get_model(options["app"], options["model"])
        pks = options["pk"]
        collector = NestedObjects(using="default")
        collector.collect([Model.objects.get(pk__in=pks)])

        objects = list(chain.from_iterable(collector.data.values()))

        data = json.loads(
            serializers.serialize(
                "json", objects, use_natural_foreign_keys=options["no_pk"]
            )
        )

        if options["no_pk"]:
            # FIXME: natural foreign keys are not serialized as arrays, but are expected to be
            # arrays in loaddata
            for obj in data:
                del obj["pk"]

        if options["indent"]:
            out = json.dumps(data, indent=options["indent"])

        sys.stdout.write(out)
