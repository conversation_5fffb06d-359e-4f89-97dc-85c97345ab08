import asyncio
import logging
from functools import partial, wraps

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from django.conf import settings
from django.core.management import call_command
from django.core.management.base import BaseCommand
from django.db import close_old_connections, connection
from django.utils.module_loading import import_string

log = logging.getLogger(__name__)


def ensure_connection(func):
    """
    Wrapper for jobs so that db connection isn't left open
    """

    @wraps(func)
    def inner(*args, **kwargs):
        log.debug("Ensuring db connection")
        connection.ensure_connection()
        try:
            result = func(*args, **kwargs)
        finally:
            log.debug("Closing old db connections")
            close_old_connections()
        return result

    return inner


class Command(BaseCommand):
    def __init__(self, *args, **kwargs):
        self.loop = asyncio.get_event_loop()
        super().__init__(*args, **kwargs)

    def get_scheduler(self):
        scheduler = AsyncIOScheduler()

        for job in settings.JOBS:
            job = job.copy()
            if "manage" in job:
                command = job.pop("manage")
                job["name"] = job.get("name", f"manage:{command}")
                func = partial(call_command, command)
            elif "func" in job:
                func = job.pop("func")
                if isinstance(func, str):
                    func = import_string(func)
            else:
                raise ValueError("jobs must define either 'manage' or 'func'")

            if "cron" in job:
                trigger = "cron"
                trigger_opts = job.pop("cron")
            elif "interval" in job:
                trigger = "interval"
                trigger_opts = job.pop("interval")
            else:
                raise ValueError("jobs must defined 'cron' or 'interval'")

            func = ensure_connection(func)
            scheduler.add_job(func, trigger, **trigger_opts, **job)

        return scheduler

    def handle(self, **options):
        scheduler = self.get_scheduler()
        log.info("Starting job scheduler")
        scheduler.start()

        try:
            self.loop.run_forever()
        except (KeyboardInterrupt, SystemExit):
            pass
