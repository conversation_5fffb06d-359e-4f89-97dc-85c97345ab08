import logging
import os

from django.conf import settings
from django.contrib.staticfiles.management.commands.collectstatic import (
    Command as BaseCommand,
)
from django.template.loader import get_template

log = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Extends collectstatic command to also build static error pages for nginx
    """

    def handle(self, *args, **options):
        super().handle(*args, **options)

        error_pages_root = os.path.join(settings.STATIC_ROOT, "error_pages")
        if not os.path.exists(error_pages_root):
            os.mkdir(error_pages_root)

        # Render error pages and output them to STATIC_ROOT
        templates = [
            "hive/error_pages/maintenance.html",
            "hive/error_pages/not_found.html",
            "hive/error_pages/internal_error.html",
        ]
        for filename in templates:
            output_filename = os.path.join(error_pages_root, os.path.basename(filename))
            log.info("Building error page: %s", output_filename)

            template = get_template(filename)
            output = template.render({})

            with open(output_filename, "w") as file:
                file.write(output)
