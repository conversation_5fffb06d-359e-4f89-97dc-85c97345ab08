import os

from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.core.management.base import CommandError

# from django_extensions.management.commands.runserver_plus import Command as BaseCommand
from sslserver.management.commands.runsslserver import Command as BaseCommand

from hive import localcerts


class Command(BaseCommand):
    help = (
        "Starts an SSL server for development. Note: you must first generate ssl keys"
    )

    def handle(self, *args, **options):
        ssl_crt_file = os.path.join(
            settings.LOCALDEV_SSL_DIR, f"{settings.BASE_DOMAIN}.crt"
        )
        ssl_key_file = os.path.join(
            settings.LOCALDEV_SSL_DIR, f"{settings.BASE_DOMAIN}.key"
        )

        # Make sure SSL certs are generated
        if not os.path.exists(ssl_crt_file) or not os.path.exists(ssl_key_file):
            try:
                localcerts.generate_local_ssl_certs()
            except ImproperlyConfigured as error:
                raise CommandError(error)

        if options.get("addrport"):
            raise CommandError(
                "address/port cannot be specified when using `run_ssl_server`. Instead, configure "
                "BASE_DOMAIN, *_DOMAIN, and *_PORT settings as explained in README.md"
            )

        options["certificate"] = ssl_crt_file
        options["key"] = ssl_key_file
        options["addrport"] = f"{settings.DOMAIN}:{settings.DEV_PORT}"

        super().handle(*args, **options)
