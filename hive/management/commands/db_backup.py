import datetime
import glob
import logging
import os
import shlex
import shutil
import subprocess
from os.path import relpath

from django.conf import settings
from django.core.management.base import BaseCommand, CommandError

log = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Backs up the database using a Grandfather-Father-Son backup scheme"  # noqa

    def add_arguments(self, parser):
        if settings.DEBUG:
            parser.add_argument("--fake-date", type=str)

    def rotate(self, rotation, date, db_dir):
        fdate = date.strftime("%Y-%m-%d")
        file = glob.glob(os.path.join(db_dir, "daily", f"*{fdate}*.gz"))

        if not file:
            return

        destination = os.path.join(db_dir, rotation)
        os.makedirs(destination, exist_ok=True)

        log.info(
            "Performing %s backup rotation: %s -> %s",
            rotation,
            relpath(file[0], self.base_dir),
            relpath(destination, self.base_dir),
        )
        shutil.copy(file[0], destination)

    def prune(self, prune_dir, keep):
        if not os.path.exists(prune_dir):
            return
        files = sorted(os.listdir(prune_dir), reverse=True)
        for file in files[keep:]:
            prune_path = os.path.join(prune_dir, file)
            if os.path.exists(prune_path):
                log.info(
                    "Pruning backup file: %s", prune_path.replace(self.base_dir, "")
                )
                os.remove(prune_path)

    def handle(self, **options):
        config = getattr(settings, "DB_BACKUP", None)
        if config is None:
            log.warning(
                "Database backups are disabled. `DB_BACKUP` setting is not defined."
            )
            return

        db_host = settings.DATABASES["default"]["HOST"]
        db_name = settings.DATABASES["default"]["NAME"]
        db_user = settings.DATABASES["default"]["USER"]
        db_password = settings.DATABASES["default"]["PASSWORD"]
        db_options = settings.DATABASES["default"].get("OPTIONS", {})

        self.base_dir = config.get("base_dir")
        if self.base_dir is None:
            raise CommandError("DB_BACKUP.base_dir is not defined.")

        keep_daily = int(config.get("keep_daily", 7))
        keep_weekly = int(config.get("keep_weekly", 5))
        keep_monthly = int(config.get("keep_monthly", 12))
        weekly_rotate_weekday = config.get("weekly_rotate_weekday", "0")
        monthly_rotate_day = config.get("monthly_rotate_day", "01")
        yearly_rotate_date = config.get("yearly_rotate_date", "01/01")
        dump_args = config.get("dump_args", "")

        db_dir = os.path.join(self.base_dir, db_name)
        os.makedirs(db_dir, exist_ok=True)

        if options.get("fake_date"):
            import time_machine

            fake_time = time_machine.travel(options["fake_date"])
            fake_time.start()

        date = datetime.datetime.now()
        day = date.strftime("%d")
        weekday = date.strftime("%w")
        month = date.strftime("%m")

        if options.get("fake_date"):
            fake_time.stop()

        # Do the daily backup
        file_path = os.path.join(db_dir, "daily", f"{db_name}_{date:%Y-%m-%d}.sql")
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        env = f"PGPASSWORD={shlex.quote(db_password)}"
        if sslmode := db_options.get("sslmode"):
            env += f" PGSSLMODE={sslmode}"

        log.info("Performing backup to %s", relpath(file_path, self.base_dir))
        command = (
            f"{env} pg_dump -w -h {db_host} -U {db_user} -d {db_name} {dump_args} > {file_path} "
            f"&& gzip -f {file_path}"
        )
        try:
            subprocess.run(
                command,
                shell=True,
                check=True,
            )
        except subprocess.CalledProcessError as e:
            message = f"Backup failed."
            self.stdout.write("stdout: %s", e.stdout)
            self.stderr.write("stderr: %s", e.stderr)
            raise CommandError(message) from e

        # Perform rotations
        if weekday == weekly_rotate_weekday:
            self.rotate("weekly", date, db_dir)

        if day == monthly_rotate_day:
            self.rotate("monthly", date, db_dir)

        if f"{month}/{day}" == yearly_rotate_date:
            self.rotate("yearly", date, db_dir)

        self.prune(os.path.join(db_dir, "daily"), keep_daily)
        self.prune(os.path.join(db_dir, "weekly"), keep_weekly)
        self.prune(os.path.join(db_dir, "monthly"), keep_monthly)
