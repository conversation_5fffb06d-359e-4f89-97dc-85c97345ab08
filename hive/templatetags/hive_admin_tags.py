import json as _json

from django import template

# from material.admin.base import Inline
# from material import Layout


register = template.Library()


@register.filter
def json(obj):
    return _json.dumps(obj, indent=2)


@register.filter
def is_list(obj):
    if isinstance(obj, list):
        return True
    return False


# @register.simple_tag
# def inline_fieldset_layout(adminform, inline_admin_formsets):
#    inlines = [Inline(inline) for inline in inline_admin_formsets]
#    return Layout(*inlines)
