from django.conf import settings
from django.template import Library

from hive.utils import ordinal, site_reverse

register = Library()


@register.filter
def subtract(a, b):
    return a - b


@register.simple_tag
def rating_class(percent, high, med):
    """
    Returns a low/med/high class name depending on the given thresholds.
    """
    percent = int(percent or 0)

    if percent >= high:
        return "rating-high"
    elif percent >= med:
        return "rating-med"
    else:
        return "rating-low"


@register.filter(name="type")
def _type(obj):
    return type(obj)


def get(obj, key, default=""):
    if isinstance(obj, dict):
        return obj.get(key, default)
    return getattr(obj, key, default)


register.filter(name="get")(get)
register.simple_tag(name="get")(get)


@register.filter(name="ordinal")
def _ordinal(n):
    return ordinal(n)


@register.simple_tag
def setting(name):
    return getattr(settings, name)


@register.filter
def get_setting(name):
    return getattr(settings, name)


@register.simple_tag
def site_url(site, name, **kwargs):
    return site_reverse(site, name, kwargs=kwargs)
