import re

from django import template
from django.template import Context, Template
from django.utils.safestring import mark_safe
from markdown import markdown
from markdown3_newtab import NewTabExtension

from hive.markdown.autolink import AutoLinkExtension
from hive.markdown.resourcelinks import ResourceLinksExtension

register = template.Library()


def render_markdown(text, inline=False, urlize=True, links_newtab=False, context=None):
    extensions = [
        "markdown.extensions.codehilite",
        "markdown.extensions.attr_list",
        "mdx_truly_sane_lists",
    ]

    if urlize:
        extensions.append(AutoLinkExtension())

    if links_newtab:
        extensions.append(NewTabExtension())

    context = context or {}
    course = context.get("course")
    club = context.get("club")
    if course or club:
        extensions.append(ResourceLinksExtension(course=course, club=club))

    text = Template(text).render(Context(context))
    html = markdown(text, extensions=extensions)

    if inline:
        html = re.sub(r"^<p>(.*?)</p>", r"\1", html)

    return mark_safe(html)


@register.filter(name="markdown")
def render_markdown_filter(text, inline=False):
    return render_markdown(text, inline=inline)


@register.simple_tag(name="markdown", takes_context=True)
def render_markdown_tag(context, text, inline=False, urlize=True, links_newtab=False):
    """
    Use the templatetag when your markdown needs access to context, such as when using
    [[resource:...]]
    """
    return render_markdown(
        text, inline=inline, context=context, urlize=urlize, links_newtab=links_newtab
    )


@register.filter
def disable_links(text):
    return mark_safe(
        re.sub(r"<a.*?>(.*?)</a>", r'<span class="disabled-link">\1</span>', text)
    )
