import base64
import bin<PERSON><PERSON><PERSON>
import datetime
import decimal
import hashlib
import json
import logging
import math
import operator
import os
import re
import time
from functools import reduce
from urllib.parse import urlencode

import requests
import sentry_sdk
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from django.conf import settings
from django.contrib.admin.utils import NestedObjects
from django.contrib.gis.geos import Point
from django.core.exceptions import FieldDoesNotExist
from django.db import models
from django.db.models.constants import LOOKUP_SEP
from django.urls import reverse
from django.utils import timezone
from django.utils.dateformat import DateFormat
from django.utils.encoding import force_text, smart_str
from django.utils.text import capfirst
from django.utils.translation import gettext_lazy as _
from hashids import Hashids
from mailchimp3 import MailChimp
from mailchimp3.mailchimpclient import MailChimpError
from ratelimit import limits
from requests.exceptions import RequestException
from unidecode import unidecode

HASHID_MIN_LENGTH = 8
HASHID_CHARS = "abcdefghijklmnopqrstuvwxyz0123456789"
ONE_DAY_SECONDS = 60 * 60 * 24
ONE_HOUR = 60 * 60


log = logging.getLogger(__name__)


def percent(n, d):
    if d == 0:
        return 0
    return round((n / d) * 100)


def clean_username(username):
    # Normalize the string, eg ñ -> n
    username = unidecode(username)

    # Strip any special characters
    username = re.sub(r"[^A-Za-z0-9_@./=-]", "", username)

    return username


def get_age(date, as_of=None):
    if date is None:
        return None
    if as_of is None:
        as_of = timezone.now().date()
    return (as_of - date) // datetime.timedelta(days=365.2425)


def ordinal(n):
    s = "tsnrhtdd"[(n // 10 % 10 != 1) * (n % 10 < 4) * n % 10 :: 4]
    return "{}{}".format(n, s)


class GeocodeError(Exception):
    def __init__(self, *args, **kwargs):
        self.response = kwargs.pop("response", None)
        if self.response:
            return super().__init__(
                f"Error while calling geocoding api: {self.response.text}"
            )
        super().__init__(*args, **kwargs)


class GeocodeAddressNotFoundError(GeocodeError):
    def __init__(self, response):
        self.response = response
        super.__init__("Address not found.")


# FIXME: Move this to a celery task once we implement celery
@limits(calls=2, period=1)
def geocode(**kwargs):
    raise_error = kwargs.pop("raise_error", False)
    params = kwargs
    url = f"https://geocode.maps.co/search?{params}"
    response = requests.get(url, params=params)

    # Slow down geocode requests by adding 1 second per request
    time.sleep(1)

    try:
        response.raise_for_status()
    except RequestException as e:
        if raise_error:
            raise GeocodeError(response=response) from e
        return None

    result = response.json()

    if len(result) == 0:
        if raise_error:
            raise GeocodeAddressNotFoundError(response)
        return None

    location = result[0]
    lat = location["lat"]
    lng = location["lon"]

    return Point(float(lat), float(lng))


def get_age_range_description(**kwargs):
    min_grade = kwargs.get("min_grade")
    max_grade = kwargs.get("max_grade")
    min_age = kwargs.get("min_age")
    max_age = kwargs.get("max_age")
    if min_grade and min_grade == max_grade:
        return _("Grade {}").format(min_grade)
    if min_grade and max_grade:
        return _("Grades {} - {}").format(min_grade, max_grade)
    elif min_grade:
        return _("Grades {} and up").format(min_grade)
    elif max_grade:
        return _("Grades {} and under").format(max_grade)
    elif min_age and min_age == max_age:
        return _("Age {}").format(min_age)
    elif min_age and max_age:
        return _("Ages {} - {}").format(min_age, max_age)
    elif min_age:
        return _("Ages {} and up").format(min_age)
    elif max_age:
        return _("Ages {} and under").format(min_age)
    return ""


def preview_delete(objs, using="default"):
    collector = NestedObjects(using=using)
    collector.collect(objs)

    def format(obj):
        return (capfirst(obj._meta.verbose_name), force_text(obj))

    return collector.nested(format)


def format_sql(query):
    import sqlparse

    if isinstance(query, models.QuerySet):
        query = str(query.query)
    return sqlparse.format(query, reindent=True, keyword_case="upper")


def print_sql(query, nocolor=False):
    """
    Prints a formatted sql string or QuerySet
    """
    sql = format_sql(query)
    if nocolor:
        return sql
    from pygments import formatters, highlight, lexers

    lexer = lexers.get_lexer_by_name("postgresql")
    print(highlight(sql, lexer, formatters.TerminalFormatter()))


def json_defaults(obj):
    if isinstance(obj, (datetime.date, datetime.datetime)):
        return obj.isoformat()
    if isinstance(obj, models.Model):
        return obj.pk
    if isinstance(obj, decimal.Decimal):
        return str(obj)
    raise TypeError("{} is not JSON serializable".format(repr(obj)))


def mc_subscribe(email, silent=False, **data):
    mc = MailChimp(mc_api=settings.MAILCHIMP_API_KEY)
    hash = hashlib.md5(email.lower().encode()).hexdigest()
    list_id = settings.MAILCHIMP_LIST_ID
    subscriber_data = {"email_address": email, "merge_fields": {}}
    if data.get("first_name"):
        subscriber_data["merge_fields"]["FNAME"] = data["first_name"]
    if data.get("last_name"):
        subscriber_data["merge_fields"]["LNAME"] = data["last_name"]

    try:
        member = mc.lists.members.get(list_id=list_id, subscriber_hash=hash)
    except MailChimpError as e:
        error = e.args[0]
        if error.get("status") == 404:
            member = None
        else:
            raise

    if member is None:
        # create member
        subscriber_data["status"] = "subscribed"
        try:
            mc.lists.members.create(list_id, subscriber_data)
        except Exception as err:
            if silent:
                sentry_sdk.capture_exception(err)
            else:
                raise


def lookup_needs_distinct(opts, lookup_path):
    # https://github.com/django/django/commit/244cc401559e924355cf943b6b8e66ccf2f6da3a
    """
    Return True if 'distinct()' should be used to query the given lookup path.
    """
    lookup_fields = lookup_path.split(LOOKUP_SEP)
    # Go through the fields (following all relations) and look for an m2m.
    for field_name in lookup_fields:
        if field_name == "pk":
            field_name = opts.pk.name
        try:
            field = opts.get_field(field_name)
        except FieldDoesNotExist:
            # Ignore query lookups.
            continue
        else:
            if hasattr(field, "get_path_info"):
                # This field is a relation; update opts to follow the relation.
                path_info = field.get_path_info()
                opts = path_info[-1].to_opts
                if any(path.m2m for path in path_info):
                    # This field is a m2m relation so distinct must be called.
                    return True
    return False


def get_search_results(queryset, search_term, search_fields):
    """
    Returns a tuple containing a queryset to implement the search,
    and a boolean indicating if the results may contain duplicates.
    """

    # Apply keyword searches.
    def construct_search(field_name):
        if field_name.startswith("^"):
            return "%s__unaccent__istartswith" % field_name[1:]
        elif field_name.startswith("="):
            return "%s__unaccent__iexact" % field_name[1:]
        elif field_name.startswith("@"):
            return "%s__search" % field_name[1:]
        else:
            return "%s__unaccent__icontains" % field_name

    use_distinct = False
    if search_fields and search_term:
        orm_lookups = [
            construct_search(str(search_field)) for search_field in search_fields
        ]
        for bit in search_term.split():
            or_queries = [models.Q(**{orm_lookup: bit}) for orm_lookup in orm_lookups]
            queryset = queryset.filter(reduce(operator.or_, or_queries))
        if not use_distinct:
            for search_spec in orm_lookups:
                if lookup_needs_distinct(queryset.model._meta, search_spec):
                    use_distinct = True
                    break

    if use_distinct:
        return queryset.distinct()
    return queryset


def short_description(desc):
    def decorator(func):
        func.short_description = desc
        return func

    return decorator


def get_fiscal_year(date=None):
    date = date or timezone.now()
    if date.month > settings.FISCAL_START_MONTH:
        return date.year + 1
    return date.year


def get_AES_cipher(iv):
    backend = default_backend()
    key = binascii.a2b_hex(settings.AES_KEY)
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=backend)
    return cipher


def encrypt_bytes(data):
    if not isinstance(data, bytes):
        raise TypeError("must be bytes, not {}".format(type(data).__name__))
    # pad bytes to nearest multiple of 16
    padded_length = math.ceil(len(data) / 16) * 16
    data = data.ljust(padded_length, b"\0")
    iv = os.urandom(16)
    cipher = get_AES_cipher(iv)
    encryptor = cipher.encryptor()
    ct = encryptor.update(data) + encryptor.finalize()
    return iv + ct


def encrypt(data):
    """
    Returns an AES-encrypted value of the given bytestring (using settings.AES_KEY_FILE),
    encoded to a base85 string.
    """
    # Return base85-encoded string
    encrypted_bytes = encrypt_bytes(data)
    return base64.b85encode(encrypted_bytes).decode("utf8")


def decrypt_bytes(encrypted_bytes):
    iv = encrypted_bytes[:16]
    ct = encrypted_bytes[16:]
    cipher = get_AES_cipher(iv)
    decryptor = cipher.decryptor()
    decrypted = decryptor.update(ct) + decryptor.finalize()
    return decrypted.split(b"\0", 1)[0]


def decrypt(encrypted_data):
    if not isinstance(encrypted_data, str):
        raise TypeError("must be str, not {}".format(type(encrypted_data).__name__))
    if encrypted_data == "":
        raise ValueError("encrypted data is empty")
    data = base64.b85decode(encrypted_data)
    return decrypt_bytes(data)


class Age(models.Func):
    """
    Implements postgres AGE function
    """

    function = "AGE"
    output = models.IntegerField()


class DatePart(models.Func):
    """
    Implements postgres DATE_PART function
    """

    function = "DATE_PART"
    output_field = models.IntegerField()

    def __init__(self, part, field, **extra):
        part = models.Value(part, output_field=models.TextField())
        super().__init__(part, field, **extra)


class RegexpReplace(models.Func):
    """
    Implements postgres REGEXP_REPLACE as a queryset function
    """

    function = "REGEXP_REPLACE"

    def __init__(self, expression, pattern, replacement, **extra):
        if not hasattr(pattern, "resolve_expression"):
            if not isinstance(pattern, str):
                raise TypeError("'pattern' must be a string")
            pattern = models.Value(pattern)
        if not hasattr(replacement, "resolve_expression"):
            if not isinstance(replacement, str):
                raise TypeError("'replacement' must be a string")
            replacement = models.Value(replacement)
        expressions = [expression, pattern, replacement]
        super().__init__(*expressions, **extra)


class InvalidGitRepository(Exception):
    pass


def fetch_git_sha(head=None, path=None):
    """
    Returns a git hash for the current version of Hive
    """
    if not path:
        path = settings.BASE_DIR

    if not head:
        head_path = os.path.join(path, ".git", "HEAD")
        if not os.path.exists(head_path):
            raise InvalidGitRepository(
                "Cannot identify HEAD for git repository at %s" % (path,)
            )

        with open(head_path, "r") as fp:
            head = str(fp.read().strip())

        if head.startswith("ref: "):
            head = head[5:]
            revision_file = os.path.join(path, ".git", *head.split("/"))
        else:
            return head
    else:
        revision_file = os.path.join(path, ".git", "refs", "heads", head)

    if not os.path.exists(revision_file):
        if not os.path.exists(os.path.join(path, ".git")):
            raise InvalidGitRepository(
                "%s does not seem to be the root of a git repository" % (path,)
            )

        # Check for our .git/packed-refs' file since a `git gc` may have run
        # https://git-scm.com/book/en/v2/Git-Internals-Maintenance-and-Data-Recovery
        packed_file = os.path.join(path, ".git", "packed-refs")
        if os.path.exists(packed_file):
            with open(packed_file) as fh:
                for line in fh:
                    line = line.rstrip()
                    if line and line[:1] not in ("#", "^"):
                        try:
                            revision, ref = line.split(" ", 1)
                        except ValueError:
                            continue
                        if ref == head:
                            return str(revision)

        raise InvalidGitRepository(
            'Unable to find ref to head "%s" in repository' % (head,)
        )

    with open(revision_file) as fh:
        return str(fh.read()).strip()


def hashid(num, salt, min_length=HASHID_MIN_LENGTH, chars=HASHID_CHARS):
    """
    Create a hashid for the given number. A salt must be provided to represent the "type" of id you
    are hashing. If hashing a model id, use `model_hashid(instance)` instead.
    """
    final_salt = settings.SECRET_KEY
    if salt is not None:
        final_salt = salt + " " + settings.SECRET_KEY
    return Hashids(salt=final_salt, alphabet=chars, min_length=min_length).encode(num)


def decode_hashid(id, salt, min_length=HASHID_MIN_LENGTH, chars=HASHID_CHARS):
    final_salt = settings.SECRET_KEY
    if salt is not None:
        final_salt = salt + " " + settings.SECRET_KEY
    ids = Hashids(salt=final_salt, alphabet=chars, min_length=min_length).decode(id)
    if len(ids):
        return ids[0]
    raise ValueError(f"Could not decode hashid: {id}")


def model_hashid(obj, min_length=HASHID_MIN_LENGTH, chars=HASHID_CHARS):
    """
    Returns a hashid for the given model instance, using "app.Model" as the salt.
    """
    salt = f"{obj._meta.app_label}.{obj._meta.model_name}"
    return hashid(obj.id, salt=salt, min_length=min_length, chars=chars)


def decode_model_hashid(
    model, hashid, min_length=HASHID_MIN_LENGTH, chars=HASHID_CHARS
):
    salt = f"{model._meta.app_label}.{model._meta.model_name}"
    return decode_hashid(hashid, salt=salt, min_length=min_length, chars=chars)


class HashidQuerySet(models.QuerySet):
    def get_by_hashid(self, hashid):
        id = decode_model_hashid(self.model, hashid)
        return self.get(id=id)


class HashidModel(models.Model):
    objects = HashidQuerySet.as_manager()

    @property
    def hashid(self):
        return model_hashid(self)

    class Meta:
        abstract = True


def site_reverse(site, name, kwargs=None):
    scheme = settings.SCHEME
    domain = settings.DOMAINS[site]
    if settings.ENVIRONMENT == "dev":
        port = settings.DEV_PORTS[site]
        domain = f"{domain}:{port}"
    urlconf = f"hive.site_urlconfs.{site}"
    path = reverse(name, urlconf=urlconf, kwargs=kwargs)
    return f"{scheme}://{domain}{path}"


def dict_find(obj, path):
    keys = path.split(".")
    rv = obj
    for key in keys:
        if rv is None:
            return None
        rv = rv.get(key)
    return rv


def format_date(value, format_str):
    """
    Same as `django.utils.dateformat.format()`, but ensures datetime values are localized.
    """
    if isinstance(value, datetime.datetime):
        value = timezone.localtime(value)
    df = DateFormat(value)
    return df.format(format_str)
