import csv
from collections import OrderedDict
from datetime import datetime

from dateutil.relativedelta import relativedelta
from django import http
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON>, IntegerField, OuterRef, Subquery, Value
from django.shortcuts import render
from django.utils import timezone

from clubs import utils
from hive.admin import register_appview


@register_appview("hive", "tshirts/$", "tshirts", "T-shirt sizes", "T-shirts")
def tshirts_view(request, *args, **kwargs):
    from clubs.models import Student
    from students.constants import SHIRT_SIZE_CHOICES as student_shirt_sizes
    from volunteers.constants import SHIRT_SIZE_CHOICES as volunteer_shirt_sizes
    from volunteers.models import Volunteer

    request.current_app = "hive"

    periods = list(utils.get_periods_lookup())

    if request.GET.get("period"):
        year, period = request.GET["period"].split("-")
    else:
        year, period = periods[0][0].split("-")

    # for sorting sizes
    sizes = OrderedDict(student_shirt_sizes)
    sizes.update(volunteer_shirt_sizes)
    sizes = list(sizes.keys())

    students = (
        Student.objects.filter(pk__in=Student.objects.for_period(year, period))
        .values("shirt_size")
        .annotate(total=Count("shirt_size"))
        .order_by()
    )
    volunteers = (
        Volunteer.objects.filter(pk__in=Volunteer.objects.for_period(year, period))
        .values("shirt_size")
        .annotate(total=Count("shirt_size"))
        .order_by()
    )

    student_counts = dict((s["shirt_size"], s["total"]) for s in students)
    volunteer_counts = dict((v["shirt_size"], v["total"]) for v in volunteers)

    counts = []
    for size in sizes:
        if size == "":
            continue
        counts.append(
            {
                "size": size,
                "students": student_counts.get(size, 0),
                "volunteers": volunteer_counts.get(size, 0),
                "total": student_counts.get(size, 0) + volunteer_counts.get(size, 0),
            }
        )

    totals = {
        "students": sum(c["students"] for c in counts),
        "volunteers": sum(c["volunteers"] for c in counts),
        "total": sum(c["total"] for c in counts),
    }

    context = {
        "periods": periods,
        "period_description": dict(periods).get(f"{year}-{period}"),
        "period_code": f"{year}-{period}",
        "year": year,
        "period": period,
        "counts": counts,
        "totals": totals,
    }

    if request.GET.get("csv"):
        response = http.HttpResponse(content_type="text/csv")
        filename = "tshirt-order-{:%Y-%m-%d}.csv".format(timezone.now())
        response["Content-Disposition"] = "attachment; filename=" + filename
        csv_writer = csv.writer(response)
        csv_writer.writerow(["size", "students", "volunteers", "total"])
        for row in counts:
            csv_writer.writerow(
                [row["size"], row["students"], row["volunteers"], row["total"]]
            )
        return response

    return render(request, "admin/tshirts.html", context)


@register_appview("hive", "tshirts/download/$", "tshirts-download", exclude_menu=True)
def tshirts_csv_view(request, *args, **kwargs):
    from clubs.models import Club, Location, Student
    from volunteers.models import Volunteer, VolunteerAssignment

    request.current_app = "hive"

    periods = utils.get_periods_lookup()

    if request.GET.get("period"):
        year, period = request.GET["period"].split("-")
    else:
        year, period = periods[0]

    clubs = Club.objects.for_period(year, period)
    assignments = VolunteerAssignment.objects.for_period(year, period)
    students = Student.objects.for_period(year, period)
    volunteers = Volunteer.objects.for_period(year, period)

    student_location_query = (clubs.filter(students=OuterRef("pk")).order_by()).values(
        "location_id"
    )
    volunteer_location_query = (
        clubs.filter(volunteer_assignments__volunteer=OuterRef("pk")).order_by()
    ).values("location_id")
    volunteer_role_query = (
        assignments.filter(volunteer=OuterRef("pk")).order_by()
    ).values("role")
    volunteer_num_locations_query = volunteer_location_query.annotate(
        num_locations=Count("location_id", distinct=True)
    ).values("num_locations")

    students = students.annotate(
        type=Value("student", output_field=CharField(max_length=16)),
        location=Subquery(student_location_query[:1]),
        role=Value("", output_field=CharField(max_length=32)),
        num_locations=Value(1, output_field=IntegerField()),
    ).values(
        "first_name",
        "last_name",
        "type",
        "shirt_size",
        "location",
        "role",
        "num_locations",
    )
    volunteers = volunteers.annotate(
        type=Value("volunteer", output_field=CharField(max_length=16)),
        location=Subquery(volunteer_location_query[:1]),
        role=Subquery(volunteer_role_query[:1]),
        num_locations=Subquery(volunteer_num_locations_query),
    ).values(
        "first_name",
        "last_name",
        "type",
        "shirt_size",
        "location",
        "role",
        "num_locations",
    )

    # Create map of locations
    locations = {}
    for location in Location.objects.all():
        locations[location.pk] = location

    rows = students.union(volunteers).order_by("location", "shirt_size", "first_name")

    response = http.HttpResponse(content_type="text/csv")
    filename = "tshirts-{}-{}.csv".format(year, period.lower())
    response["Content-Disposition"] = "attachment; filename=" + filename
    csv_writer = csv.writer(response)
    csv_writer.writerow(["name", "type", "size", "location"])

    for row in rows:
        name = row["first_name"] + " " + row["last_name"]
        location = locations.get(row["location"])
        if location:
            location_name = location.short_name
        else:
            location_name = "Unknown"

        if row["role"] == "FLOATER":
            row["type"] == "Floater"
            # Separate floaters who float at multiple locations
            if row["num_locations"] > 1:
                location_name = "Multiple locations"

        csv_writer.writerow(
            [name, row["type"], row["shirt_size"], location_name, row["role"]]
        )

    return response


@register_appview(
    "hive",
    "tshirts/estimator/$",
    "tshirts-estimator",
    "T-shirt order estimator",
    "T-shirts",
)
def tshirts_estimator_view(request):
    from clubs.constants import SHIRT_SIZE_CHOICES
    from clubs.models import Club, Registration
    from volunteers.models import Volunteer

    shirt_size_choices = [s[0] for s in SHIRT_SIZE_CHOICES]

    periods = list(utils.get_periods_lookup())

    if request.GET.get("period"):
        year, period = request.GET["period"].split("-")
    else:
        year, period = periods[0][0].split("-")

    all_clubs = Club.objects.for_period(year, period)
    selected_clubs = all_clubs

    club_ids = [int(id) for id in request.GET.getlist("clubs")]
    if len(club_ids) > 0:
        selected_clubs = selected_clubs.filter(id__in=club_ids)

    # gather stats on all student shirt sizes by age at time of registration
    student_sizes_by_age = {}
    for reg in Registration.objects.all():
        if not reg.form_data:
            continue
        reg_students = reg.form_data.get("students", [])
        for reg_student in reg_students:
            if not reg_student.get("shirt_size"):
                continue
            birth_date = datetime.strptime(reg_student["birth_date"], "%Y-%m-%d")
            age = relativedelta(reg.date.date(), birth_date.date()).years
            if not student_sizes_by_age.get(age):
                student_sizes_by_age[age] = []
            student_sizes_by_age[age].append(reg_student["shirt_size"])

    student_size_counts = {size: 0 for size in shirt_size_choices}

    # get estimated counts by club
    total_volunteers = 0
    for club in selected_clubs:
        if club.min_age:
            min_age = club.min_age
            max_age = club.max_age
        elif club.min_grade:
            # age is roughly grade + 5
            min_age = club.min_grade + 5
            max_age = club.max_grade + 5
        total_volunteers += club.num_volunteers_needed

        # gather size stats for students in the given age range
        student_size_stats = {size: 0 for size in shirt_size_choices}
        for age in range(min_age, max_age + 1):
            for size in student_sizes_by_age.get(age, []):
                student_size_stats[size] += 1

        student_size_stats_sum = sum(student_size_stats.values())
        student_size_factors = {
            size: student_size_stats[size] / student_size_stats_sum
            for size in shirt_size_choices
        }

        # estimate counts for students in this club
        for size, factor in student_size_factors.items():
            student_size_counts[size] += club.num_student_seats * factor

    # gather stats on volunteer sizes
    volunteer_size_stats = {size: 0 for size in shirt_size_choices}
    for volunteer in Volunteer.objects.all():
        if volunteer.shirt_size:
            volunteer_size_stats[volunteer.shirt_size] += 1
    volunteer_size_stats_sum = sum(volunteer_size_stats.values())
    volunteer_size_factors = {
        size: volunteer_size_stats[size] / volunteer_size_stats_sum
        for size in shirt_size_choices
    }

    # estimate counts for all volunteers
    volunteer_size_counts = {size: 0 for size in shirt_size_choices}
    for size, factor in volunteer_size_factors.items():
        volunteer_size_counts[size] += total_volunteers * factor

    # round numbers in both sets
    student_size_counts = {s: round(n) for s, n in student_size_counts.items()}
    volunteer_size_counts = {s: round(n) for s, n in volunteer_size_counts.items()}

    # compile rows for table
    counts = []
    for size in shirt_size_choices:
        counts.append(
            {
                "size": size,
                "students": student_size_counts[size],
                "volunteers": volunteer_size_counts[size],
                "total": student_size_counts[size] + volunteer_size_counts[size],
            }
        )
    totals = {
        "students": sum(c["students"] for c in counts),
        "volunteers": sum(c["volunteers"] for c in counts),
        "total": sum(c["total"] for c in counts),
    }

    context = {
        "periods": periods,
        "period_code": f"{year}-{period}",
        "all_clubs": all_clubs,
        "selected_clubs": selected_clubs,
        "year": year,
        "period": period,
        "year_period": f"{year}-{period}",
        "counts": counts,
        "totals": totals,
    }

    return render(request, "admin/tshirts_estimator.html", context)
