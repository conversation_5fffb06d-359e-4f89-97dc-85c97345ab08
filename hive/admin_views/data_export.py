import os
import subprocess
import tempfile
import zipfile

from django import http
from django.conf import settings
from django.shortcuts import render
from django.utils import timezone

from hive.admin import register_appview


@register_appview(
    "hive", "data-export/$", "data-export", "Data export tool", "Data management"
)
def data_export_view(request, *args, **kwargs):
    # This is currently just a quick solution to allow staff to dump data to send to CNM
    # (our current impact measurement partner)
    #
    # If/when we switch to SMU as a measurement partner, we will likely need more customized
    # CSV output that doesn't require the partner to do a lot of relational mapping (ie, we
    # won't just dump tables, we will need to do the queries/etc on our end to make things
    # easier on their end).

    tables = [
        "students_student",
        "clubs_club",
        "clubs_session",
        "clubs_location",
        "clubs_studentregistration",
        "clubs_studentattendance",
        "clubs_volunteerattendance",
        "volunteers_volunteer",
        "volunteers_volunteersignup",
        "volunteers_volunteeropportunity",
        "volunteers_volunteerassignment",
        "volunteers_organization",
        "contacts_contact",
    ]

    if request.method == "POST":
        selected_tables = request.POST.getlist("selected_tables")
        dbconfig = settings.DATABASES["default"]
        env = os.environ.copy()
        env["PGPASSWORD"] = dbconfig["PASSWORD"]
        with tempfile.TemporaryDirectory() as tmpdir:
            files = []
            for table in selected_tables:
                # file_name = f'{table}.csv'
                file_name = "hive-" + table.split("_")[1] + ".csv"
                file_path = f"{tmpdir}/{file_name}"
                cmd = [
                    "psql",
                    "-h",
                    dbconfig["HOST"],
                    "-U",
                    dbconfig["USER"],
                    "-w",
                    "-c",
                    f"\\copy {table} TO '{file_path}' WITH CSV HEADER",
                ]
                try:
                    subprocess.run(
                        cmd,
                        check=True,
                        env=env,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                    )
                except subprocess.CalledProcessError as e:
                    raise RuntimeError(f"Error executing psql: {e.stderr.decode()}")

                files.append((file_name, file_path))

            with tempfile.NamedTemporaryFile(suffix=".zip") as zip_file:
                with zipfile.ZipFile(zip_file, mode="w") as archive:
                    for file_name, file_path in files:
                        archive.write(file_path, file_name)

                now = timezone.now()
                return http.FileResponse(
                    open(zip_file.name, "rb"),
                    as_attachment=True,
                    filename=f"export-{now:%Y-%m-%d}.zip",
                )

    context = {"tables": tables}

    return render(request, "admin/data_export.html", context)
