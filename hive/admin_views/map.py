from django.conf import settings
from django.shortcuts import render

from clubs.utils import get_periods_lookup
from hive.admin import register_appview


@register_appview("hive", "map/$", "map", "Map view", "Reports")
def map_view(request, *args, **kwargs):
    from clubs.models import Club, Location
    from students.models import Student
    from volunteers.models import Volunteer

    show = request.GET.getlist("show") or ["p", "s", "v"]

    # Get period dropdown options
    period_options = get_periods_lookup()

    if request.GET.get("period"):
        year, period = request.GET["period"].split("-")
    else:
        year, period = period_options[0][0].split("-")

    all_locations = set()
    for club in Club.objects.for_period(year, period):
        if club.location is not None:
            all_locations.add(club.location)

    locations = all_locations
    if request.GET.getlist("locations"):
        locations = Location.objects.filter(pk__in=request.GET.getlist("locations"))

    students = Student.objects.none()
    if "s" in show:
        students = Student.objects.for_period(year, period).exclude(
            account__default_contact__geo=None, postal_code_geo=None
        )

    volunteers = Volunteer.objects.none()
    if "v" in show:
        volunteers = Volunteer.objects.for_period(year, period).exclude(geo=None)
        if locations:
            volunteers = volunteers.filter(assignments__club__location__in=locations)

    clubs = Club.objects.none()
    if "p" in show:
        clubs = Club.objects.for_period(year, period).exclude(location__geo=None)
        if locations:
            clubs = clubs.filter(location__in=locations)

    context = {
        "all_locations": all_locations,
        "locations": locations,
        "period_description": dict(period_options).get("{}-{}".format(year, period)),
        "period_options": period_options,
        "year": year,
        "period": period,
        "year_period": "{}-{}".format(year, period),
        "show": show,
        "clubs": clubs,
        "students": students,
        "volunteers": volunteers,
        "GOOGLE_MAPS_API_KEY": settings.GOOGLE_MAPS_API_KEY,
    }
    return render(request, "admin/map.html", context)
