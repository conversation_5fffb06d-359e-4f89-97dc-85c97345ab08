from django.conf import settings
from django.conf.urls import include, url
from django.conf.urls.static import static

from hive.site_urlconfs import debug_toolbar_urls

urlpatterns = [
    url(r"^", include("student_portal.urls")),
    url(r"^", include("django.contrib.auth.urls")),
    url(r"^ide/", include("ide.urls")),
    url(r"^courses/", include("courses.urls")),
]

if settings.CHAT_ENABLED:
    urlpatterns += [
        url(r"^chat/", include("chat.urls")),
    ]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += debug_toolbar_urls()
