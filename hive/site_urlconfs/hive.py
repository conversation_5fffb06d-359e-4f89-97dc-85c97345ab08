from django.conf import settings
from django.conf.urls import include, url
from django.conf.urls.static import static

from hive.admin import admin_site
from hive.site_urlconfs import debug_toolbar_urls

# TODO: split this up into admin / mentor sites
# hive dashboard
urlpatterns = [
    url(r"^", include("dashboard.urls")),
    url(r"^", include("django.contrib.auth.urls")),
    url(r"^volunteer/", include("volunteers.urls")),
    url(r"^courses/", include("courses.urls")),
    url(r"^ide/", include("ide.urls")),
    url(r"^admin/", admin_site.urls),
    url(r"^martor/", include("martor.urls")),
    # url(r'^slack/', include(slackbot.urls)),
]

if settings.CHAT_ENABLED:
    urlpatterns += [
        url(r"^chat/(?P<club_code>[^/]+)/", include("chat.urls")),
    ]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += debug_toolbar_urls()
