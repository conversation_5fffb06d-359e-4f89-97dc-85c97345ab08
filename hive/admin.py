import itertools
from collections import OrderedDict
from importlib import import_module

from django.apps import apps
from django.contrib import admin
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.decorators.cache import never_cache
from django.views.generic.base import RedirectView
from martor.widgets import AdminMartorWidget


class HiveAdminSite(admin.AdminSite):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Allows other apps to register a sidebar link
        self._appviews = OrderedDict()

    def _build_app_dict(self, request, label=None):
        # Allow override of model name in admin
        app_dict = super()._build_app_dict(request, label)
        _app_dict = app_dict
        if label:
            _app_dict = {label: app_dict}
        for app_module, app in _app_dict.items():
            app_models = import_module(f"{app_module}.models")
            for model_dict in app["models"]:
                model = getattr(app_models, model_dict["object_name"])
                if hasattr(model, "get_admin_verbose_name"):
                    model_dict["name"] = model.get_admin_verbose_name()
        return app_dict

    @never_cache
    def index(self, request, extra_context=None):
        extra_context = extra_context or {}

        extra_context["appviews"] = self._appviews

        return super().index(request, extra_context)

    def register_appview(
        self,
        app_label,
        urlpattern,
        name,
        verbose_name=None,
        category=None,
        exclude_menu=False,
    ):
        """
        Allows an app to register a view link in the sidebar
        """
        verbose_name = verbose_name or name.title()

        def wrapper(viewfunc):
            if not self._appviews.get(category):
                self._appviews[category] = []
            self._appviews[category].append(
                {
                    "urlname": f"admin:{app_label}_{name}",
                    "app_label": app_label,
                    "urlpattern": urlpattern,
                    "viewfunc": viewfunc,
                    "name": name,
                    "verbose_name": verbose_name,
                    "exclude_menu": exclude_menu,
                }
            )

        return wrapper

    def get_urls(self):
        from django.conf.urls import url

        appview_urls = []
        for category, appviews in self._appviews.items():
            for appview in appviews:
                appview_urls.append(
                    url(
                        f'^{appview["app_label"]}/{appview["urlpattern"]}',
                        self.admin_view(appview["viewfunc"]),
                        name=f'{appview["app_label"]}_{appview["name"]}',
                    )
                )

        # Redirect for updated quickbooks oauth url (Currently unable to change this on Intuit's
        # Oauth settings, awaiting questionnaire approval)
        qbauth_url = reverse_lazy("admin:billing_qbauth")
        qbauth_redirect = RedirectView.as_view(url=qbauth_url, query_string=True)

        return (
            super().get_urls()
            + appview_urls
            + [
                url("^qbauth/$", qbauth_redirect, name="qbauth"),
            ]
        )


admin_site = HiveAdminSite()


def register(cls):
    return admin.register(cls, site=admin_site)


def register_appview(*args, **kwargs):
    return admin_site.register_appview(*args, **kwargs)


def archive_action(modeladmin, request, queryset):
    queryset.update(archived=True)


archive_action.short_description = "Archive selected items"


class ArchivedFilter(admin.SimpleListFilter):
    title = "Archived"
    parameter_name = "archived"
    archived_field = "archived"

    def lookups(self, request, model_admin):
        return (("", "Not archived"), ("YES", "Archived"), ("ALL", "Show all"))

    def choices(self, changelist):
        return (
            {
                "selected": self.value() not in ("YES", "ALL"),
                "query_string": changelist.get_query_string(
                    remove=[self.parameter_name]
                ),
                "display": dict(self.lookup_choices)[""],
            },
            {
                "selected": self.value() == "YES",
                "query_string": changelist.get_query_string(
                    {self.parameter_name: "YES"}
                ),
                "display": dict(self.lookup_choices)["YES"],
            },
            {
                "selected": self.value() == "ALL",
                "query_string": changelist.get_query_string(
                    {self.parameter_name: "ALL"}
                ),
                "display": dict(self.lookup_choices)["ALL"],
            },
        )

    def queryset(self, request, queryset):
        value = self.value()
        if value == "YES":
            queryset = queryset.filter(**{self.archived_field: True})
        elif value != "ALL":
            queryset = queryset.exclude(**{self.archived_field: True})
        return queryset


class ListingStatusFilter(admin.SimpleListFilter):
    title = "Listing status"
    parameter_name = "status"
    status_lookup = "status"

    def lookups(self, request, model_admin):
        return (
            ("", "Not archived"),
            ("DRAFT", "Draft"),
            ("PUBLISHED", "Published"),
            ("ARCHIVED", "Archived"),
            ("ALL", "Show all"),
        )

    def choices(self, changelist):
        value = self.value()
        for choice, label in self.lookup_choices:
            selected = choice == "" and value is None or choice == value
            yield {
                "selected": selected,
                "query_string": changelist.get_query_string(
                    {self.parameter_name: choice}
                ),
                "display": label,
            }

    def queryset(self, request, queryset):
        value = self.value()
        if not value:
            queryset = queryset.exclude(**{self.status_lookup: "ARCHIVED"})
        elif value != "ALL":
            queryset = queryset.filter(**{self.status_lookup: value})
        return queryset


class ShowMoreFilterMixin:
    show_initial = 10

    def __init__(self, request, params, *args, **kwargs):
        super().__init__(request, params, *args, **kwargs)
        self._show_all = False
        self._show_all_param = f"{self.parameter_name}__show_all"
        if params.get(self._show_all_param):
            self._show_all = True
            value = params.pop(self._show_all_param)
            self.used_parameters[self._show_all_param] = value

    def expected_parameters(self):
        params = super().expected_parameters()
        params.append(self._show_all_param)
        return params

    def choices(self, changelist):
        yield {
            "selected": self.value() is None,
            "query_string": changelist.get_query_string(remove=[self.parameter_name]),
            "display": _("All"),
        }
        lookup_choices = self.lookup_choices
        if not self._show_all:
            lookup_choices = itertools.islice(lookup_choices, self.show_initial)

        count = 0
        for lookup, title in lookup_choices:
            count += 1
            yield {
                "selected": self.value() == str(lookup),
                "query_string": changelist.get_query_string(
                    {self.parameter_name: lookup}
                ),
                "display": title,
            }

        if len(tuple(self.lookup_choices)) > self.show_initial:
            if not self._show_all:
                yield {
                    "selected": False,
                    "query_string": changelist.get_query_string(
                        {self._show_all_param: "1"}
                    ),
                    "display": _("[show more...]"),
                }
            else:
                yield {
                    "selected": False,
                    "query_string": changelist.get_query_string(
                        remove=[self._show_all_param]
                    ),
                    "display": _("[show fewer...]"),
                }


class ReadOnlyAdminMixin:
    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class AdminMarkdownWidget(AdminMartorWidget):
    @property
    def media(self):
        media = super().media
        # media.all.remove('plugins/css/semantic.min.css')
        # media.all.append('martor-plugins/css/semantic.css')
        return media


# Automatically load admin appview modules
for app_label, app_config in apps.app_configs.items():
    try:
        import_module(f"{app_config.module.__name__}.admin_views")
    except ModuleNotFoundError:
        pass
