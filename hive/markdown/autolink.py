import xml.etree.ElementTree as etree

import markdown
from markdown.inlinepatterns import Pattern

EXTRA_AUTOLINK_RE = r'(?<!"|>)((https?://|www)[-\w./#?%=&]+)'


class AutoLinkPattern(Pattern):

    def handleMatch(self, m):
        el = etree.Element("a")
        if m.group(2).startswith("http"):
            href = m.group(2)
        else:
            href = "http://%s" % m.group(2)
        el.set("href", href)
        el.set("target", "_blank")
        el.text = m.group(2)
        return el


class AutoLinkExtension(markdown.Extension):
    """
    There's already an inline pattern called autolink which handles
    <http://www.google.com> type links. So lets call this extra_autolink
    """

    def extendMarkdown(self, md, md_globals):
        md.inlinePatterns.add(
            "extra_autolink", AutoLinkPattern(EXTRA_AUTOLINK_RE, self), "<automail"
        )


def makeExtension(configs=[]):
    return AutoLinkExtension(configs=configs)
