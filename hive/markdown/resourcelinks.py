"""
ResourceLinks Extension for Python-Markdown
Converts [[resource:slug-name]] to a course resource link.
"""

import xml.etree.ElementTree as etree

from django.utils.http import urlencode
from markdown.extensions import Extension
from markdown.inlinepatterns import InlineProcessor

from courses.utils import get_course_handler


class ResourceLinksExtension(Extension):
    def __init__(self, **kwargs):
        self.config = {
            "html_class": ["resource-link", "CSS hook. Leave blank for none."],
            "course": ["course", "Course object"],
            "club": ["club", "Club object"],
        }

        super().__init__(**kwargs)

    def extendMarkdown(self, md):
        self.md = md

        # append to end of inline patterns
        RESOURCE_LINK_RE = r"\[\[resource:([\w0-9_ -]+)([/#][^|\]]+)?(\|([^\]]+))?\]\]"
        link_pattern = ResourceLinkInlineProcessor(RESOURCE_LINK_RE, self.getConfigs())
        link_pattern.md = md
        # priority 74, prioritizes over wikilinks procesor
        md.inlinePatterns.register(link_pattern, "resourcelink", 74)


class ResourceLinkInlineProcessor(InlineProcessor):
    def __init__(self, pattern, config):
        super().__init__(pattern)
        self.config = config

    def handleMatch(self, m, data):
        if m.group(1).strip():
            html_class = self._getMeta()
            slug = m.group(1).strip()
            extra = m.group(2)
            name = m.group(4)

            course = self.config.get("course")
            course_handler = get_course_handler()
            resource_links = course_handler.get_resource_links(
                course=course, include_lti=True
            )
            resource_links = resource_links.filter(slug=slug)

            if resource_links.count() > 0:
                resource_link = resource_links[0]
            else:
                return f'[[NOT FOUND: "{slug}"]]', m.start(0), m.end(0)

            a = etree.Element("a")
            a.text = name and name.strip() or resource_link.name

            href = resource_link.permalink
            if extra:
                href += "?extra=" + urlencode(extra.strip())

            a.set("href", href)
            a.set("target", "__blank__")
            if html_class:
                a.set("class", " ".join((html_class, slug)))
        else:
            a = ""
        return a, m.start(0), m.end(0)

    def _getMeta(self):
        """Return meta data or config data."""
        html_class = self.config["html_class"]
        if hasattr(self.md, "Meta"):
            if "resource_html_class" in self.md.Meta:
                html_class = self.md.Meta["resource_html_class"][0]
        return html_class
