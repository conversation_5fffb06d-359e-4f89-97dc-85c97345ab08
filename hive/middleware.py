from datetime import <PERSON><PERSON><PERSON>

from django.conf import settings
from django.http.request import DisallowedHost, split_domain_port
from django.utils import timezone
from django.utils.translation import activate

if settings.SHIFT_TIME:
    import freezegun


class ValidateDomainMiddleware(object):
    """
    Checks to see if the request host is consistent with settings.DOMAIN. If not, give a useful
    error message.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        host = request._get_raw_host()
        host_domain = split_domain_port(host)[0]
        valid_domain = split_domain_port(settings.DOMAIN)[0]
        if host_domain != valid_domain:
            raise DisallowedHost(
                f"This site is configured to run on {settings.DOMAIN}, but you are accessing it "
                f"via {host}. Please see README.md (Environment Variables section) for more info."
            )
        response = self.get_response(request)
        return response


class CodioIframeMiddleware(object):
    """
    This allows Codio workspaces to load hive sites within the IDE
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        response["Content-Security-Policy"] = "frame-ancestors 'self' codio.com"

        return response


class SameSiteNoneMiddleware(object):
    """
    Sets sessionid and csrftoken cookies to use samesite=None and secure if using https.

    **Important**: This must appear BEFORE SessionMiddleware in settings.MIDDLEWARE
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        # Note: samesite=None cookies may break older chrome versions. If this becomes a problem,
        # we may need to add UA sniffing here.
        if settings.SCHEME == "https":
            if "sessionid" in response.cookies:
                response.cookies["sessionid"]["samesite"] = "None"
                response.cookies["sessionid"]["secure"] = True

            if "csrftoken" in response.cookies:
                response.cookies["csrftoken"]["samesite"] = "None"
                response.cookies["csrftoken"]["secure"] = True

        return response


class AdminLanguageMiddleware(object):
    """
    Use this middleware to ensure the admin is always running under the
    default language, to prevent vinaigrette from clobbering the registered
    fields with the user's picked language in the change views. Aslo make
    sure that this is after any LocaleMiddleware like classes.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def is_admin_request(self, request):
        """
        Returns True if this request is for the admin views.
        """
        return request.path.startswith("/admin/")

    def __call__(self, request):
        response = self.get_response(request)

        if not self.is_admin_request(request):
            # We are in the admin site
            activate(settings.LANGUAGE_CODE)

        return response


class FreezeTimeMiddleware(object):
    """
    Used for testing to fix the "current" date/time to a configured time for all requests
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if not settings.SHIFT_TIME:
            return self.get_response(request)

        real_date = timezone.now()
        shifted_date = real_date + timedelta(seconds=settings.SHIFT_TIME)
        with freezegun.freeze_time(shifted_date, tick=True):
            return self.get_response(request)


class MartorPreviewFix(object):
    """
    See here: https://github.com/agusmakmun/django-markdown-editor/issues/184
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if "martor" in request.path:
            setattr(request, "_dont_enforce_csrf_checks", True)

        response = self.get_response(request)

        return response
