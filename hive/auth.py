from django.conf import settings
from django.contrib.auth.backends import ModelBackend

from users.utils import is_student, is_volunteer


class MultiSiteAuthBackend(ModelBackend):
    """
    Controls which users can log into which sites. For example, students can log into student
    portal and API, while mentors can log into mentor portal and API.
    """

    def user_can_authenticate(self, user):
        # Students cannot access the volunteer dashboard
        if settings.SITE == "hive":
            return not is_student(user)

        # Only students can log into student portal
        if settings.SITE == "student":
            return is_student(user)

        # Students should not log into registration site (only for parent accounts, for now)
        if settings.SITE == "clubs":
            return not is_student(user)

        return True
