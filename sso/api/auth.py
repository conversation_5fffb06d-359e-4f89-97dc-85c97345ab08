import logging
import re

from django.core.exceptions import PermissionDenied
from rest_framework import authentication, exceptions
from rest_framework.permissions import BasePermission

from sso import utils
from sso.models import AuthToken

logger = logging.getLogger(__name__)


class SSOTokenAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        import json

        logger.debug(
            "HEADERS: "
            + json.dumps(
                {k: v for k, v in request.META.items() if k.startswith("HTTP_")},
                indent=2,
            )
        )
        auth_header = request.META.get("HTTP_AUTHORIZATION")
        if auth_header is None:
            logger.debug("No authorization header")
            return None

        match = re.match(r"AuthToken (\w+)$", auth_header)
        if not match:
            logger.debug("No auth token (no header match)")
            return None

        token = None

        try:
            token_str = match.group(1)
        except IndexError:
            logger.debug("No auth token (empty value)")
            return None

        # TODO Delete any expired auth tokens (need to implement token refresh/expiration first)

        try:
            token = AuthToken.objects.get(token=token_str)
        except AuthToken.DoesNotExist as e:
            logger.debug(str(e))
            raise exceptions.AuthenticationFailed("Invalid token")

        return (token.user, None)


class AuthorizedForResource(BasePermission):
    """
    Use case: a trusted client (server-side) sends X_AUTHORIZE_FOR header
    to verify that a user is authorized for a given resource.

    A hive app can register an authorization function using
    @register_authorization_plugin decorator

    See example in ide.utils.authorize_for_ide
    """

    def has_permission(self, request, view):
        authorize_for = request.META.get("HTTP_X_AUTHORIZE_FOR")
        if authorize_for:
            try:
                utils.authorize_for(authorize_for, request)
            except PermissionDenied:
                logger.info(f'Authorization for "{authorize_for}" failed')
                return False
            logger.info('Authorization for "{authorize_for}" successful')
        return True
