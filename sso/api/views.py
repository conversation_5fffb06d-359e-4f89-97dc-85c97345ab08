import logging
import re

from rest_framework import exceptions
from rest_framework.decorators import api_view
from rest_framework.response import Response

from hive.utils import site_reverse

from .. import errors, models, utils

logger = logging.getLogger(__name__)


@api_view(["GET"])
def request_token(request):
    """
    Retrieves a "request token", which is required in order to use /sso/auth
    """
    auth_header = request.META.get("HTTP_AUTHORIZATION")
    if auth_header is None:
        logger.debug("No authorization header")
        raise exceptions.AuthenticationFailed("Invalid token")

    match = re.match(r"ClientApp (\w+):(\w+)$", auth_header)
    if not match:
        logger.debug("No ClientApp token")
        raise exceptions.AuthenticationFailed("Invalid token")

    try:
        client_id = match.group(1)
        client_secret = match.group(2)
    except IndexError as e:
        logger.debug(f"Invalid token: {e}")
        raise exceptions.AuthenticationFailed("Invalid token")

    try:
        client = utils.get_client(client_id)
    except (models.ClientApp.DoesNotExist, errors.InvalidClientID) as e:
        logger.debug(str(e))
        raise exceptions.AuthenticationFailed("Invalid token")

    if client.secret != client_secret:
        logger.debug("Client secret does not match")
        raise exceptions.AuthenticationFailed("Invalid token")

    request_token = models.ClientRequestToken.objects.create(client=client)

    # This is being accessed through the api site, so to reverse a url for the sso site we need to
    # use `site_reverse`
    auth_url = site_reverse("sso", "sso:auth")

    return Response({"request_token": request_token.token, "auth_url": auth_url})
