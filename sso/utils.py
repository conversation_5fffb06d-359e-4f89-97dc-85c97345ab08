from hive.utils import decode_model_hashid

from .errors import InvalidClientID
from .models import CLIENT_ID_LENGTH, ClientApp

_authorization_plugins = {}


def get_client(client_id):
    try:
        client_pk = decode_model_hashid(
            ClientApp, client_id, min_length=CLIENT_ID_LENGTH
        )
    except ValueError:
        raise InvalidClientID()

    return ClientApp.objects.get(pk=client_pk)


def on_user_logged_out(sender, user, request, **kwargs):
    if user:
        user.auth_tokens.all().delete()


def register_authorization_plugin(name):
    def wrapper(callback):
        _authorization_plugins[name] = callback

    return wrapper


def authorize_for(name, request):
    return _authorization_plugins[name](request)


def get_auth_token(*args, **kwargs):
    # This function is no longer used -- use new ClientApp SSO workflow instead
    raise NotImplementedError
