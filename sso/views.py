import logging
from datetime import timedelta

from django import http
from django.conf import settings
from django.contrib.auth.views import LoginView, login_required
from django.utils import timezone
from furl import furl

from . import forms, models

logger = logging.getLogger(__name__)

REQUEST_TOKEN_EXPIRE_TIME = 5 * 60


@login_required(login_url=settings.LOGIN_URL)
def auth(request):
    request_token_str = request.GET.get("request_token")
    if not request_token_str:
        return http.HttpResponseBadRequest("`request_token` parameter is required")

    next = request.GET.get("next")
    if not next:
        return http.HttpResponseBadRequest("`next` parameter is required")

    # delete any expired client tokens
    exp_date = timezone.now() - timedelta(seconds=REQUEST_TOKEN_EXPIRE_TIME)
    models.ClientRequestToken.objects.filter(created__lte=exp_date).delete()

    # validate provided request token
    try:
        request_token = models.ClientRequestToken.objects.get(token=request_token_str)
    except models.ClientRequestToken.DoesNotExist:
        return http.HttpResponseRedirect(
            furl(next).add({"error": "INVALID_REQUEST_TOKEN"}).url
        )

    client = request_token.client
    token, created = models.AuthToken.objects.get_or_create(
        user=request.user, client=client
    )
    next_url = furl(next).add({"token": token.token}).url

    # request token is no longer needed
    request_token.delete()

    return http.HttpResponseRedirect(next_url)


class SSOLoginView(LoginView):
    template_name = "sso/login.html"
    form_class = forms.SSOAuthenticationForm
