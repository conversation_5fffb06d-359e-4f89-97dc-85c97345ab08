from django.contrib import admin, messages

from hive.admin import register

from . import models


@register(models.ClientApp)
class ClientAppAdmin(admin.ModelAdmin):
    list_display = ["name", "_client_id"]
    fields = ["name", "_client_id"]
    readonly_fields = ["_client_id"]

    def _client_id(self, obj):
        return (obj and obj.pk and obj.client_id) or "-"

    _client_id.short_description = "Client ID"

    def save_model(self, request, obj, form, change):
        creating = obj.pk is None
        obj.save()
        if creating:
            messages.add_message(
                request, messages.INFO, f"The client secret is: {obj.secret}"
            )
