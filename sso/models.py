import secrets

from django.conf import settings
from django.db import models

from hive.utils import model_hashid

CLIENT_ID_LENGTH = 16


class ClientApp(models.Model):
    """
    Represents a client application that makes API calls on behalf of a user.

    **IMPORTANT**: The client secret should never be made publicly available (do not expose it on
    thefront-end, regardless of whether or a the user is authenticated).
    """

    name = models.CharField(max_length=256)
    secret = models.CharField(max_length=64)

    @property
    def client_id(self):
        return model_hashid(self, min_length=CLIENT_ID_LENGTH)

    def save(self, *args, **kwargs):
        if not self.secret:
            self.secret = secrets.token_hex(32)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class TokenModel(models.Model):
    client = models.ForeignKey(ClientApp, on_delete=models.CASCADE)
    token = models.Char<PERSON>ield(max_length=64)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = secrets.token_hex(32)
        super().save(*args, **kwargs)


class ClientRequestToken(TokenModel):
    """
    This token is used by client application to request a user authentication token. A client can
    retrieve a request token by making an GET call to /api/sso/request-token while supplying the
    client_id and client_secret in the headers (see sso.views for more details).
    """

    def __str__(self):
        return f"{self.client.name} - {self.created}"


class AuthToken(TokenModel):
    """
    This token allows the user to make authenticated API calls when the browser session is not
    available.

    **IMPORTANT**: Be very careful about how this is passed around. Never make this token available
    via a cross-origin request (even if sessison-authenticated).

    This token can be retrieved by a client app by redirecting a user to /sso/auth-redirect and
    supplying a valid request token in the querystring (see above).
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="auth_tokens"
    )

    def __str__(self):
        return f"{self.user} - {self.client.name} - {self.created}"
