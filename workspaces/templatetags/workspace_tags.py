import glob
import os
import posixpath
import re
from pathlib import Path

from django import template
from django.conf import settings
from django.templatetags.static import static
from django.utils.safestring import mark_safe

import workspaces

register = template.Library()

STATIC_PATH = ("workspaces", "guacamole", "app")
BASE_DIR = os.path.dirname(workspaces.__file__)
APP_DIR = os.path.join(BASE_DIR, "static", *STATIC_PATH)
JSTEMPLATE = os.path.join(
    BASE_DIR, "templates", "workspaces", "includes", "app-js.html"
)
CSSTEMPLATE = os.path.join(
    BASE_DIR, "templates", "workspaces", "includes", "app-css.html"
)
SRC_PATTERN = re.compile(r'(\s+)src=([\'"])(.*?)[\'"]')

static_path_url = posixpath.join(*STATIC_PATH)


def src_url_converter(matchobj):
    space = matchobj.group(1)
    quote = matchobj.group(2)
    url = matchobj.group(3)
    converted_url = static("workspaces/guacamole/" + url)
    return "%ssrc=%s%s%s" % (space, quote, converted_url, quote)


def process_angular_template(content):
    # escape quotes, newlines, and script tags
    content = content.replace("'", "\\'").replace("\n", "\\n")
    content = content.replace("<script", "\\u003Cscript")
    content = content.replace("</script", "\\u003C/script")

    # fix src="" urls
    content = SRC_PATTERN.sub(src_url_converter, content)

    return content


@register.simple_tag
def workspace_app_css():
    css = ""
    for filepath in glob.glob(os.path.join(APP_DIR, "**", "*.css"), recursive=True):
        filepath = os.path.relpath(filepath, APP_DIR)
        url = posixpath.join(static_path_url, Path(filepath).as_posix())
        css += '  <link rel="stylesheet" type="text/css" href="{}"></script>\n'.format(
            static(url)
        )

    return mark_safe(css)


@register.simple_tag
def workspace_app_js():
    js = ""

    # js files
    for filepath in glob.glob(os.path.join(APP_DIR, "**", "*.js"), recursive=True):
        filepath = os.path.relpath(filepath, APP_DIR)
        url = posixpath.join(static_path_url, Path(filepath).as_posix())
        js += '  <script type="text/javascript" src="{}"></script>\n'.format(
            static(url)
        )

    # angular template cache
    tplfiles = glob.glob(os.path.join(APP_DIR, "**", "*.html"), recursive=True)
    tplrelpaths = [Path(os.path.relpath(p, APP_DIR)).as_posix() for p in tplfiles]
    js += '  <script type="text/javascript">\n'
    path_array_js = "[" + ", ".join("'app/{}'".format(f) for f in tplrelpaths) + "]"
    js += "    angular.module('templates-main', {});\n".format(path_array_js)
    for filepath in tplfiles:
        relfilepath = Path(os.path.relpath(filepath, APP_DIR)).as_posix()
        js += (
            "    "
            "angular.module('app/{}', []).run(['$templateCache', function($templateCache) {{\n"
        ).format(relfilepath)
        with open(os.path.join(filepath), "r") as file:
            content = file.read()
        content = process_angular_template(content)
        js += "      $templateCache.put('app/{}', '{}');\n".format(relfilepath, content)
        js += "    }]);\n"
    js += "  </script>\n"

    return mark_safe(js)


@register.filter
def min(path):
    if path.endswith(".min.js") and settings.DEBUG:
        return re.sub(r".min.js$", ".js", path)
    return path
