from compressor.filters.css_default import CssAbsoluteFilter as BaseCssAbsoluteFilter
from django.templatetags.static import static

prefix = "workspaces/guacamole/"


class CssAbsoluteFilter(BaseCssAbsoluteFilter):
    """
    CSS urls in the guacamole client app are all relative to the guacamole static root.
    """

    def url_converter(self, matchobj):
        quote = matchobj.group(1)
        url = matchobj.group(2)
        if self.path.startswith(prefix):
            url = static(prefix + url)
        converted_url = self._converter(url)
        return "url(%s%s%s)" % (quote, converted_url, quote)
