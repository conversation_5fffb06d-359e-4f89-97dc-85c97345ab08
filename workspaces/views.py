from django import http
from django.conf import settings
from django.contrib.auth.views import login_required
from django.shortcuts import get_object_or_404, render

from students.models import Student
from users.utils import is_student, is_volunteer
from volunteers import verification
from workspaces import utils


@login_required
def workspace(request, student_id=None, read_only=False):
    user = request.user

    auto_resize = False

    token = utils.get_auth_token(user, "workspaces")

    if student_id is None and is_student(user):
        # This is a student accessing their workspace
        student = request.user.student
        read_only = False
        auto_resize = True
    elif is_volunteer(user):
        # This is a mentor accessing a student workspace
        volunteer = user.contact.volunteer
        student = get_object_or_404(Student, student_id=student_id)
        verification.verify_student_access(volunteer, student)
    else:
        raise http.Http404()

    connection_id = "workspace-" + student.student_id
    if read_only:
        connection_id += "-readonly"

    context = {
        "GUACAMOLE_BASE_URL": settings.GUACAMOLE_BASE_URL,
        "min": settings.DEBUG and ".min" or "",
        "user": user,
        "connection_id": connection_id,
        "auth_token": token,
        "read_only": read_only,
        "auto_resize": auto_resize,
        "site": settings.SITE,
    }
    return render(request, "workspaces/workspace.html", context)
