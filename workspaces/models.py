from django.conf import settings
from django.db import models
from django.utils import crypto


class WorkspaceAuthToken(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="workspace_auth_tokens",
    )
    token = models.CharField(max_length=64)
    created = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = crypto.get_random_string(64)
        super().save(*args, **kwargs)

    def __str__(self):
        return str("{} {}".format(self.user, self.created))
