import requests
from django.conf import settings

from sso.utils import get_auth_token


def get_available_workspaces(user):
    token = get_auth_token(user, "workspaces")

    headers = {"Authorization": "AuthToken " + token}
    r = requests.post(settings.GUACAMOLE_BASE_URL + "/api/tokens", headers=headers)
    r.raise_for_status()
    data = r.json()
    guacToken = data.get("authToken")

    tree_path = "/api/session/data/boldidea-workspaces/connectionGroups/ROOT/tree"
    url = settings.GUACAMOLE_BASE_URL + tree_path + "?token=" + guacToken
    r = requests.get(url, headers=headers)
    r.raise_for_status()
    data = r.json()
    return data.get("childConnections")
