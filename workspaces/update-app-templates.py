#!/bin/env python
import glob
import os
import posixpath
from pathlib import Path

STATIC_PATH = ("workspaces", "guacamole", "app")
BASE_DIR = os.path.dirname(__file__)
APP_DIR = os.path.join(BASE_DIR, "static", *STATIC_PATH)
JSTEMPLATE = os.path.join(
    BASE_DIR, "templates", "workspaces", "includes", "app-js.html"
)
CSSTEMPLATE = os.path.join(
    BASE_DIR, "templates", "workspaces", "includes", "app-css.html"
)


def main():
    js = ""
    ngtpl = ""
    css = ""

    static_path_url = posixpath.join(*STATIC_PATH)

    # js files
    for filepath in glob.glob(os.path.join(APP_DIR, "**", "*.js"), recursive=True):
        filepath = os.path.relpath(filepath, APP_DIR)
        js += '  <script type="text/javascript" src="{{% static "{}/{}" %}}"></script>\n'.format(
            static_path_url, Path(filepath).as_posix()
        )

    # angular template cache
    tplfiles = glob.glob(os.path.join(APP_DIR, "**", "*.html"), recursive=True)
    tplrelpaths = [Path(os.path.relpath(p, APP_DIR)).as_posix() for p in tplfiles]
    ngtpl += "  {% verbatim %}\n"
    ngtpl += '  <script type="text/javascript">\n'
    path_array_js = "[" + ", ".join("'app/{}'".format(f) for f in tplrelpaths) + "]"
    ngtpl += "    angular.module('templates-main', {});\n".format(path_array_js)
    for filepath in tplfiles:
        relfilepath = Path(os.path.relpath(filepath, APP_DIR)).as_posix()
        ngtpl += "    angular.module('app/{}', []).run(['$templateCache', function($templateCache) {{\n".format(
            relfilepath
        )
        with open(os.path.join(filepath), "r") as file:
            content = file.read()
        # escape quotes, newlines, and script tags
        content = content.replace("'", "\\'").replace("\n", "\\n")
        content = content.replace("<script", "\\u003Cscript")
        content = content.replace("</script", "\\u003C/script")
        ngtpl += "      $templateCache.put('app/{}', '{}');\n".format(
            relfilepath, content
        )
        ngtpl += "    }]);\n"
    ngtpl += "  </script>\n"
    ngtpl += "  {% endverbatim %}\n"

    # css files
    for filepath in glob.glob(os.path.join(APP_DIR, "**", "*.css"), recursive=True):
        filepath = os.path.relpath(filepath, APP_DIR)
        css += '  <link rel="stylesheet" type="text/css" href="{{% static "{}/{}" %}}"></script>\n'.format(
            static_path_url, Path(filepath).as_posix()
        )

    with open(CSSTEMPLATE, "w") as tplfile:
        tplfile.write("{% load static compress %}\n\n")
        tplfile.write("{% compress css %}\n")
        tplfile.write(css)
        tplfile.write("{% endcompress %}\n")

    print("Updated " + CSSTEMPLATE)

    with open(JSTEMPLATE, "w") as tplfile:
        tplfile.write("{% load static compress %}\n\n")
        tplfile.write("{% compress js %}\n")
        tplfile.write(js)
        tplfile.write(ngtpl)
        tplfile.write("{% endcompress %}\n")

    print("Updated " + JSTEMPLATE)


if __name__ == "__main__":
    main()
