ARG IMAGE=python:3.9-slim
ARG UV_VERSION=0.8.4
ARG UV_CACHE_DIR=/uv-cache


FROM ${IMAGE} AS image-base

# -----------------------------------------------------------------------------
# build-base: system dependencies & python build system
# -----------------------------------------------------------------------------
FROM image-base AS build-base

RUN apt-get -y update
RUN apt-get install -y --no-install-recommends build-essential gcc gdal-bin \
lcov libgdal-dev libpq-dev libsasl2-dev libssl-dev tox valgrind curl

ARG UV_VERSION
ARG UV_CACHE_DIR

ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    UV_CACHE_DIR=${UV_CACHE_DIR} \
    XDG_BIN_HOME=/usr/local/bin \
    UV_PROJECT_ENVIRONMENT=/venv \
    UV_LINK_MODE=copy \
    VIRTUAL_ENV=/venv \
    PATH="/venv/bin:$PATH"

WORKDIR /build

COPY --from=ghcr.io/astral-sh/uv:latest /uv /usr/local/bin/uv

# Set up the main venv
RUN uv venv $VIRTUAL_ENV


# ------------------------------------------------------------------------------
# build: python dependencies & hive build
# ------------------------------------------------------------------------------
FROM build-base AS build
ENV PATH="/venv/bin:$PATH" \
    UV_PROJECT_ENVIRONMENT=/venv \
    UV_LINK_MODE=copy \
    VIRTUAL_ENV=/venv

# Build wheel for production use
COPY pyproject.toml uv.lock ./
RUN uv sync
COPY . .
RUN uv build
RUN uv pip install dist/*.whl

# ------------------------------------------------------------------------------
# static-build: Static files build stage
# ------------------------------------------------------------------------------
# This is used as a base to copy over static files into the nginx stage
FROM build AS static-build
ARG HIVE_ENVIRONMENT

ENV DJANGO_STATIC_ROOT=/hive/static \
    VIRTUAL_ENV=/venv \
    COMPRESS_ROOT=/hive/static \
    COMPRESS_OFFLINE=True \
    PATH="/venv/bin:$PATH"

# Build & collect staticfiles for later
RUN DJANGO_SECRET_KEY=collectstatic-tmp MANAGEMENT_SCRIPT=1 /venv/bin/manage compress
RUN DJANGO_SECRET_KEY=collectstatic-tmp MANAGEMENT_SCRIPT=1 /venv/bin/manage collectstatic --noinput

# ------------------------------------------------------------------------------
# base: Base stage (system deps and app paths)
# ------------------------------------------------------------------------------
FROM image-base AS base

# Note: postgresql-client is needed for the pg_dump binary for db backup job
RUN apt-get -y update && \
    apt-get install -y --no-install-recommends gdal-bin postgresql-client

RUN useradd -Md /hive hive
WORKDIR /hive

RUN mkdir /hive/media && \
    mkdir /hive/db-backups && \
    chown -R hive:hive /hive

# ------------------------------------------------------------------------------
# base-app: Base app stage
# ------------------------------------------------------------------------------
FROM base AS base-app

# copy venv from build stage
COPY --from=build --chown=hive:hive /venv /venv

# ------------------------------------------------------------------------------
# nginx: Nginx gateway service
# ------------------------------------------------------------------------------
# With Azure App Services, we can't share volumes between containers, so we have
# to build  nginx alongside the base image and use that to serve as the primary
# gateway as well as to serve static files. This allows us to keep our config in
# version control, and use the existing python base to collect static files.
FROM nginx:alpine AS nginx-prod
ARG DJANGO_STATIC_ROOT=/hive/static
ARG GIT_COMMIT_SHA
LABEL org.label-schema.vcs-ref=$GIT_COMMIT_SHA

COPY --from=static-build /hive/static /hive/static

COPY ./docker/configs/prod/nginx /etc/nginx/templates

# ------------------------------------------------------------------------------
# prod: Production build
# ------------------------------------------------------------------------------
FROM base-app AS prod

ARG WSGI_APP=hive.wsgi:application
ARG WSGI_PORT=8000
ARG GIT_COMMIT_SHA
LABEL org.label-schema.vcs-ref=$GIT_COMMIT_SHA

ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    WSGI_APP=${WSGI_APP} \
    WSGI_PORT=${WSGI_PORT} \
    WORKERS=3 \
    THREADS=1 \
    VIRTUAL_ENV=/venv \
    PATH="/venv/bin:$PATH"

# Copy django-compress cache manifest
# Note: if django-compress can't find this, it will give an error like this:
#
#   "You have offline compression enabled but key XXXXX is missing..."
#
# We don't need to copy the whole static dir b/c they will be copied to the
# nginx container.
COPY --from=static-build \
    /hive/static/CACHE/manifest.json \
    /hive/static/CACHE/manifest.json

# Add openssh-server server for azure webapp SSH access
# See: https://learn.microsoft.com/en-us/azure/app-service/configure-custom-container
RUN apt-get install -y --no-install-recommends openssh-server && \
    useradd -d /hive -G hive -s /usr/bin/bash hive-admin && \
    echo "hive-admin:BoldIdeaHive!" | chpasswd
COPY ./docker/configs/prod/sshd_config /etc/ssh/sshd_config

COPY ./docker/configs/prod/docker-entrypoint.sh /docker-entrypoint.sh

# Clean up apt
RUN apt-get remove --purge --auto-remove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists

CMD bash --noprofile --norc /docker-entrypoint.sh

# ------------------------------------------------------------------------------
# dev: Local dev build
# ------------------------------------------------------------------------------
FROM base-app AS dev

ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    XDG_BIN_HOME=/usr/local/bin \
    VIRTUAL_ENV=/venv \
    UV_PROJECT_ENVIRONMENT=/venv \
    UV_LINK_MODE=copy \
    PATH="/venv/bin:$PATH"

# Install system packages useful for building newly added python deps
RUN apt-get install -y --no-install-recommends \
    build-essential gcc lcov valgrind curl libffi-dev libssl-dev git

COPY --from=ghcr.io/astral-sh/uv:latest /uv /usr/local/bin/uv

RUN uv pip install azure-cli

WORKDIR /src

# Copy hive src (though this should be mounted via docker volume)
COPY . .
COPY --from=build /build/uv.lock .

# Update image w/ dev packages
RUN uv sync --extra dev

# Make /hive/static writable for development
RUN mkdir /hive/static && chown -R hive:hive /hive/static

USER hive

CMD ["manage", "runserver", "0.0.0.0:8000"]
