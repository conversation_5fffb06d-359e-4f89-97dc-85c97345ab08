#!/bin/env python
import argparse
import os
import re
import shutil
import subprocess
import sys
from configparser import ConfigParser
from io import StringIO

parser = argparse.ArgumentParser(
    description="Generate self-sigend wildcard SSL certificate files for the given domain"
)

default_dir = os.environ.get("LOCALCERTS_SSL_DIR") or os.getcwd()
parser.add_argument(
    "--dir",
    metavar="PATH",
    type=str,
    default=default_dir,
    help="The destination directory for the generated certificates",
)

subparsers = parser.add_subparsers(dest="cmd", metavar="COMMAND")

create_parser = subparsers.add_parser("create", help="generate new SSL cert files")
create_parser.add_argument(
    "domain", metavar="DOMAIN", type=str, help="The base domain for the wildcard cert"
)

dump_parser = subparsers.add_parser("dump", help="output a given key")
file_types = {
    "cert": "crt",
    "key": "key",
    "pem": "pem",
    "ca-cert": "ca.crt",
    "ca-key": "ca.key",
}
file_type_choices = file_types.keys()
dump_parser.add_argument(
    "domain",
    metavar="DOMAIN",
    type=str,
    help="The base domain for the wildcard cert (eg: example.com)",
)

dump_parser.add_argument(
    "type",
    metavar="TYPE",
    type=str,
    choices=file_types.keys(),
    help=f'where TYPE is one of: {", ".join(file_type_choices)}',
)


def clean(files):
    for file in files:
        try:
            os.remove(file)
        except Exception:
            pass


class CommandError(Exception):
    pass


def generate_local_ssl_certs(domain, output_dir, stdout=None):
    if not stdout:
        stdout = sys.stdout

    cnf_filename = os.path.join(output_dir, f"{domain}.cnf")
    ca_key_filename = os.path.join(output_dir, f"{domain}.ca.key")
    ca_crt_filename = os.path.join(output_dir, f"{domain}.ca.crt")
    pem_filename = os.path.join(output_dir, f"{domain}.pem")
    csr_filename = os.path.join(output_dir, f"{domain}.csr")
    key_filename = os.path.join(output_dir, f"{domain}.key")
    crt_filename = os.path.join(output_dir, f"{domain}.crt")

    files = (
        cnf_filename,
        ca_key_filename,
        ca_crt_filename,
        csr_filename,
        key_filename,
        crt_filename,
        pem_filename,
    )
    clean(files)

    config = ConfigParser()
    config.optionxform = lambda option: option

    # Determine location of openssl.conf
    openssl_bin = shutil.which("openssl")
    if not openssl_bin:
        raise Exception(
            "The openssl binary could not be found on your system. Please make "
            "sure you have OpenSSL installed."
        )

    OPENSSL_CONF = os.environ.get("OPENSSL_CONF")
    if OPENSSL_CONF is None:
        # find openssl installation
        out = subprocess.check_output([openssl_bin, "version", "-d"])
        match = re.match(r'OPENSSLDIR: "(.+)"', out.decode())
        openssl_dir = match.group(1)
        OPENSSL_CONF = os.path.join(openssl_dir, "openssl.cnf")

    with open(OPENSSL_CONF, "r") as stream:
        config.read_string("[top]\n" + stream.read())

    config.set(" v3_ca ", "subjectKeyIdentifier", "hash")
    config.set(" v3_ca ", "authorityKeyIdentifier", "keyid:always,issuer")
    config.set(" v3_ca ", "basicConstraints", "critical, CA:TRUE, pathlen:3")
    config.set(" v3_ca ", "keyUsage", "critical, cRLSign, keyCertSign")
    config.set(" v3_ca ", "nsCertType", "sslCA, emailCA")

    config.set(" v3_req ", "basicConstraints", "CA:FALSE")
    config.set(
        " v3_req ", "keyUsage", "nonRepudiation, digitalSignature, keyEncipherment"
    )
    config.set(" v3_req ", "subjectAltName", "@alt_names")
    config.remove_option(" v3_req ", "extendedKeyUsage")

    config.add_section(" alt_names ")
    config.set(" alt_names ", "DNS.1", domain)
    config.set(" alt_names ", "DNS.2", "*.{}".format(domain))

    config.set(" req ", "req_extensions", "v3_req")

    # Write config to string
    temp = StringIO()
    config.write(temp)
    temp.seek(0)
    config_str = temp.read()

    # Remove first line from config
    config_str = "\n".join(config_str.splitlines()[1:])

    with open(cnf_filename, "w") as f:
        f.write(config_str)

    def openssl_run(*args):
        cmd = ("openssl",) + args
        stdout.write(" ".join(cmd) + "\n")
        stdout.write(subprocess.check_output(cmd).decode())

    openssl_run("genrsa", "-out", ca_key_filename, "2048")
    openssl_run(
        "req",
        "-new",
        "-x509",
        "-subj",
        f"/CN={domain}",
        "-extensions",
        "v3_ca",
        "-days",
        "824",
        "-key",
        ca_key_filename,
        "-sha256",
        "-out",
        ca_crt_filename,
        "-config",
        cnf_filename,
    )
    openssl_run("genrsa", "-out", key_filename, "2048")
    openssl_run(
        "req",
        "-subj",
        f"/CN={domain}",
        "-extensions",
        "v3_req",
        "-sha256",
        "-new",
        "-key",
        key_filename,
        "-out",
        csr_filename,
    )
    openssl_run(
        "x509",
        "-req",
        "-extensions",
        "v3_req",
        "-days",
        "824",
        "-sha256",
        "-in",
        csr_filename,
        "-CA",
        ca_crt_filename,
        "-CAkey",
        ca_key_filename,
        "-CAcreateserial",
        "-out",
        crt_filename,
        "-extfile",
        cnf_filename,
    )
    openssl_run("x509", "-in", crt_filename, "-text", "-noout")

    stdout.write(
        "\n------------\n"
        "Successfully created SSL certificates.\n\n"
        "You can export the .ca.crt file by running the following command:\n\n"
        f"    localcerts dump {domain} ca-cert > /tmp/{domain}.ca.crt \n\n"
        "You must now add the certificate CA to your browser's trusted certs.\n"
        "In Chrome, go to chrome://settings/certificates, click the\n"
        "'Authorities' tab, click 'Import', then select the generated .ca.crt file.\n\n"
    )


def dump(domain, type, dir):
    ext = file_types[type]
    with open(os.path.join(dir, f"{domain}.{ext}")) as f:
        sys.stdout.write(f.read())


if __name__ == "__main__":
    args = parser.parse_args()

    if args.cmd == "dump":
        dump(args.domain, args.type, args.dir)
    elif args.cmd == "create":
        generate_local_ssl_certs(args.domain, args.dir)
