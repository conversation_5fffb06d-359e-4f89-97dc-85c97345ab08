#!/usr/bin/env python
import secrets
import sys


def mksecret(length, symbols=False):
    alpha = "abcdefghijklmnopqrstuvwxyz0123456789"
    if symbols:
        alpha += "!@#$%^&*(-_=+)"
    return "".join(secrets.choice(alpha) for i in range(length))


def mkenv():
    env = ""
    with open("/hive/env.template") as env_file:
        env = env_file.read()

    env = env.replace("{{DB_PASSWORD}}", mksecret(11))
    env = env.replace("{{SECRET_KEY}}", mksecret(50, True))
    env = env.replace("{{AES_KEY}}", secrets.token_bytes(32).hex())

    sys.stdout.write(env)


if __name__ == "__main__":
    mkenv()
