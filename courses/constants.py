CODING_ENVIRONMENT_CHOICES = (
    ("IDE", "Built-in IDE"),
    ("WORKSPACES", "Workspaces"),
    ("REPLIT", "Repl.it"),
    ("<PERSON><PERSON><PERSON>CH", "Glitch"),
)

BADGE_DEFAULT_NARRATIVE_TEMPLATE_FIELDS = [
    {"name": "club", "label": "Club name", "type": "text", "required": True},
    {"name": "mentor_name", "label": "Mentor name", "type": "text", "required": True},
    {
        "name": "mentor_notes",
        "label": "Mentor notes",
        "type": "textarea",
        "required": False,
    },
]

BADGE_DEFAULT_NARRATIVE_TEMPLATE = """
Student has completed all badge criteria as verified by a trained Bold Idea mentor.

**Mentor:** {{mentor_name}}  
**Club:** {{club}}
{% if mentor_notes %}
**Mentor notes:**  
{{mentor_notes}}
{% endif %}
"""
