# Updates badge data from openbadge url
import logging

from django.core.management.base import BaseCommand, CommandError
from django.db.models import Q

from courses.badges import badgr
from courses.models import Badge
from students.models import Student
from students.utils import get_student_participated_courses

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "--student",
            action="append",
            dest="students",
            help="Only sync assertions for the given student(s).",
        )

        parser.add_argument(
            "--club",
            action="append",
            dest="clubs",
            help="Only sync assertions for students in the given club(s).",
        )

        parser.add_argument(
            "--badge",
            action="append",
            dest="badges",
            help="Only sync the badge(s) with the given id",
        )

        parser.add_argument(
            "--skip-assertions",
            type=bool,
            dest="skip_assertions",
            default=False,
            help="Don't sync badge assertions",
        )

    def handle(self, *args, **options):
        badges = Badge.objects.all()
        students = None

        if options.get("students") and options.get("clubs"):
            raise CommandError(
                "You can only specifiy one of--students or --clubs, but not both"
            )

        if options.get("clubs"):
            club_pks = (int(v) for v in options["clubs"] if v.isnumeric())
            club_codes = (v for v in options["clubs"] if not v.isnumeric())
            students = Student.objects.filter(
                Q(clubs__club_code__in=club_codes) | Q(club__club_id__in=club_pks)
            )
        elif options.get("students"):
            students = Student.objects.filter(student_id__in=options["students"])

        if students is not None:
            limit_courses = set()
            for student in students:
                for course in get_student_participated_courses(student):
                    limit_courses.add(course)
            badges = badges.filter(courses__in=limit_courses)

        if options.get("badges"):
            badges = badges.filter(pk__in=options["badges"])

        if badges.count() == 0:
            raise CommandError("No badges found to sync.")

        badgr.sync_badges(
            badges,
            students,
            skip_assertions=options["skip_assertions"],
            stdout=self.stdout,
            stderr=self.stderr,
        ),
