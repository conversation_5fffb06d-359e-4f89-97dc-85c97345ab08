from django.conf.urls import url

from courses import views

app_name = "courses"

urlpatterns = [
    url(r"^roadmap/$", views.roadmap, name="roadmap"),
    url(r"^select/$", views.select_course, name="select-course"),
    url(r"^resources/$", views.resources, name="resources"),
    url(r"^(?P<course_id>\d+)/resources/$", views.resources, name="resources"),
    url(
        r"^embed/roadmap/$", views.roadmap, name="embed-roadmap", kwargs={"embed": True}
    ),
    url(
        r"^embed/select/$",
        views.select_course,
        name="embed-select-course",
        kwargs={"embed": True},
    ),
    url(
        r"^embed/resources/$",
        views.resources,
        name="embed-resources",
        kwargs={"embed": True},
    ),
    url(r"^api/roadmap/steps/(?P<step_id>\d+)$", views.update_step, name="update-step"),
    url(r"^resources/(?P<slug>[^/]+)/$", views.resource_link, name="resource-link"),
    url(
        r"^resources/(?P<course_id>[^/]+)/(?P<slug>[^/]+)/$",
        views.resource_link,
        name="resource-link",
    ),
    url(r"^quizzes/(?P<slug>[^/]+)/$", views.quiz, name="quiz"),
    url(r"^(?P<type>[^/]+)-survey/$", views.survey, name="survey"),
]
