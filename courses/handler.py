from django.db.models import Q

from courses import models


class BaseCourseHandler:
    """
    The purpose of the course handler is to provide abstraction so that both students and
    volunteers can interact with course material. Volunteers should be able to see courses just as
    students do, but there are certain things that are different for volunteers.

    The course implementations are defined in `student_portal/course_handler.py` and
    `dashboard/course_handler.py`
    """

    def get_available_courses(self, request):
        return models.Course.objects.all()

    def get_course(self, request):
        raise NotImplementedError()

    def get_roadmap(self, request):
        raise NotImplementedError()

    def update_roadmap_step(self, request, roadmap, step_id, completed):
        raise NotImplementedError()

    def get_resource_links(self, request=None, course=None, include_lti=False):
        qs = models.ResourceLink.objects.filter(Q(course=None) | Q(course=course))
        if not include_lti:
            qs = qs.exclude(lti=True)
        return qs

    def get_roadmap_context(self, request, context):
        return context

    def get_select_course_context(self, request, context):
        return context

    def get_resources_page_context(self, request, context):
        return context

    def on_course_selected(self, requset):
        pass
