import base64
import json
import re
import sys
import time
from datetime import date, datetime

import ciso8601
import requests
import simplejson
from django.conf import settings
from django.utils import timezone
from requests import Session, exceptions
from requests.auth import AuthBase

from courses.models import BadgeAssertion
from students.models import Student


class BadgrError(Exception):
    pass


class BadgrValidationError(BadgrError):
    def __init__(self, res):
        self.res = res
        error = ""
        if res.validation_errors:
            error = str(res.validation_errors)
        if res.field_errors:
            error = str(res.field_errors)
        super().__init__(f"Validation error: {error}")


class BadgrAuth(AuthBase):
    def __init__(self):
        super().__init__()
        self.access_token = None
        self.access_token_expiration = None

    def __call__(self, request):
        if self.access_token_expiration is not None:
            token_time_remaining = self.access_token_expiration - time.time()
        else:
            token_time_remaining = 0

        if token_time_remaining < 5:
            # re-fresh the access token
            self.get_access_token()

        request.headers["Authorization"] = "Bearer {}".format(self.access_token)
        return request

    def get_access_token(self):
        auth_res = requests.post(
            "https://api.badgr.io/o/token",
            data={
                "username": settings.BADGR_USERNAME,
                "password": settings.BADGR_PASSWORD,
            },
        )
        auth_res.raise_for_status()
        try:
            data = auth_res.json()
        except (json.JSONDecodeError, simplejson.JSONDecodeError):
            body = auth_res.content
            raise BadgrError(
                f"Could not parse JSON response during authentication: {body}"
            )
        self.access_token = data["access_token"]
        self.access_token_expiration = time.time() + data["expires_in"]


def json_object_hook(obj):
    d = {}
    for k, v in obj.items():
        if isinstance(v, str):
            try:
                dt = ciso8601.parse_datetime(v)
                d[k] = timezone.localtime(dt)
            except ValueError:
                d[k] = v
        else:
            d[k] = v
    return d


def json_serializer_default(obj):
    if isinstance(obj, (date, datetime)):
        return obj.isoformat()
    return str(obj)


class BadgrResponse:
    def __init__(self, response):
        self._response = response
        self._data = None
        if "json" in response.headers.get("Content-Type"):
            self._data = response.json(object_hook=json_object_hook)

    def raise_for_status(self):
        if self._response.status_code == 400:
            raise BadgrValidationError(self)
        self._response.raise_for_status()

    @property
    def ok(self):
        return (
            self._response.status_code < 400
            and self._data["status"].get("success") is True
        )

    @property
    def result(self):
        return self._data.get("result")

    @property
    def status(self):
        return self._data.get("status")

    @property
    def field_errors(self):
        return self._data.get("fieldErrors")

    @property
    def validation_errors(self):
        return self._data and self._data.get("validationErrors")


class BadgrSession(Session):
    HTTPError = exceptions.HTTPError

    base_url = "https://api.badgr.io/v2/"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.auth = BadgrAuth()

    def request(self, method, url, *args, **kwargs):
        url = self.base_url.rstrip("/") + "/" + url.lstrip("/")

        kwargs["headers"] = kwargs.get("headers") or {}

        # pass json obj through serializer if provided
        if kwargs.get("json"):
            obj = kwargs.pop("json")
            kwargs["data"] = json.dumps(obj, default=json_serializer_default)
            kwargs["headers"]["Content-Type"] = "application/json"

        res = super().request(method, url, *args, **kwargs)

        return BadgrResponse(res)


def get_badge_entity_id_from_uri(uri):
    return re.match(r"https://api.badgr.io/public/badges/([^/]+)", uri).group(1)


def get_assertion_entity_id_from_uri(uri):
    return re.match(r"https://api.badgr.io/public/assertions/([^/]+)", uri).group(1)


def build_badgeclass(badge, form_data, session=None):
    session = session or BadgrSession()

    # Get existing badgeclass definition from badgr
    # (Note: the schema for the Badgr API is slightly different schema from the openbadge schema)
    if badge.pk is not None:
        badgeclass_id = get_badge_entity_id_from_uri(badge.data["id"])
        response = session.get(f"badgeclasses/{badgeclass_id}")
        response.raise_for_status()
        badgeclass = response.result[0]
    else:
        badgeclass = {
            "issuer": settings.BADGR_ISSUER_ID,
            "issuerOpenBadgeId": f"https://api.badgr.io/public/issuers/{settings.BADGR_ISSUER_ID}",
        }

    badgeclass.update(
        {
            "name": form_data["name"],
            "description": form_data["description"],
            "criteriaNarrative": form_data["earning_criteria"] or None,
            "criteriaUrl": form_data["criteria_url"] or None,
        }
    )

    image = form_data["image"]
    if image:
        b64_data = base64.b64encode(image.read()).decode()
        image_encoded = f"data:{image.content_type};base64,{b64_data}"
        badgeclass["image"] = image_encoded

    if form_data.get("narrative_template"):
        badgeclass["extensions"] = {
            "extensions:narrativeTemplate": {
                "@context": (
                    "https://static.boldidea.org/openbadge/extensions/narrativeTemplate/"
                    "context.json"
                ),
                "type": ["Extension", "extensions:NarrativeTemplate"],
                "evidenceTemplate": {
                    "fields": json.loads(form_data["narrative_template_fields"]),
                    "template": form_data["narrative_template"],
                },
            }
        }

    return badgeclass


def save_badgeclass(badgeclass, session=None):
    session = session or BadgrSession()
    entity_id = badgeclass.get("entityId")
    if entity_id:
        path = f"badgeclasses/{entity_id}"
        res = session.put(path, json=badgeclass)
    else:
        path = f"issuers/{settings.BADGR_ISSUER_ID}/badgeclasses"
        res = session.post(path, json=badgeclass)

    if not res.ok:
        res.raise_for_status()

    return res


def build_assertion(badge, student, evidence_url, evidence_narrative):
    # build the assertion
    assertion = {
        "badgeclass": get_badge_entity_id_from_uri(badge.openbadge_url),
        "extensions": {
            "extensions:recipientProfile": {
                "@context": "https://openbadgespec.org/extensions/recipientProfile/context.json",
                "type": ["Extension", "extensions:RecipientProfile"],
                "name": student.name,
            }
        },
        "evidence": [{"narrative": evidence_narrative}],
        "recipient": {
            "type": "email",
            "hashed": False,
            "identity": settings.BADGR_RECIPIENT_FORMAT.format(student=student),
        },
    }

    if evidence_url:
        assertion["evidence"][0]["url"] = evidence_url

    return assertion


def save_assertion(assertion, session=None):
    session = session or BadgrSession()
    path = "issuers/{}/assertions".format(settings.BADGR_ISSUER_ID)
    res = session.post(path, json=assertion)

    if not res.ok:
        res.raise_for_status()

    return res


def revoke_assertion(assertion, reason, session=None):
    session = session or BadgrSession()
    entity_id = get_assertion_entity_id_from_uri(assertion.openbadge_url)
    path = "assertions/{}".format(entity_id)
    res = session.delete(path, json={"revocation_reason": reason})

    if not res.ok:
        res.raise_for_status()

    return res


def sync_badges(badges, students=None, skip_assertions=False, stdout=None, stderr=None):
    """
    Pull badge data from Badgr into local model, in case badge definition has changed on Badgr.
    """
    stdout = stdout or sys.stdout
    stderr = stderr or sys.stderr

    session = BadgrSession()

    # Sync  badges
    for badge in badges:
        stdout.write(f'Updating "{badge}": {badge.openbadge_url}\n')
        try:
            badge.update_openbadge_data()
            badge.save()
        except Exception as e:
            stderr.write(f"Could not update badge {badge.openbadge_url}: {e}")
            continue

        if skip_assertions:
            continue

        stdout.write("  Updating assertions...")

        params = {"include_revoked": "true"}
        if students:
            params["recipient"] = [
                f"{s.student_id}@{settings.STUDENT_USER_EMAIL_DOMAIN}" for s in students
            ]

        badgeclass_id = get_badge_entity_id_from_uri(badge.openbadge_url)
        res = session.get(f"/badgeclasses/{badgeclass_id}/assertions", params=params)
        res.raise_for_status()
        time.sleep(1)

        current_assertions = {
            (badge.id, a.student_id): a for a in badge.assertions.all()
        }
        current_assertions_by_openbadge_id = {
            a.openbadge_url: a for a in badge.assertions.all()
        }

        for assertion in res.result:
            if assertion["revoked"]:
                if assertion["openBadgeId"] in current_assertions_by_openbadge_id:
                    # Assertion exists in our DB, but is revoked on Badgr, delete it
                    try:
                        db_assertion = BadgeAssertion.objects.get(
                            openbadge_url=assertion["openBadgeId"]
                        )
                    except BadgeAssertion.DoesNotExist:
                        continue
                    else:
                        db_assertion.delete()
                        del current_assertions_by_openbadge_id[
                            db_assertion.openbadge_url
                        ]
                        if (badge.id, db_assertion.student_id) in current_assertions:
                            del current_assertions[(badge.id, db_assertion.student_id)]
                        stdout.write(f"  Deleted revoked assertion: {db_assertion}")
                continue

            student_email = assertion["recipient"]["identity"]
            student_id = student_email.split("@")[0]
            try:
                student = Student.objects.get(student_id=student_id)
            except Student.DoesNotExist:
                stderr.write(f"  Could not find student with student_id: {student_id}")
                continue

            if assertion["openBadgeId"] not in current_assertions_by_openbadge_id:
                existing = current_assertions.get((badge.id, student.id))
                if existing:
                    # This is assertion is duplicated in badgr (same badge+student)
                    # TODO: better way for staff to fix duplicate assertions?
                    stderr.write(
                        "  WARNING: Duplicate assertion found: \n"
                        f'    duplicate: {assertion["openBadgeId"]}\n'
                        f"    existing:  {existing.openbadge_url}"
                    )
                else:
                    # Assertion is valid, and does not exist in our DB, create it.
                    db_assertion = BadgeAssertion(
                        student=student,
                        badge=badge,
                        openbadge_url=assertion["openBadgeId"],
                    )
                    db_assertion.update_openbadge_data()
                    db_assertion.save()
                    current_assertions[(badge.id, student.id)] = db_assertion
                    stdout.write(f"  Created assertion: {db_assertion}")
