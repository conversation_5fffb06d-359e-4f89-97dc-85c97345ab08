import re

import jq


class AssertionValidation:
    def __init__(self, extension_data):
        self.validations = extension_data["validations"]
        self.errors = []

    def get_required_fields(self):
        required_fields = []
        for validation in self.validations:
            if validation["validationType"] == "required":
                required_fields.append(validation["path"])
        return required_fields

    def validate(self, assertion):
        self.errors = []
        for validation in self.validations:
            values = jq.compile(validation["path"]).input(assertion).all()
            for value in values:
                if validation["validationType"] == "required":
                    if value in (None, ""):
                        self.errors.append(validation["message"])
                elif validation["validationType"] == "pattern":
                    pattern = validation["pattern"]
                    if not re.match(pattern, value):
                        self.errors.append(validation["message"])

    def is_valid(self, assertion):
        self.validate(assertion)
        return len(self.errors) == 0
