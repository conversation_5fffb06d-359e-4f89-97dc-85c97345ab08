from copy import copy

from django.template import Context, Template


class NarrativeTemplate:
    def __init__(self, extension_data):
        data = extension_data["evidenceTemplate"]
        self.template = data["template"]
        self.fields = [copy(field) for field in data["fields"]]
        self.errors = []

    def load_values(self, data, prefix=""):
        for field in self.fields:
            key = prefix + field["name"]
            field["value"] = data.get(key, "")

    def validate(self):
        self.errors = []

        # validate required fields
        required_fields_missing = False
        for field in self.fields:
            if field.get("required") and not field.get("value"):
                required_fields_missing = True

        if required_fields_missing:
            self.errors.append("Please complete all required fields*")

    def is_valid(self):
        self.validate()
        return len(self.errors) == 0

    def render(self):
        # TODO?: use mustache instead, so we can render a preview client-side
        # build the narrative text
        tpl_context = {field["name"]: field["value"] for field in self.fields}
        tpl = Template(self.template)
        return tpl.render(Context(tpl_context))
