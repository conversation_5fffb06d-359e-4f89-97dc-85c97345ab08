import hashlib
import json

from django import http
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import logout
from django.contrib.auth.views import login_required
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy as reverse
from django.utils import timezone
from django.utils.translation import gettext as _
from django.views.decorators.csrf import csrf_exempt
from lti import ToolConsumer

from clubs.models import Club, Session
from courses import models
from hive.utils import model_hashid
from students.models import Student, StudentLogin
from surveys.models import StudentSurveyLink
from users.utils import is_student
from volunteers.models import Volunteer
from volunteers.utils import get_volunteer_clubs

from .utils import get_course_handler


def get_student(request):
    user = request.user
    if not user or not user.is_authenticated:
        return

    try:
        return user.student
    except Student.DoesNotExist:
        pass

    try:
        student = StudentLogin.objects.get(
            service="GOOGLE", username=user.email
        ).student
    except StudentLogin.DoesNotExist:
        return

    # associate student w/ user
    student.user = user
    student.save()
    return student


def student_login_required(view):
    @login_required(login_url=reverse("courses:login"))
    def wrapper(request, *args, **kwargs):
        student = get_student(request)
        if not student:
            messages.add_message(
                request,
                messages.ERROR,
                "Could not find student matching {}".format(request.user.email),
            )
            logout(request)
            return http.HttpResponseRedirect(reverse("courses:login"))
        return view(request, *args, **kwargs)

    return wrapper


@student_login_required
def index(request):
    student = get_student(request)
    # clubs = student.clubs.current().exclude(course=None)
    clubs = student.clubs.all().exclude(course=None)
    context = {"clubs": clubs}
    return render(request, "courses/index.html", context)


@student_login_required
def course(request, club_id, session_number=None):
    club = get_object_or_404(Club, pk=club_id)
    student = get_student(request)

    if session_number:
        session = get_object_or_404(
            Session, club=club, type="CLUB", number=session_number
        )
    else:
        today = timezone.now().date()
        session = club.sessions.filter(type="CLUB", date__gte=today).order_by("date")[0]

    exit_tickets = []
    if club.course:
        for exit_ticket in club.course.exit_tickets.order_by("-session_number"):
            exit_tickets.append((exit_ticket, exit_ticket.get_student_best(student)))

    context = {"club": club, "session": session, "exit_tickets": exit_tickets}
    return render(request, "courses/course.html", context)


@student_login_required
def quiz(request, slug):
    student = get_student(request)
    quiz = get_object_or_404(models.Quiz, slug=slug)

    student_quiz = None
    error = None
    if quiz:
        student_quiz = quiz.get_student_latest(student)
        questions = quiz.questions.order_by("question_number")
        error = None
        if quiz.can_retake(student) and request.method == "POST":
            answers = []
            for i, question in enumerate(questions):
                key = "question_{}".format(question.pk)
                answer = set(int(n.strip()) for n in request.POST.getlist(key))
                if len(answer) > 0:
                    answers.append(answer)

            num_answered = len(answers)

            if num_answered < quiz.questions.count():
                messages.add_message(
                    request, messages.ERROR, "Please answer each question!"
                )
            else:
                results = quiz.build_results(answers)
                num_correct = sum(1 for r in results if r["is_correct"])
                student_quiz = models.StudentQuiz.objects.create(
                    quiz=quiz,
                    student=student,
                    num_questions=quiz.questions.count(),
                    num_correct=num_correct,
                    results=results,
                )
                return http.HttpResponseRedirect(".")

    context = {
        "quiz": quiz,
        "questions": questions,
        "student_quiz": student_quiz,
        "error": error,
    }
    return render(request, "courses/quiz.html", context)


@login_required
@csrf_exempt
def update_step(request, step_id):
    step = get_object_or_404(models.Step, pk=step_id)

    course_handler = get_course_handler()
    roadmap = course_handler.get_roadmap(request)

    if request.method == "POST":
        data = json.loads(request.body)
        completed = data.get("completed")
        if completed is None:
            return http.HttpResponseBadRequest("completed is required")

        # Verify that previous steps have been completed
        # TODO: This might be too restrictive. If needed we can safely remove the requirement
        # to complete previous steps.
        if completed:
            completed_steps = []
            if roadmap.pk:
                completed_steps = [
                    s.step_id
                    for s in roadmap.completed_steps.filter(
                        step__section__unit__course=step.section.unit.course
                    )
                ]

            uncompleted_previous_steps = (
                models.Step.objects.filter(
                    Q(section__unit__course=step.section.unit.course)
                    & (
                        Q(section__unit__number__lt=step.section.unit.number)
                        | Q(
                            section__unit=step.section.unit,
                            section__number__lt=step.section.number,
                        )
                        | Q(section=step.section, number__lt=step.number)
                    )
                )
                .exclude(pk__in=completed_steps)
                .exclude(archived=True)
            )

            if uncompleted_previous_steps.count():
                return http.HttpResponse(
                    json.dumps(
                        {
                            "error": _(
                                "Oops! You must first complete all previous steps."
                            ),
                        },
                        indent=2,
                    ),
                    content_type="application/json",
                )

        # Update the roadmap
        roadmap_step = course_handler.update_roadmap_step(
            request, roadmap, step_id, completed
        )

        if completed and roadmap_step is not None:
            return http.HttpResponse(
                json.dumps(
                    {
                        "success": True,
                        "roadmap_step": {
                            "id": roadmap_step.id,
                            "roadmap_id": roadmap_step.roadmap_id,
                            "user_id": roadmap_step.roadmap.user_id,
                            "step_id": roadmap_step.step_id,
                            "completed_date": roadmap_step.completed_date.isoformat(),
                        },
                    },
                    indent=2,
                ),
                content_type="application/json",
            )

        return http.HttpResponse(json.dumps({"success": True}, indent=2))

    return http.HttpResponseNotAllowed(allowed_methods=["POST"])


class RedirectException(Exception):
    def __init__(self, response_or_url):
        self._response_or_url = response_or_url
        self.next = None

    @property
    def response(self):
        if isinstance(self._response_or_url, str):
            url = self._response_or_url
            if self.next:
                url += "?next=" + self.next
            return redirect(self._response_or_url)
        return self._response_or_url


def course_required(view):
    def wrapper(request, *args, **kwargs):
        handler = get_course_handler()
        try:
            handler.get_course(request)
        except RedirectException as e:
            return e.response
        return view(request, *args, **kwargs)

    return wrapper


def roadmap_required(view):
    def wrapper(request, *args, **kwargs):
        handler = get_course_handler()
        try:
            handler.get_roadmap(request)
        except RedirectException as e:
            return e.response
        return view(request, *args, **kwargs)

    return wrapper


@login_required
@course_required
def roadmap(request, embed=False):
    handler = get_course_handler()
    try:
        roadmap = handler.get_roadmap(request)
    except RedirectException as e:
        return e.response

    context = {
        "embed": embed,
        "base_tpl": embed and "courses/embed.html" or "courses/base.html",
        "roadmap": roadmap,
        "course": roadmap.course,
        "available_courses": handler.get_available_courses(request),
    }
    context = handler.get_roadmap_context(request, context)
    response = render(request, "courses/roadmap.html", context)
    if embed:
        response.xframe_options_exempt = True
    return response


@login_required
def select_course(request, embed=False):
    handler = get_course_handler()
    next = request.GET.get("next", "/")

    try:
        available_courses = handler.get_available_courses(request)
    except RedirectException as e:
        return e.response

    available_courses = available_courses.exclude(archived=True)

    selected_course = None
    if available_courses.count() == 1:
        selected_course = available_courses[0]
    elif request.GET.get("course_id"):
        try:
            selected_course = models.Course.objects.get(pk=request.GET["course_id"])
        except models.Course.DoesNotExist:
            return http.HttpBadRequest("Invalid course_id")

    if selected_course:
        try:
            handler.on_course_selected(request, selected_course)
        except RedirectException as e:
            return e.response

        return redirect(next)

    context = {
        "embed": embed,
        "base_tpl": embed and "courses/embed.html" or "courses/base.html",
        "courses": available_courses,
        "next": next,
    }
    context = handler.get_select_course_context(request, context)
    response = render(request, "courses/select_course.html", context)
    if embed:
        response.xframe_options_exempt = True
    return response


@login_required
def resources(request, course_id=None, **kwargs):
    handler = get_course_handler()
    if course_id:
        course = models.Course.objects.get(pk=course_id)
    else:
        try:
            course = handler.get_course(request)
        except RedirectException as e:
            return e.response

    try:
        available_courses = handler.get_available_courses(request)
        resource_links = handler.get_resource_links(
            request=request, course=course
        ).order_by("section", "order")
    except RedirectException as e:
        return e.response

    # exclude LTI resource URLs (these are linked from the roadmap using [[resource:foo]]
    resource_links = resource_links.exclude(lti=True)
    context = {
        "course": course,
        "resources": resource_links,
        "available_courses": available_courses,
    }
    context = handler.get_resources_page_context(request, context)
    return render(request, "courses/resources.html", context)


@login_required
def resource_link(request, slug, course_id=None):
    resource = get_object_or_404(models.ResourceLink, course_id=course_id, slug=slug)
    if resource.lti:
        return lti_launch(request, resource)
    else:
        extra = request.GET.get("extra")
        return redirect(resource.url + (extra and extra or ""))


def lti_launch(request, resource):
    # LTI implementation guide:
    # https://www.imsglobal.org/specs/ltiv1p1p1/implementation-guide#table-of-contents
    #
    # Canvas "lti_outbound" implementation
    # https://github.com/instructure/canvas-lms/tree/master/gems/lti_outbound/lib/lti_outbound

    launch_url = resource.url
    user = request.user

    # Get (or create) the LTIUser instance.
    #
    # The `lti_user_id` field defaults to the hashid for the associated User instance, however this
    # is not guaranteed to always be the case. For example, if a user account has been merged, and
    # the primary key for that user has changed, this will allow that user to retain old LTI
    # account, since the originating ID cannot be changed in the LTI consumer.
    lti_user, created = models.LTIUser.objects.get_or_create(
        user=user, defaults={"lti_user_id": model_hashid(user)}
    )

    extra_params = {}

    if is_student(user):
        student = request.user.student
        email = f"{student.student_id}@{settings.STUDENT_USER_EMAIL_DOMAIN}"
        if student.user.email:
            # allow overriding email used for LTI if explicitly set on student's user record
            email = student.user.email
        roles = "urn:lti:role:ims/lis/Learner"
        roles = "Learner"
    else:
        email = user.email
        roles = "urn:lti:instrole:ims/lis/Observer,urn:lti:role:ims/lis/Mentor"
        volunteer = Volunteer.objects.get(user=user)
        clubs = get_volunteer_clubs(volunteer)
        students = Student.objects.filter(clubs__in=clubs)
        student_ids = [model_hashid(student.user) for student in students]
        extra_params["role_scope_mentor"] = ",".join(student_ids)

    if resource.course:
        extra_params["context_id"] = (
            f"Assignment-Course:{resource.course.pk}-{resource.slug}"
        )
    else:
        extra_params["context_id"] = f"Assignment-Global:{resource.slug}"

    resource_info = {
        "launch_url": launch_url,
        "user_id": lti_user.lti_user_id,
        "roles": roles,
    }

    resource_link_id = hashlib.sha256(json.dumps(resource_info).encode()).hexdigest()

    params = {
        "tool_consumer_instance_guid": "hive.boldidea.org",
        "lti_message_type": "basic-lti-launch-request",
        "lti_version": "LTI-1p0",
        "resource_link_id": resource_link_id,
        "resource_link_title": resource.name,
        "user_id": lti_user.lti_user_id,
        "roles": roles,
        "lis_person_name_given": user.get_first_name(),
        "lis_person_name_family": user.get_last_name(),
        "lis_person_name_full": user.get_full_name(),
        "lis_person_contact_email_primary": email,
    }

    params.update(extra_params)

    consumer = ToolConsumer(
        consumer_key=settings.CODIO_LTI_CONSUMER,
        consumer_secret=settings.CODIO_LTI_SECRET,
        launch_url=launch_url,
        params=params,
    )

    launch_data = consumer.generate_launch_data()

    context = {
        "launch_data_json": json.dumps(launch_data, indent=2),
        "launch_data": launch_data,
        "launch_url": consumer.launch_url,
        "auto_launch": settings.LTI_AUTO_LAUNCH,
    }

    return render(request, "courses/lti_launch.html", context)


@login_required
def survey(request, type):
    user = request.user

    if not is_student(user):
        context = {
            "type": type,
            "student_portal_url": f'{settings.SCHEME}://{settings.DOMAINS["student"]}',
        }
        return render(request, "courses/survey.html", context)

    grade_level = "ELEMENTARY"

    if is_student(user):
        student = user.student
        if student.grade >= 9:
            grade_level = "HIGH"
        elif student.grade >= 6:
            grade_level = "MIDDLE"
        else:
            grade_level = "ELEMENTARY"

    survey_link = get_object_or_404(
        StudentSurveyLink, type=type.upper(), grade_level=grade_level
    )

    return http.HttpResponseRedirect(survey_link.get_url(student))
