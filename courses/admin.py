import json
from urllib.parse import urlparse

from bitfield import <PERSON><PERSON>ield
from bitfield.admin import Bit<PERSON>ieldList<PERSON>ilter
from bitfield.forms import BitFieldCheckboxSelectMultiple
from django import forms
from django.contrib import admin
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from django.db.models import Text<PERSON>ield
from django.forms.widgets import Textarea
from django.urls import reverse
from django.utils.safestring import mark_safe
from django_json_widget.widgets import JSONEditorWidget

from clubs.admin_filters import (
    ClubCategoryFilter,
    ClubDrilldownFilter,
    ClubFilter,
    PeriodFilter,
)
from hive.admin import AdminMarkdownWidget, ArchivedFilter, register

from . import constants, models
from .badges import badgr


class ResourceLinkInline(admin.TabularInline):
    model = models.ResourceLink
    fields = ["order", "section", "name", "slug", "url", "visibility", "lti"]
    classes = ["collapse"]
    extra = 0
    formfield_overrides = {
        BitField: {"widget": BitFieldCheckboxSelectMultiple},
    }


class UnitInline(admin.TabularInline):
    model = models.Unit
    fields = ["number", "name"]
    classes = ["collapse"]
    extra = 0


class CourseFilter(ClubDrilldownFilter):
    title = "Course"
    parameter_name = "course"

    def __init__(self, request, params, model, model_admin):
        self.course_lookup = model_admin.course_filter_lookup
        super().__init__(request, params, model, model_admin)

    def lookups(self, request, model_admin):
        clubs = self._get_clubs(request)
        courses = models.Course.objects.filter(clubs__in=clubs).exclude(archived=True)
        return ((course.pk, course.name) for course in courses.distinct())

    def queryset(self, request, queryset):
        value = self.value()
        if value:
            return queryset.filter(**{self.course_lookup: self.value()})
        return queryset


@register(models.Course)
class CourseAdmin(admin.ModelAdmin):
    fields = [
        "order",
        "name",
        "photo",
        "description",
        "detailed_description",
        "course_selection_description",
        "coding_environment",
        "badges",
        "archived",
    ]
    autocomplete_fields = ["badges"]
    list_display = ["name", "order"]
    list_editable = ["order"]
    list_filter = [ArchivedFilter]
    exclude = ["learning_objective_sets"]
    search_fields = ["name"]
    inlines = [ResourceLinkInline, UnitInline]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget}}


class SectionInline(admin.TabularInline):
    model = models.Section
    fields = ["name", "number", "archived"]
    extra = 0


@register(models.Unit)
class UnitAdmin(admin.ModelAdmin):
    list_display = ["name", "course", "number", "archived"]
    search_fields = ["course__name", "name"]
    course_filter_lookup = "course"
    list_filter = [CourseFilter, ArchivedFilter]
    inlines = [SectionInline]


class StepInline(admin.TabularInline):
    model = models.Step
    fields = ["number", "type", "description", "archived"]
    extra = 0


@register(models.Section)
class SectionAdmin(admin.ModelAdmin):
    list_display = ["name", "unit", "number", "archived"]
    autocomplete_fields = ["unit"]
    course_filter_lookup = "unit__course"
    list_filter = [CourseFilter, "unit", ArchivedFilter]
    exclude = ["learning_objectives"]  # TODO
    inlines = [StepInline]


class LearningObjectiveInline(admin.TabularInline):
    model = models.LearningObjective
    fields = ["number", "description"]
    extra = 0

    formfield_overrides = {
        TextField: {"widget": Textarea(attrs={"rows": 1, "style": "width: 100%"})}
    }


@register(models.LearningObjectiveSet)
class LearningObjectiveSetAdmin(admin.ModelAdmin):
    list_display = ["name", "code", "number"]
    fields = ["name", "code", "number"]
    inlines = [LearningObjectiveInline]


class QuizQuestionInline(admin.StackedInline):
    model = models.QuizQuestion
    fields = ["question_number", "question_text", "choices", "correct_answers"]
    extra = 0


@register(models.Quiz)
class QuizAdmin(admin.ModelAdmin):
    inlines = [QuizQuestionInline]
    list_display = ["name"]
    fields = ["name", "slug"]
    prepopulated_fields = {"slug": ("name",)}


@register(models.ResourceLinkSection)
class ResourceLinkSectionAdmin(admin.ModelAdmin):
    list_display = ["name", "order"]
    list_editable = ["order"]


@register(models.ResourceLink)
class ResourceLinkAdmin(admin.ModelAdmin):
    list_display = ["name", "slug", "section", "order", "url"]
    list_editable = ["section", "order"]
    fields = ["section", "name", "slug", "order", "url", "visibility", "lti"]
    list_filter = ["section", ("visibility", BitFieldListFilter)]
    formfield_overrides = {
        BitField: {"widget": BitFieldCheckboxSelectMultiple},
    }

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(course=None).order_by("order")


class BadgeAdminForm(forms.ModelForm):
    name = forms.CharField()
    image = forms.ImageField(
        help_text="PNG format w/ transparent background recommended"
    )
    description = forms.CharField(widget=Textarea)
    earning_criteria = forms.CharField(
        required=False,
        widget=AdminMarkdownWidget,
        help_text=(
            "Detailed criteria for earning the badge. Students can see this info before badge is awarded."
        ),
    )
    criteria_url = forms.URLField(required=False)
    # use_narrative_template = forms.BooleanField(required=False)
    narrative_template_fields = forms.CharField(widget=Textarea, required=False)
    narrative_template = forms.CharField(widget=AdminMarkdownWidget, required=False)

    def __init__(self, *args, **kwargs):
        instance = kwargs.get("instance") or None
        initial_data = kwargs.get("initial") or {}

        if instance is not None:
            initial_data.update(
                {
                    "name": instance.data["name"],
                    "description": instance.data["description"],
                    "earning_criteria": instance.data["criteria"].get("narrative"),
                    "criteria_url": instance.data["criteria"].get("id"),
                }
            )

            if "extensions:narrativeTemplate" in instance.data:
                evidence_template = instance.data["extensions:narrativeTemplate"].get(
                    "evidenceTemplate", {}
                )
                initial_data.update(
                    {
                        "narrative_template": evidence_template.get("template", ""),
                        "narrative_template_fields": json.dumps(
                            evidence_template.get("fields", [])
                        ),
                    }
                )
        else:
            # Set default narrative template fields
            initial_data["narrative_template_fields"] = json.dumps(
                constants.BADGE_DEFAULT_NARRATIVE_TEMPLATE_FIELDS
            )
            initial_data["narrative_template"] = (
                constants.BADGE_DEFAULT_NARRATIVE_TEMPLATE.strip()
            )

        kwargs["initial"] = initial_data

        super().__init__(*args, **kwargs)

        if instance is not None:
            # Make image field optional, as we don't have an "initial" value to give the form
            self.fields["image"].required = False

        # Add help text for narrative template fieldsc
        markdown_help_url = "https://commonmark.org/help/"
        tpl_help_url = "https://docs.djangoproject.com/en/3.2/ref/templates/language/"

        self.fields["narrative_template"].help_text = (
            "<p>This text will be shown in the narrative for a badge once it is awarded. When "
            "awarding a badge in Hive, mentors will be presented with the fields provided below, "
            "such as <em>club</em>, <em>mentor name</em>, and <em>mentor notes</em>. The template "
            "provided above will be populated with the values the mentor provides for these "
            "fields.</p>"
            "<p>You may also leave this field entierly blank, which will allow the mentor to "
            "supply the full narrative.</p>"
            f'</p><a href="{markdown_help_url}" target="_blank">Markdown syntax</a> supported.<br>'
            "Use <code>{{field_name}}</code> to reference a template field. <br>"
            f'<a href="{tpl_help_url}" target="_blank">Django template syntax</a> is also '
            "supported.</p>"
        )

        self.fields["narrative_template_fields"].help_text = (
            "These fields will be displayed in the form used to award badges. Once that form is submitted, the values "
            "are inserted into the narrative template and used for the final badge narrative."
        )


@register(models.Badge)
class BadgeAdmin(admin.ModelAdmin):
    list_display = ["_name", "order", "_url_short"]
    list_editable = ["order"]
    list_filter = [CourseFilter]
    search_fields = ["data__name"]
    course_filter_lookup = "courses"
    fieldsets = (
        (
            "General info",
            {"fields": ("name", "image", "description", "earning_criteria")},
        ),
        (
            "Evidence narrative template",
            {"fields": ("narrative_template", "narrative_template_fields")},
        ),
        (
            "OpenBadge data",
            {
                "classes": ("collapse", "open"),
                "fields": (
                    "_url",
                    "data",
                ),
            },
        ),
    )

    readonly_fields = ["_url"]

    form = BadgeAdminForm

    formfield_overrides = {
        JSONField: {
            "widget": JSONEditorWidget(options={"modes": ["view"], "mode": "view"})
        }
    }

    def _name(self, obj):
        return obj.data and obj.data.get("name") or "(no data)"

    def _url_short(self, obj):
        host = urlparse(obj.openbadge_url).netloc
        tld = ".".join(host.split(".")[-2:])
        return mark_safe(
            f'<a href="{obj.openbadge_url}" target="_blank">View on {tld}</a>'
        )

    _url_short.short_description = "Link"

    def _url(self, obj):
        return mark_safe(
            f'<a href="{obj.openbadge_url}" target="_blank">{obj.openbadge_url}</a>'
        )

    def get_fieldsets(self, request, obj=None):
        fieldsets = list(super().get_fieldsets(request, obj))
        if not obj:
            del fieldsets[2]
        return fieldsets

    def save_model(self, request, obj, form, change):
        # if "name" field is in form data, we're updating badge details,
        # otherwise, the update may be from the list view
        if "name" in form.cleaned_data:
            # Push the badge to Badgr
            badgeclass_definition = badgr.build_badgeclass(obj, form.cleaned_data)
            response = badgr.save_badgeclass(badgeclass_definition)
            obj.openbadge_url = response.result[0]["openBadgeId"]

            # Update local openbadge data field
            obj.update_openbadge_data()

        super().save_model(request, obj, form, change)


class BadgeFilter(ClubDrilldownFilter):
    title = "Badge"
    parameter_name = "badge"
    club_lookup = "badge__courses__clubs"

    def _get_lookup(self):
        return "badge"

    def lookups(self, request, model_admin):
        clubs = self._get_clubs(request)
        if request.GET.get("course"):
            courses = models.Course.objects.filter(pk=request.GET["course"])
        else:
            courses = models.Course.objects.filter(clubs__in=clubs).exclude(
                archived=True
            )
        badges = models.Badge.objects.filter(courses__in=courses)
        return ((badge.pk, str(badge)) for badge in badges.distinct())


@register(models.BadgeAssertion)
class BadgeAssertionAdmin(admin.ModelAdmin):
    list_display = ["__str__", "student", "badge"]
    search_fields = ["student__first_name__unaccent", "student__last_name__unaccent"]
    course_filter_lookup = "badge__courses"
    list_filter = [
        PeriodFilter,
        ClubCategoryFilter.factory("student_registration__club"),
        ClubFilter.factory("student_registration__club"),
        CourseFilter,
        BadgeFilter,
    ]
    fields = [
        "_student",
        "_badge",
        "created",
        "_openbadge_url",
        "added_by",
        "_sreg",
        "data",
    ]
    readonly_fields = [f for f in fields if f != "data"]

    formfield_overrides = {
        JSONField: {
            "widget": JSONEditorWidget(options={"modes": ["view"], "mode": "view"})
        }
    }

    def has_add_permission(self, request, obj=None):
        return False

    def _student(self, obj):
        student = obj.student
        url = reverse("admin:students_student_change", kwargs={"object_id": student.pk})
        return mark_safe(f'<a href="{url}">{student}</a>')

    def _badge(self, obj):
        badge = obj.badge
        url = reverse("admin:courses_badge_change", kwargs={"object_id": badge.pk})
        return mark_safe(f'<a href="{url}">{badge}</a>')

    def _openbadge_url(self, obj):
        return mark_safe(
            f'<a href="{obj.openbadge_url}" target="_blank">{obj.openbadge_url}</a>'
        )

    def _sreg(self, obj):
        if obj.student_registration:
            sreg = obj.student_registration
            url = reverse(
                "admin:clubs_studentregistration_change", kwargs={"object_id": sreg.pk}
            )
            return mark_safe(f'<a href="{url}">{sreg}</a>')

    _sreg.short_description = "Student registration"
