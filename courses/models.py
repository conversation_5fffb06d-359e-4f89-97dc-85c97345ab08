import time
from datetime import timed<PERSON><PERSON>

import requests
import vinaigrette
from bitfield import <PERSON><PERSON>ield
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from django.core.validators import validate_comma_separated_integer_list
from django.db import models
from django.template.defaultfilters import slugify
from django.urls import reverse
from django.utils import timezone

from clubs.query import PeriodQuerySet
from students.models import Student
from users.models import User

from . import constants


class LearningObjectiveSet(models.Model):
    number = models.PositiveIntegerField()
    name = models.CharField(max_length=64)
    code = models.SlugField()

    class Meta:
        ordering = ["number"]

    def __str__(self):
        return self.name


class LearningObjective(models.Model):
    set = models.ForeignKey(LearningObjectiveSet, on_delete=models.CASCADE)
    number = models.PositiveIntegerField()
    description = models.TextField()

    class Meta:
        ordering = ["set__number", "number"]

    @property
    def code(self):
        return f"{self.set.code}-{self.number:02}"

    def __str__(self):
        return f"{self.code} {self.description}"


class CourseQuerySet(models.QuerySet):
    def not_archived(self):
        return self.exclude(archived=True)


class Course(models.Model):
    photo = models.ImageField(upload_to="courses/course_photos", blank=True)
    name = models.CharField(max_length=64)
    description = models.TextField()
    detailed_description = models.TextField(blank=True)
    course_selection_description = models.TextField(
        blank=True,
        help_text=(
            'Shown on "select course" screen to help students/mentors choose an appropriate course'
        ),
    )
    learning_objective_sets = models.ManyToManyField(LearningObjectiveSet, blank=True)
    coding_environment = models.CharField(
        choices=constants.CODING_ENVIRONMENT_CHOICES, max_length=32, blank=True
    )
    archived = models.BooleanField(default=False)
    badges = models.ManyToManyField("Badge", related_name="courses", blank=True)
    ide_config = JSONField(blank=True, default=dict)
    order = models.IntegerField(default=0)

    objects = CourseQuerySet.as_manager()

    class Meta:
        ordering = ["order"]

    def __str__(self):
        return self.name

    @property
    def coding_environment_url(self):
        from . import utils

        return utils.get_coding_environment_url(self.coding_environment)

    @property
    def roadmap_steps(self):
        return Step.objects.filter(section__unit__course=self).select_related(
            "section", "section__unit"
        )


vinaigrette.register(
    Course,
    ["description", "detailed_description"],
    restrict_to=models.Q(archived=False),
)


class ResourceLinkSection(models.Model):
    name = models.CharField(max_length=64)
    order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ["order", "name"]


class BaseResourceLink(models.Model):
    section = models.ForeignKey(
        ResourceLinkSection,
        on_delete=models.PROTECT,
        related_name="%(app_label)s_links",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=64)
    slug = models.SlugField(blank=True, null=True)
    order = models.PositiveIntegerField(default=0)
    url = models.CharField(blank=True, max_length=255)
    visibility = BitField(flags=(("MENTORS", "Mentors"), ("STUDENTS", "Students")))
    lti = models.BooleanField("LTI link", default=False)

    class Meta:
        abstract = True
        ordering = ["section", "order"]

    def __str__(self):
        return self.name


class ResourceLink(BaseResourceLink):
    course = models.ForeignKey(
        Course,
        related_name="resource_links",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    @property
    def permalink(self):
        kwargs = {"slug": self.slug}
        if self.course:
            kwargs["course_id"] = self.course.id
        return reverse("courses:resource-link", kwargs=kwargs)

    class Meta:
        unique_together = ["course", "slug"]


class UnitQuerySet(models.QuerySet):
    def not_archived(self):
        return self.exclude(archived=True).exclude(course__archived=True)


class Unit(models.Model):
    course = models.ForeignKey(Course, related_name="units", on_delete=models.CASCADE)
    number = models.PositiveIntegerField()
    name = models.CharField(max_length=255)
    archived = models.BooleanField(default=False)

    objects = UnitQuerySet.as_manager()

    class Meta:
        ordering = ["number"]

    def __str__(self):
        return f"{self.course} / {self.name}"


class SectionQuerySet(models.QuerySet):
    def not_archived(self):
        return (
            self.exclude(archived=True)
            .exclude(unit__archived=True)
            .exclude(unit__course__archived=True)
        )


class Section(models.Model):
    unit = models.ForeignKey(Unit, related_name="sections", on_delete=models.CASCADE)
    number = models.PositiveIntegerField()
    name = models.CharField(max_length=255)
    learning_objectives = models.ManyToManyField(LearningObjective, blank=True)
    archived = models.BooleanField(default=False)

    objects = SectionQuerySet.as_manager()

    class Meta:
        ordering = ["unit__number", "number"]

    def __str__(self):
        return f"{self.unit} / {self.name}"


class StepQuerySet(models.QuerySet):
    def not_archived(self):
        return (
            self.exclude(archived=True)
            .exclude(section__archived=True)
            .exclude(section__unit__archived=True)
            .exclude(section__unit__course__archived=True)
        )


class Step(models.Model):
    section = models.ForeignKey(Section, related_name="steps", on_delete=models.CASCADE)
    number = models.PositiveIntegerField()
    type = models.SlugField(max_length=16, blank=True)
    description = models.TextField()
    archived = models.BooleanField(default=False)

    objects = StepQuerySet.as_manager()

    class Meta:
        ordering = ["section__unit__number", "section__number", "number"]

    def __str__(self):
        return f"{self.section} / Step #{self.number}"


class UserRoadmap(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(
        User, related_name="course_roadmaps", on_delete=models.PROTECT
    )
    course = models.ForeignKey(
        Course, related_name="user_roadmaps", on_delete=models.PROTECT
    )

    @property
    def summary(self):
        units = []
        all_steps = Step.objects.not_archived().filter(
            section__unit__course=self.course
        )
        completed_steps = self.completed_steps.not_archived().order_by(
            "step__section__unit__number", "step__section__number", "step__number"
        )
        last_completion = (
            completed_steps.count()
            and completed_steps.order_by(
                "-step__section__unit__number",
                "-step__section__number",
                "-step__number",
            )[0]
            or None
        )
        completed_step_ids = set(s.step_id for s in completed_steps)
        active_unit = None
        for unit in self.course.units.not_archived():
            unit_completed_steps = set(
                s.step for s in completed_steps if s.step.section.unit == unit
            )
            num_completed = len(unit_completed_steps)
            total_steps = all_steps.filter(section__unit=unit).count()

            percent_complete = (
                total_steps > 0 and round((num_completed / total_steps) * 100) or 0
            )
            active = False
            if active_unit is None and percent_complete < 100:
                active_unit = unit
                active = True
            units.append(
                {
                    "name": unit.name,
                    "completed_steps": num_completed,
                    "total_steps": total_steps,
                    "percent_complete": percent_complete,
                    "sections": unit.sections.all(),
                    "active": active,
                }
            )

        if len(units) > 0 and active_unit is None:
            units[0]["active"] = True

        return {
            "units": units,
            "last_completion": last_completion,
            "completed_steps": completed_step_ids,
            "percent_complete": all_steps.count() > 0
            and round((len(completed_step_ids) / all_steps.count()) * 100)
            or 0,
        }

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.course}"


class UserRoadmapCompletedStepQuerySet(models.QuerySet):
    def not_archived(self):
        return (
            self.exclude(step__archived=True)
            .exclude(step__section__archived=True)
            .exclude(step__section__unit__archived=True)
            .exclude(step__section__unit__course__archived=True)
        )


class UserRoadmapCompletedStep(models.Model):
    roadmap = models.ForeignKey(
        UserRoadmap, related_name="completed_steps", on_delete=models.PROTECT
    )
    step = models.ForeignKey(Step, related_name="completions", on_delete=models.PROTECT)
    completed_date = models.DateTimeField(auto_now_add=True)

    objects = UserRoadmapCompletedStepQuerySet.as_manager()


class StudentSelfAssessment(models.Model):
    student = models.ForeignKey(
        Student, related_name="self_assessments", on_delete=models.CASCADE
    )
    learning_objective = models.ForeignKey(LearningObjective, on_delete=models.PROTECT)
    rating = models.PositiveIntegerField()


class Quiz(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField()

    class Meta:
        verbose_name_plural = "quizzes"

    def get_student_latest(self, student):
        student_tickets = StudentQuiz.objects.filter(student=student, quiz=self)
        if student_tickets.count() > 0:
            return student_tickets.order_by("-date")[0]
        return None

    def get_student_best(self, student):
        student_tickets = StudentQuiz.objects.filter(student=student, quiz=self)
        if student_tickets.count() > 0:
            return student_tickets.order_by("-num_correct", "-date")[0]
        return None

    def can_retake(self, student):
        # Students must wait 22 hours before re-taking
        time_ago = timezone.now() - timedelta(hours=22)
        recent_tickets = StudentQuiz.objects.filter(
            student=student, quiz=self, date__gte=time_ago
        )
        return recent_tickets.count() == 0

    def can_view_answers(self, student):
        # If students got 100%, they can view the answers at any time
        best = self.get_student_best(student)
        if best and best.score == 100:
            return True

        # Otherwise, students can only view answers within 10 hours of taking the quiz
        time_ago = timezone.now() - timedelta(hours=10)
        recent_tickets = StudentQuiz.objects.filter(
            student=student, quiz=self, date__gte=time_ago
        )
        if recent_tickets.count() > 0:
            if recent_tickets.latest("date").score == 100:
                return True
        return recent_tickets.count() > 0

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)

        # update order fields
        for i, question in enumerate(self.questions.all()):
            question.order = i + 1
            question.save()
        super().save(*args, **kwargs)

    def build_results(self, answers, team=None):
        # FIXME: good god this is ugly
        """
        Returns answers in same format as StudentQuiz, to be rendered with
        `courses/includes/quiz_results.html`. If `team` is passed as an array of students,
        team stats will be calculated in addition to global stats.
        """
        results = []
        answers = list(answers)

        # Calculate stats
        global_results = {}
        team_results = {}

        for question in self.questions.all():
            global_results[question.id] = {"correct": 0, "answered": 0}
            if team:
                team_results[question.id] = {"correct": 0, "answered": 0}

        def get_percent(n, d):
            return d and round((n / d) * 100) or 0

        def update_result_choices(result, student_ticket):
            student_result = [
                r
                for r in student_ticket.results
                if r["question_id"] == result["question_id"]
            ][0]
            student_choices = sorted(
                student_result["choices"], key=lambda c: c["number"]
            )

            for i, choice in enumerate(result["choices"]):
                if not choice.get("global_result"):
                    choice["global_result"] = {"count": 0, "percent": 0}
                if not choice.get("team_result"):
                    choice["team_result"] = {"count": 0, "percent": 0}

                student_choice = student_choices[i]
                if student_choice["selected"]:
                    choice["global_result"]["count"] += 1
                    choice["global_result"]["percent"] = get_percent(
                        choice["global_result"]["count"],
                        global_results[result["question_id"]]["answered"],
                    )
                    if student_ticket.student in team:
                        choice["team_result"]["count"] += 1
                        choice["team_result"]["percent"] = get_percent(
                            choice["team_result"]["count"],
                            team_results[result["question_id"]]["answered"],
                        )

        team = team or []
        for student_ticket in self.student_results.all():
            for result in student_ticket.results:
                global_results[result["question_id"]]["answered"] += 1
                if student_ticket.student in team:
                    team_results[result["question_id"]]["answered"] += 1
                if result["is_correct"]:
                    global_results[result["question_id"]]["correct"] += 1
                    if student_ticket.student in team:
                        team_results[result["question_id"]]["correct"] += 1

        # calculate percentages
        for r in global_results.values():
            r["percent"] = get_percent(r["correct"], r["answered"])
        for r in team_results.values():
            r["percent"] = get_percent(r["correct"], r["answered"])

        for i, question in enumerate(self.questions.all()):
            result = question.build_result(answers[i])
            result["global_results"] = global_results[question.id]
            if team_results:
                result["team_results"] = team_results[question.id]
            for student_ticket in self.student_results.all():
                update_result_choices(result, student_ticket)
            results.append(result)

        return results

    def __str__(self):
        return self.name


class QuizQuestion(models.Model):
    quiz = models.ForeignKey(Quiz, related_name="questions", on_delete=models.PROTECT)
    question_number = models.IntegerField()
    question_text = models.TextField(help_text="Markdown supported")
    choices = models.TextField(blank=True, help_text="One choice per line")
    correct_answers = models.CharField(
        validators=[validate_comma_separated_integer_list],
        max_length=32,
        help_text="Comma-separated numbers (1-n)",
    )

    class Meta:
        unique_together = ["quiz", "question_number"]
        ordering = ["quiz", "question_number"]

    @property
    def choices_list(self):
        return self.choices.strip().splitlines()

    @property
    def multiple_choice(self):
        return len(self.correct_answers.split(",")) > 1

    @property
    def correct_answers_set(self):
        return set(int(n.strip()) for n in self.correct_answers.split(","))

    def build_result(self, answer):
        choices = []
        answer = set(answer)

        for c, choice in enumerate(self.choices_list):
            choice_num = c + 1
            choices.append(
                {
                    "number": choice_num,
                    "text": choice.strip(),
                    "correct": choice_num in self.correct_answers_set,
                    "selected": choice_num in answer,
                }
            )

        return {
            "is_correct": (answer == self.correct_answers_set),
            "question_id": self.id,
            "question_number": self.question_number,
            "question_text": self.question_text,
            "choices": choices,
        }


class StudentQuiz(models.Model):
    date = models.DateTimeField(default=timezone.now)
    student = models.ForeignKey(
        Student, related_name="quiz_results", on_delete=models.CASCADE
    )
    quiz = models.ForeignKey(
        Quiz, related_name="student_results", on_delete=models.PROTECT
    )
    num_questions = models.IntegerField()
    num_correct = models.IntegerField()
    results = JSONField()

    @property
    def score(self):
        return int(round(self.num_correct / self.num_questions, 2) * 100)

    @property
    def can_retake(self):
        return self.quiz.can_retake(self.student)

    @property
    def can_view_answers(self):
        return self.quiz.can_view_answers(self.student)


class LTIUser(models.Model):
    """
    Represents a user in an LTI consumer such as Codio.

    The `lti_user_id` field defaults to the hashid for the associated User instance (see
    `views.lti_launch`), however it is not guaranteed to always match. For example, if a
    user account has been merged and the new record is kept (which means and the primary key for
    that user has changed), we may want to allow that user to retain old LTI account, since the
    originating ID cannot be changed in the LTI consumer.
    """

    user = models.OneToOneField(User, related_name="lti_user", on_delete=models.CASCADE)
    lti_user_id = models.CharField(max_length=255, unique=True)


class Badge(models.Model):
    openbadge_url = models.URLField()
    data = JSONField(default=dict)
    order = models.PositiveIntegerField(default=0)

    def update_openbadge_data(self, throttle=True):
        """
        Pulls the JSON data from the current `openbadge_url` into the `data` field
        """
        headers = {"Content-Type": "application/json"}
        res = requests.get(self.openbadge_url, headers=headers)
        if throttle:
            time.sleep(1)
        res.raise_for_status()
        self.data = res.json()

    @property
    def image(self):
        if image := self.data.get("image"):
            if isinstance(image, dict):
                return image.get("id")
            return image

    def __str__(self):
        return self.data and self.data.get("name") or "(no data)"

    class Meta:
        ordering = ["order"]


class BadgeAssertion(models.Model):
    student = models.ForeignKey(
        Student, related_name="badge_assertions", on_delete=models.PROTECT
    )
    badge = models.ForeignKey(
        Badge, related_name="assertions", on_delete=models.PROTECT
    )
    created = models.DateTimeField()
    openbadge_url = models.URLField()
    added_by = models.ForeignKey(
        User,
        related_name="issued_badge_assertions",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )
    student_roadmap = models.ForeignKey(
        UserRoadmap,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="badge_assertions",
    )
    student_registration = models.ForeignKey(
        "clubs.StudentRegistration",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name="badge_assertions",
    )
    data = JSONField(default=dict)

    objects = PeriodQuerySet.as_manager("student_registration__club")

    class Meta:
        unique_together = ("student", "badge")

    def update_openbadge_data(self, throttle=True):
        """
        Pulls the JSON data from the current `openbadge_url` into the `data` field
        """
        headers = {"Content-Type": "application/json"}
        res = requests.get(self.openbadge_url, headers=headers)
        if throttle:
            time.sleep(1)
        res.raise_for_status()
        self.data = res.json()
        self.created = self.data["issuedOn"]

    @property
    def image(self):
        if image := self.data.get("image"):
            if isinstance(image, dict):
                return image.get("id")
            return image

    def __str__(self):
        return f"{self.student} - {self.badge}"
