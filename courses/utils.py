import importlib

from django.conf import settings
from django.urls import reverse


def get_coding_environment_url(environment):
    if environment == "IDE":
        return reverse("ide:projects")
    if environment == "WORKSPACES":
        return reverse("student_portal:workspace")
    if environment == "REPLIT":
        return "https://repl.it/"
    if environment == "GLITCH":
        return "https://glitch.com/signin"
    if environment == "CODIO":
        return "https://codio.com"
    return ""


def get_course_handler():
    modname, clsname = settings.COURSE_HANDLER.rsplit(".", 1)
    mod = importlib.import_module(modname)
    if not hasattr(mod, clsname):
        raise ImportError(f"Cannot find import name '{clsname}' from '{modname}'.")
    return getattr(mod, clsname)()
