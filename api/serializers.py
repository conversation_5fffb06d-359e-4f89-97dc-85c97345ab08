from rest_framework.relations import HyperlinkedRelatedField


class NamespacedUrlFieldMixin:
    # Allows namespaced urls to work on HyperlinkedModelSerializer
    url_namespace = None

    def build_field(self, field_name, *args, **kwargs):
        field_class, field_kwargs = super().build_field(field_name, *args, **kwargs)
        if self.url_namespace and issubclass(field_class, HyperlinkedRelatedField):
            field_kwargs["view_name"] = (
                f'{self.url_namespace}:{field_kwargs["view_name"]}'
            )
        return field_class, field_kwargs
