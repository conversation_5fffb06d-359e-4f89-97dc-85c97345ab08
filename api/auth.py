import logging
import re

from django.contrib.auth.models import AnonymousUser
from rest_framework import authentication, exceptions

from .models import ServiceAccount

logger = logging.getLogger(__name__)


class ServiceAccountUser(AnonymousUser):
    def __init__(self, service_account, *args, **kwargs):
        self.service_account = service_account
        super().__init(*args, **kwargs)

    @property
    def is_authenticated(self):
        # Always return True. This is a way to tell if
        # the user has been authenticated in permissions
        return True


class ServiceAccountAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.META.get("HTTP_AUTHORIZATION")
        if auth_header is None:
            logger.debug("No authorization header")
            return None

        match = re.match(r"Token (\w+)$", auth_header)
        if not match:
            logger.debug("No auth token")
            return None

        try:
            token_str = match.group(1)
        except IndexError:
            logger.debug("No token")
            return None

        try:
            service_account = ServiceAccount.objects.get(token=token_str)
        except ServiceAccount.DoesNotExist:
            logger.debug(f"Invalid token (does not exist: {token_str})")
            raise exceptions.AuthenticationFailed("Invalid token")

        user = ServiceAccountUser(service_account)

        return (user, None)
