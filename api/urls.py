from django.conf.urls import include, url
from rest_framework_nested import routers

from . import views

app_name = "api"
base_router = routers.SimpleRouter()  # FIXME: is this still needed?

# TODO: autodiscover */api/urls.py

urlpatterns = [
    url(r"^", include(base_router.urls)),
    url(r"^", include("clubs.api.urls")),
    url(r"^", include("volunteers.api.urls")),
    url(r"^$", views.api_root, name="root"),
    url(r"^health-check/$", views.health_check, name="health-check"),
    url(r"^sso/", include("sso.api.urls")),
    url(r"^studio/", include("studio.api.urls")),
    url(r"^users/", include("users.api.urls")),
]
