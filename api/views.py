from rest_framework import exceptions, viewsets
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import exception_handler as drf_exception_handler


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def api_root(request):
    # TODO: This is here just so we can reverse() the API root. In the future, we may want to
    # enumerate available endpoints
    return Response({})


@api_view(["GET"])
def health_check(request):
    return Response({"status": "ok"})


def api_exception_handler(exc, context):
    response = drf_exception_handler(exc, context)
    if response is None:
        return response

    error_code = exc.__class__.__name__

    if isinstance(exc, exceptions.ValidationError):
        errors = response.data
        response.data = {"error_code": error_code, "detail": errors}
    else:
        response.data["error_code"] = error_code

    return response


class NestedModelViewSet(viewsets.ModelViewSet):
    parent = None
    parent_fk = None
    parent_kwarg = None

    def __init__(self, *args, **kwargs):
        if self.parent and self.parent_fk is None:
            self.parent_fk = self.parent + "_id"
        if self.parent and self.parent_kwarg is None:
            self.parent_kwarg = self.parent + "_pk"
        super().__init__(*args, **kwargs)

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.parent:
            parent_pk = self.kwargs[self.parent_kwarg]
            return queryset.filter(**{self.parent_fk: parent_pk})
        return queryset

    def create(self, request, *args, **kwargs):
        request.data[self.parent_fk] = kwargs[self.parent_kwarg]
        return super().create(request, *args, **kwargs)
