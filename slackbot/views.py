import logging

from django.conf import settings
from django.http import HttpResponse
from slack import WebClient
from slack_utils.decorators import slack_receiver

logger = logging.getLogger(__name__)
client = WebClient(token=settings.SLACK_ACCESS_TOKEN)


@slack_receiver("team_join")
def on_team_join(event, **kwargs):
    user_id = event.get("user")
    user_res = client.users_info(user=user_id)
    user = user_res.data["user"]
    message = (
        f"Hey @{user['real_name']}! Welcome to Bold Idea! We're glad to have you on the team. "
        "Tell us about yourself! :boldidea:"
    )
    client.chat_postMessage(channel="#general", text=message)
    return HttpResponse(status=200)
