from quickbooks import objects
from quickbooks.batch import batch_delete

from billing import qb


def run():
    client = qb.get_client()
    deleted = 1

    while deleted > 0:
        payments = objects.Payment.all(qb=client)
        result = batch_delete(payments, qb=client)

        deleted = 0
        for success in result.successes:
            deleted += 1

        print("Deleted {} Payments".format(deleted))
        for fault in result.faults:
            print(fault.Error[0])
