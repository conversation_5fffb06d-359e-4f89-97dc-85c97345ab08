import sqlite3
import time
from datetime import date
from decimal import Decimal

from django.conf import settings
from quickbooks import objects

from billing import qb


def run():
    fy19_start = time.mktime(date(2018, 7, 1).timetuple())
    conn = sqlite3.connect("payments.db")
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    client = qb.get_client()
    total_fy19 = Decimal("0.00")
    total_prev = Decimal("0.00")
    total = Decimal("0.00")
    c.execute("SELECT * FROM payments")
    payments = list(c.fetchall())

    for i, payment in enumerate(payments):
        amount = payment["amount"] / 100
        total += Decimal(str(amount))

        if payment["datestamp"] < fy19_start:
            # Payment was already deposited into pre-FY19 balance
            # Add previous payments as description-only line items and reduce balance
            total_prev += Decimal(str(amount))
        else:
            # Add payment as an actual payment to be depostied
            payment_acct = settings.INVOICE_PAYMENT_ACCT_ID
            total_fy19 += Decimal(str(amount))

            qb_payment = objects.Payment()
            qb_payment.TotalAmt = amount
            qb_payment.PaymentRefNum = payment["ref"]

            qb_payment.CustomerRef = objects.Ref()
            qb_payment.CustomerRef.value = payment["qbo_customer_id"]

            if payment_acct is not None:
                qb_payment.DepositToAccountRef = objects.Ref()
                qb_payment.DepositToAccountRef.value = payment_acct

            qb_payment_line = objects.PaymentLine()
            qb_payment_line.Amount = amount
            qb_payment_line.LinkedTxn = objects.LinkedTxn()
            qb_payment_line.LinkedTxn.TxnId = payment["qbo_invoice_id"]
            qb_payment_line.LinkedTxn.TxnType = "Invoice"
            qb_payment.Line = [qb_payment_line]
            try:
                qb_payment.save(qb=client)
            except Exception as e:
                import ipdb

                ipdb.set_trace()
                raise
        print("{}/{}".format(i + 1, len(payments)))
        time.sleep(1.02)

    print("\nTotals\n------")
    print("FY19:  {}".format(total_fy19))
    print("Prev:  {}".format(total_prev))
    print("Total: {}".format(total))
