from quickbooks.objects.customer import Customer

from accounts.models import Account
from billing import qb


def run():
    client = qb.get_client()

    customers = Customer.all(max_results=1000, qb=client)

    for customer in customers:
        if customer.ResaleNum:
            try:
                account = Account.objects.get(pk=customer.ResaleNum)
            except (Account.DoesNotExist, ValueError):
                continue
            account.billing_id = customer.Id
            account.save()
