import json
import logging
import tempfile
import time
import uuid
from datetime import datetime

from dict2xml import dict2xml
from django.conf import settings
from django.utils import timezone
from quickbooks import objects

from billing import qb
from hive.xero import client as xero

logger = logging.getLogger(__name__)


client = qb.get_client()
existing_invoices = {}
batch_to_invoice = {}
invoice_to_customer = {}

exclude_statuses = ("VOIDED", "DELETED", "DRAFT")


def normalize_inum(invoice_number):
    if "-" in invoice_number:
        invoice_number = invoice_number.split("-")[1]
    return str(int(invoice_number))


def chunks(items, n):
    for i in range(0, len(items), n):
        yield items[i : i + n]


def invoice2xml(invoice):
    contents = dict2xml(invoice)
    return '<?xml version="1.0" encoding="UTF-8"?>\n<Invoice>\n{}\n</Invoice>'.format(
        contents
    )


def import_invoice(invoice):
    # Skip voided invoices
    if invoice["Status"] in exclude_statuses:
        return

    customer_id = invoice_to_customer.get(invoice["InvoiceID"])
    if not customer_id:
        logger.error("No customer for {}".format(invoice["InvoiceNumber"]))
        return

    # Get full invoice from Xero
    invoice = xero.invoices.get(invoice["InvoiceID"])[0]

    qb_line_items = []
    for xero_line in invoice["LineItems"]:
        qb_line = objects.SalesItemLine()
        qb_line.Description = xero_line["Description"]
        qb_line.Amount = xero_line["LineAmount"]
        qb_line.SalesItemLineDetail = objects.SalesItemLineDetail()
        qb_line.SalesItemLineDetail.ServiceDate = invoice["Date"].strftime("%Y-%m-%d")
        qb_line.SalesItemLineDetail.ItemRef = objects.Ref()
        if xero_line.get("AccountCode") == "5181":
            # Financial assistance discount
            qb_line.SalesItemLineDetail.ItemRef.value = (
                settings.PROGRAM_FEE_DISCOUNT_ITEM_ID
            )
        else:
            # Program fee
            qb_line.SalesItemLineDetail.ItemRef.value = settings.PROGRAM_FEE_ITEM_ID
        qb_line_items.append(qb_line)

    # If this invoice has payments prior to FY19, add payments as line items to reduce the balance
    for payment in invoice.get("Payments", []):
        if payment["Date"] < datetime(2018, 7, 1, 0, 0, 0):
            qb_payment_line = objects.SalesItemLine()
            qb_payment_line.Description = "Payment"
            if payment.get("Ref"):
                qb_payment_line.Description += " (ref: {})".format(payment["Ref"])
            qb_payment_line.Amount = payment["Amount"] * -1
            qb_payment_line.SalesItemLineDetail = objects.SalesItemLineDetail()
            qb_payment_line.SalesItemLineDetail.ServiceDate = payment["Date"].strftime(
                "%Y-%m-%d"
            )
            qb_line_items.append(qb_payment_line)

    # Same for credit notes
    for i, cn in enumerate(invoice.get("CreditNotes", [])):
        if cn["Date"] < datetime(2018, 7, 1, 0, 0, 0):
            xero_cn = xero.creditnotes.get(cn["CreditNoteID"])[0]
            invoice["CreditNotes"][i] = xero_cn
            cn_date = xero_cn["Date"].strftime("%Y-%m-%d")
            # use first description of credit note for the allocation
            desc = xero_cn["LineItems"][0]["Description"]
            for alloc in xero_cn["Allocations"]:
                qb_payment_line = objects.SalesItemLine()
                qb_payment_line.Description = "Credit Note: {}".format(desc)
                qb_payment_line.Amount = alloc["Amount"] * -1
                qb_payment_line.SalesItemLineDetail = objects.SalesItemLineDetail()
                qb_payment_line.SalesItemLineDetail.ServiceDate = cn_date
                qb_line_items.append(qb_payment_line)

    # Same for overpayments
    for i, op in enumerate(invoice.get("Overpayments", [])):
        if op["Date"] < datetime(2018, 7, 1, 0, 0, 0):
            xero_op = xero.overpayments.get(op["OverpaymentID"])[i]
            invoice["Overpayments"][i] = xero_op
            op_date = xero_op["Date"].strftime("%Y-%m-%d")
            # use first description of credit note for the allocation
            desc = xero_op["LineItems"][0]["Description"]
            for alloc in xero_op["Allocations"]:
                qb_payment_line = objects.SalesItemLine()
                qb_payment_line.Description = "Overpayment: {}".format(desc)
                qb_payment_line.Amount = alloc["Amount"] * -1
                qb_payment_line.SalesItemLineDetail = objects.SalesItemLineDetail()
                qb_payment_line.SalesItemLineDetail.ServiceDate = op_date
                qb_line_items.append(qb_payment_line)

    qb_invoice = objects.Invoice()
    qb_invoice.DocNumber = normalize_inum(invoice["InvoiceNumber"])
    qb_invoice.TxnDate = invoice["Date"].strftime("%Y-%m-%d")
    qb_invoice.DueDate = invoice["DueDate"].strftime("%Y-%m-%d")
    qb_invoice.Line = qb_line_items
    qb_invoice.CustomerRef = objects.Ref()
    qb_invoice.CustomerRef.value = customer_id

    try:
        qb_invoice.save(qb=client)
    except Exception as e:
        import ipdb

        ipdb.set_trace()
        raise

    qb_attachment_ref = objects.AttachableRef()
    qb_attachment_ref.EntityRef = objects.Ref()
    qb_attachment_ref.EntityRef.type = "Invoice"
    qb_attachment_ref.EntityRef.value = qb_invoice.Id

    # Attach full invoice and payment data as XML (qbo doesn't support json attachments)
    with tempfile.NamedTemporaryFile("w") as tmpxml:
        tmpxml.write(invoice2xml(invoice))
        tmpxml.flush()
        qb_attachment = objects.Attachable()
        qb_attachment.AttachableRef.append(qb_attachment_ref)
        qb_attachment.FileName = "xero-data-{:%Y-%m-%d}.xml".format(timezone.now())
        qb_attachment.ContentType = "text/xml"
        qb_attachment._FilePath = tmpxml.name
        qb_attachment.save(qb=client)

    # Attach xero invoice PDF
    invoice_pdf = xero.invoices.get(
        invoice["InvoiceID"], headers={"Accept": "application/pdf"}
    )
    with tempfile.NamedTemporaryFile() as tmppdf:
        tmppdf.write(invoice_pdf)
        tmppdf.flush()
        qb_attachment = objects.Attachable()
        qb_attachment.AttachableRef.append(qb_attachment_ref)
        qb_attachment.FileName = "xero-invoice-{:%Y-%m-%d}.pdf".format(timezone.now())
        qb_attachment.ContentType = "application/pdf"
        qb_attachment._FilePath = tmppdf.name
        qb_attachment.save(qb=client)


def run():
    customer_queries = []

    # Get invoices already in QB to allow re-running the script
    for invoice in objects.Invoice.all(qb=client, max_results=500):
        existing_invoices[invoice.DocNumber] = invoice
    logger.info("Existing QBO invoices: {}".format(len(existing_invoices)))

    logger.info("Getting Xero invoices...")
    invoices = xero.invoices.filter(type="ACCREC")
    invoices = [i for i in invoices if i["Status"] not in exclude_statuses]

    new_invoices = [
        i
        for i in invoices
        if normalize_inum(i["InvoiceNumber"]) not in existing_invoices
    ]

    # Create map of Xero Invoice ID to Quickbooks Customer ID
    logger.info("Finding matching customers...")
    for invoice in new_invoices:
        if invoice["Status"] in exclude_statuses:
            continue
        customer_name = invoice["Contact"]["Name"]
        batch_id = str(uuid.uuid4())
        batch_to_invoice[batch_id] = invoice["InvoiceID"]
        customer_queries.append(
            {
                "bId": batch_id,
                "Query": "SELECT Id FROM Customer WHERE DisplayName = '{}'".format(
                    customer_name
                ),
            }
        )

    # Send in batches of 30 queries per request
    for chunk in chunks(customer_queries, 30):
        result = client.batch_operation(json.dumps({"BatchItemRequest": chunk}))
        for result_item in result["BatchItemResponse"]:
            if result_item.get("Fault"):
                logger.error(result_item["Fault"]["Error"][0]["Detail"])
                continue
            invoice_id = batch_to_invoice[result_item["bId"]]
            if not result_item["QueryResponse"].get("Customer"):
                logger.error("No customer found for invoice {}".format(invoice_id))
            invoice_to_customer[invoice_id] = result_item["QueryResponse"]["Customer"][
                0
            ]["Id"]

    answer = input(
        "{} invoices will be imported. Continue? [y/N]: ".format(len(new_invoices))
    )
    if answer.lower() != "y":
        return

    for i, invoice in enumerate(new_invoices):
        # Avoid throttling
        time.sleep(1.02)
        try:
            result = import_invoice(invoice)
        except Exception as e:
            logger.error("Error on {}: {}".format(invoice["InvoiceID"], e))
            import ipdb

            ipdb.set_trace()
            raise

        logger.info(
            "{} - {}/{}".format(invoice["InvoiceNumber"], i + 1, len(new_invoices))
        )
