"""
Custom wsgi handler for Azure app services
"""

import logging
import os

import django
from django import http
from django.core.handlers.wsgi import WS<PERSON>Handler

log = logging.getLogger(__name__)


class AzureWSGIHandler(WSGIHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_response(self, request):
        # Respond to health check from azure to prevent container from stopping.
        host = request._get_raw_host()
        if host.endswith(".azurewebsites.net"):
            return http.HttpResponse("OK", status=200)
        return super().get_response(request)


def get_wsgi_application():
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "hive.settings")
    os.environ.setdefault("AZURE_SUPPORT_ENABLED", "1")

    django.setup(set_prefix=False)
    return AzureWSGIHandler()


application = get_wsgi_application()
