import json
import logging
import os
from functools import partial

from azure.appconfiguration import AzureAppConfigurationClient, ConfigurationSetting
from azure.core.exceptions import ResourceNotFoundError
from azure.identity import DefaultAzureCredential
from azure.mgmt.web import WebSiteManagementClient
from django.core.management.base import BaseCommand, CommandError, CommandParser

from azure_support import config as azure_config


class Command(BaseCommand):
    help = "Interact with Azure resources and configuration"  # noqa

    def __init__(self, *args, **kwargs):
        # Get default subscription
        self.subscription_id = None
        context_path = os.path.expanduser("~/.azure/azureProfile.json")
        if os.path.exists(context_path):
            with open(context_path, "r", encoding="utf-8-sig") as context_file:
                context = json.load(context_file)
                for subscription in context.get("subscriptions", []):
                    if subscription.get("isDefault"):
                        self.subscription_id = subscription.get("id")

        self.subscription_id = os.environ.get(
            "AZURE_SUBSCRIPTION_ID", self.subscription_id
        )
        if not self.subscription_id:
            raise CommandError(
                "Missing AZURE_SUBSCRIPTION_ID in environment",
            )

        self.resource_group = os.environ.get("AZURE_RESOURCE_GROUP", "rg-boldidea-hive")
        self.webapp_prefix = os.environ.get("AZURE_WEBAPP_PREFIX", "app-boldidea-hive-")

        super().__init__(*args, **kwargs)

    @property
    def credential(self):
        if not hasattr(self, "_credential"):
            # Silence azure credentials logger
            identity_logger = logging.getLogger("azure.identity")
            identity_logger.setLevel(logging.ERROR)
            verbosity = getattr(self, "_verbosity", 0)

            if verbosity > 2:
                identity_logger.setLevel(logging.INFO)
            elif verbosity > 3:
                identity_logger.setLevel(logging.DEBUG)

            self._credential = DefaultAzureCredential()
        return self._credential

    @property
    def web_client(self):
        if not hasattr(self, "_web_client"):
            self._web_client = WebSiteManagementClient(
                self.credential, self.subscription_id
            )
        return self._web_client

    def add_arguments(self, parser):
        # Fix subparser error handling
        parser_class = partial(
            CommandParser,
            called_from_command_line=parser.called_from_command_line,
        )

        subparsers = parser.add_subparsers(
            dest="subcommand",
            metavar="command",
            parser_class=parser_class,
        )

        # define parser for 'restart' command
        parser_restart = subparsers.add_parser("restart", help="Restart a webapp")
        parser_restart.add_argument("appname", type=str, help="Hive app name")

        # define parser for 'log' command
        parser_log = subparsers.add_parser(
            "log",
            help="Output logs for a given app",
            description="Output logs for a given app",
        )
        parser_log.add_argument("appname", type=str, help="Hive app name")
        parser_log.add_argument("-f", "--follow", action="store_true")

        # define parser for 'config' command
        parser_config = subparsers.add_parser(
            "config",
            help="Manage hive settings",
            description="Manage hive settings",
        )
        parser_config.add_argument(
            "-l",
            "--label",
            required=True,
            help='Config label (eg, "hive_testing" or "hive_prod")',
        )
        config_subparsers = parser_config.add_subparsers(
            dest="config_operation",
            metavar="operation",
            parser_class=parser_class,
        )

        parser_config_list = config_subparsers.add_parser(
            "list",
            help="List config settings",
        )
        parser_config_list.add_argument("--resolve-secrets", action="store_true")
        parser_config_list.add_argument("keyfilter", nargs="?")

        parser_config_get = config_subparsers.add_parser(
            "get",
            help="Get config settings",
            description="Get config settings",
        )

        parser_config_get.add_argument("key", type=str, default="")

        parser_config_set = config_subparsers.add_parser(
            "set",
            help="Set config setting",
            description="Set config setting",
        )
        parser_config_set.add_argument(
            "-v",
            "--vault-ref",
            action="store_true",
            help=(
                "Set value as keyvault reference (eg: "
                "https://{vault_name}.vault.azure.net/secrets/{secret_name})"
            ),
        )
        parser_config_set.add_argument("key", type=str)
        parser_config_set.add_argument("value", type=str, nargs="?", default="")

        parser_config_delete = config_subparsers.add_parser(
            "delete",
            help="Delete config setting",
            description="Delete config setting",
        )
        parser_config_delete.add_argument("key", type=str)

        # define parser for 'container-vars' command
        parser_appsettings = subparsers.add_parser(
            "container-vars",
            help="Manage app container variables",
            description="Manage app container variables",
        )
        parser_appsettings.add_argument(
            "appname",
            type=str,
            help="Hive app name",
        )
        appsettings_subparsers = parser_appsettings.add_subparsers(
            dest="operation",
            metavar="operation",
            parser_class=parser_class,
        )

        appsettings_subparsers.add_parser(
            "list",
            help="List app container variables",
            description="List app container variables",
        )

        parser_appsettings_get = appsettings_subparsers.add_parser(
            "get",
            help="Get app container variable",
            description="Get app container variable",
        )
        parser_appsettings_get.add_argument("key", type=str)

        parser_appsettings_set = appsettings_subparsers.add_parser(
            "set",
            help="Set app container variable",
            description="Set app container variable",
        )
        parser_appsettings_set.add_argument("key", type=str)
        parser_appsettings_set.add_argument("value", type=str, nargs="?", default="")

        parser_appsettings_delete = appsettings_subparsers.add_parser(
            "delete",
            help="Delete app container variable",
            description="Delete app container variable",
        )
        parser_appsettings_delete.add_argument("key", type=str)

    def create_parser(self, *args, **kwargs):
        return super().create_parser(*args, **kwargs)

    def handle(self, *args, **options):
        self._verbosity = options["verbosity"]

        subcommand = options["subcommand"]
        handler_name = subcommand.replace("-", "_")
        handler = getattr(self, f"handle_{handler_name}", None)
        if not handler:
            raise CommandError(f"Unknown command: {subcommand}")

        handler(*args, **options)

    def handle_restart(self, *args, **options):
        app_name = options["appname"]
        self.web_client.web_apps.restart(
            self.resource_group, self.webapp_prefix + app_name
        )
        print(f'Sucessfully restarted app "{app_name}"')

    def handle_log(self, *args, **options):
        app_name = options["appname"]
        follow = options["follow"]
        raise NotImplemented()

    def handle_container_vars(self, *args, **options):
        app_name = options["appname"]
        operation = options["operation"]

        settings = self.web_client.web_apps.list_application_settings(
            self.resource_group,
            self.webapp_prefix + app_name,
        )

        if operation in ("set", "delete"):
            key = options["key"]
            if operation == "set":
                settings.properties[key] = options["value"]
            elif operation == "delete":
                if key not in settings.properties:
                    raise CommandError(
                        f'Container variable not found for app "{app_name}": {key}'
                    )
                del settings.properties[key]
            self.web_client.web_apps.update_application_settings(
                self.resource_group,
                self.webapp_prefix + app_name,
                settings,
            )
            print(f'Updated container variables for app "{app_name}"')
        elif operation == "get":
            key = options["key"]
            if key not in settings.properties:
                raise CommandError(
                    f'Container variable not found for app "{app_name}": {key}'
                )
            print(settings.properties[key])
        elif operation == "list":
            print("\n".join(f"{k}={v}" for k, v in settings.properties.items()))

    def handle_config(self, **options):
        appconfig_endpoint = os.environ.get("AZURE_APPCONFIG_ENDPOINT")
        if not appconfig_endpoint:
            raise CommandError("Missing AZURE_APPCONFIG_ENDPOINT in environment")

        client = AzureAppConfigurationClient(
            base_url=appconfig_endpoint,
            credential=self.credential,
        )

        label = options["label"]
        operation = options["config_operation"]

        if operation == "list":
            if options["resolve_secrets"]:
                settings = azure_config.get_config(appconfig_endpoint, label)
            else:
                settings = {
                    item.key: item.value
                    for item in client.list_configuration_settings(
                        label_filter=label,
                        key_filter=options.get("keyfilter", "*"),
                    )
                }
            for key, value in settings.items():
                print(f"{key}={value}")

        elif operation == "get":
            key = options["key"]
            try:
                setting = client.get_configuration_setting(label=label, key=key)
            except ResourceNotFoundError:
                raise CommandError(
                    f"Configuration key {key} not found for label {label}"
                )
            print(setting.value)

        elif operation == "set":
            key = options["key"]
            value = options["value"]
            setting = ConfigurationSetting(
                key=key,
                label=label,
            )
            if options["vault_ref"]:
                setting.value = json.dumps(
                    {
                        "uri": value,
                    }
                )
                setting.content_type = (
                    "application/vnd.microsoft.appconfig.keyvaultref+json;charset=utf-8"
                )
            else:
                setting.value = value
            client.set_configuration_setting(setting)
            print(f"Set configuration key {key} for label {label}")

        elif operation == "delete":
            key = options["key"]
            client.delete_configuration_setting(label=label, key=key)
            print(f"Deleted configuration key {key} for label {label}")
