import logging

from aiohttp import web

from hive.management.commands.cron import Command as BaseCommand

log = logging.getLogger(__name__)


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "--http-server",
            nargs="?",
            metavar="[ADDR:]PORT",
            help="Start dummy HTTP server (for running on Azure App Services)",
        )

    def start_http_server(self, addr="0.0.0.0", port=8000):
        log.info(f"Starting dummy HTTP server on {addr}:{port}")
        app = web.Application()
        app.router.add_get("/", lambda *_: web.json_response({"status": "ok"}))
        runner = web.AppRunner(app)
        self.loop.run_until_complete(runner.setup())
        site = web.TCPSite(runner, addr, port)
        self.loop.run_until_complete(site.start())

    def handle(self, **options):
        if "http_server" in options:
            addr = "0.0.0.0"
            port = options["http_server"] or "8000"
            if ":" in port:
                addr, port = port.split(":")
            self.start_http_server(addr, int(port))

        super().handle(**options)
