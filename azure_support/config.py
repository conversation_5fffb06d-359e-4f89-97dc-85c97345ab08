import json
import os
import sys

from azure.appconfiguration import AzureAppConfigurationClient, provider
from azure.identity import DefaultAzureCredential

# Note: this module is imported before Django settings are available


def get_config(appconfig_endpoint, label):
    config = None
    cache = {}
    cache_path = os.environ.get("APPCONFIG_CACHE_FILE")
    cache_key = f"{appconfig_endpoint}:{label}"

    if cache_path and os.path.exists(cache_path):
        sys.stderr.write(f"Loading appconfig from {cache_path}...\n")
        try:
            with open(cache_path) as cache_file:
                cache = json.load(cache_file)
        except json.JSONDecodeError as e:
            sys.stderr.write(f"Could not parse cache file: {e}\n")
        else:
            config = cache.get(cache_key, {})

    if config is None and appconfig_endpoint and label:
        sys.stderr.write(f'Loading appconfig with label "{label}"...\n')

        credential = DefaultAzureCredential()
        key_vault_options = None
        key_vault_options = provider.AzureAppConfigurationKeyVaultOptions(
            credential=credential
        )
        selects = {provider.SettingSelector(key_filter="*", label_filter=label)}

        config = dict(
            provider.load(
                endpoint=appconfig_endpoint,
                credential=credential,
                key_vault_options=key_vault_options,
                selects=selects,
            )
        )

        if cache_path:
            cache[cache_key] = config
            with open(cache_path, "w") as cache_file:
                json.dump(cache, cache_file)
            sys.stderr.write(f"Saved appconfig to {cache_path}\n")

    return config or {}


def configure_env():
    """
    Pull settings from azure-appconfig into environment variables
    """
    appconfig_endpoint = os.environ.get("APPCONFIG_ENDPOINT")
    appconfig_label = os.environ.get("APPCONFIG_LABEL")

    if appconfig_endpoint and appconfig_label:
        config = get_config(appconfig_endpoint, appconfig_label)
        for key, value in config.items():
            os.environ.setdefault(key, value)
        sys.stderr.write(f"Set {len(config)} environment variables from appconfig.\n")
