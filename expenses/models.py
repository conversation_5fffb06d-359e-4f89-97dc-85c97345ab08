from django.conf import settings
from django.db import models
from quickbooks import objects

from billing import qb
from users.models import User


class ExpenseClaim(models.Model):
    user = models.ForeignKey(
        User, limit_choices_to={"is_staff": True}, on_delete=models.CASCADE
    )
    date = models.DateField()
    amount = models.DecimalField(max_digits=9, decimal_places=2)
    description = models.CharField(max_length=128)
    receipt = models.FileField(blank=True)
    approved = models.BooleanField(default=False)
    account_id = models.CharField(max_length=32)
    class_id = models.CharField(max_length=32)
    journal_id = models.CharField(max_length=32, blank=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if self.approved and not self.journal_id:
            journal = qb.create_reimbursement_entry(self)
            self.journal_id = journal.Id
            self.save()
