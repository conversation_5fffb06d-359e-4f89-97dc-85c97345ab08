from django import forms
from django.contrib import admin
from quickbooks import objects

from billing import qb
from hive.admin import register

from .models import ExpenseClaim


class ExpenseClaimForm(forms.ModelForm):
    account_id = forms.ChoiceField(label="Account")
    class_id = forms.ChoiceField(label="Activity")

    class Meta:
        fields = [
            "date",
            "amount",
            "description",
            "account_id",
            "class_id",
            "receipt",
            "user",
        ]
        model = ExpenseClaim
        widgets = {"user": forms.HiddenInput()}


@register(ExpenseClaim)
class ExpenseClaimAdmin(admin.ModelAdmin):
    list_display = ["date", "user", "amount", "description", "approved"]
    list_editable = ["approved"]
    form = ExpenseClaimForm

    def get_queryset(self, request, *args, **kwargs):
        qs = super().get_queryset(request, *args, **kwargs)
        return qs.filter(approved=False)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        client = qb.get_client()
        expense_accounts = objects.Account.filter(AccountType="Expense", qb=client)
        expense_accounts = [a for a in expense_accounts if a.AcctNum]
        classes = objects.Class.all(qb=client)
        account_choices = [("", "")] + [(a.Id, a.Name) for a in expense_accounts]
        class_choices = [("", "")] + [(c.Id, c.Name) for c in classes]
        form.base_fields["account_id"].choices = account_choices
        form.base_fields["class_id"].choices = class_choices
        form.base_fields["user"].initial = request.user
        form.base_fields["user"].widget = forms.HiddenInput()
        return form
