import re
from datetime import date

from django.conf import settings
from django.db import models
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    ExpressionWrap<PERSON>,
    <PERSON>,
    <PERSON>teger<PERSON>ield,
    Max,
    Q,
    Sum,
    Value,
    When,
)
from django.db.models.functions import ExtractYear
from django.utils import timezone

from . import utils


class ProgramQueryError(Exception):
    pass


class PastFutureQuerySetMixin:
    date_lookup = "date"

    def past(self, as_of=None):
        as_of = as_of or timezone.now()
        lookup = self.date_lookup + "__lt"
        return self.filter(**{lookup: as_of})

    def future(self, as_of=None):
        as_of = as_of or timezone.now()
        lookup = self.date_lookup + "__gte"
        return self.filter(**{lookup: as_of})


class SemesterQuerySetMixin:
    semester_date_lookups = ["date"]

    def with_semester(self):
        """
        Returns a QuerySet with year and semester
        """
        if len(self.semester_date_lookups) > 1:
            raise NotImplementedError(
                "Cannot use with_semester() with more than one lookup"
            )
        lookup = self.semester_date_lookups[0]
        filter = {}
        if "sessions" in lookup:
            # get the lookup up to sessions so we can filter only program
            # sessions
            sessions_lookup = re.sub(r"^(.*(__)?sessions).*$", r"\1", lookup)
            filter[sessions_lookup + "__type"] = "PROGRAM"

        lookup_month_gte = lookup + "__month__gte"
        lookup_month_lte = lookup + "__month__lte"

        return (
            self.filter(**filter)
            .annotate(
                year=ExtractYear(lookup),
                semester=Case(
                    When(
                        Q(**{lookup_month_gte: 1, lookup_month_lte: 5}),
                        then=Value("SPRING"),
                    ),
                    When(
                        Q(**{lookup_month_gte: 6, lookup_month_lte: 8}),
                        then=Value("SUMMER"),
                    ),
                    When(
                        Q(**{lookup_month_gte: 9, lookup_month_lte: 12}),
                        then=Value("FALL"),
                    ),
                    output_field=CharField(),
                ),
                semester_num=Case(
                    When(
                        Q(**{lookup_month_gte: 1, lookup_month_lte: 5}), then=Value(0)
                    ),
                    When(
                        Q(**{lookup_month_gte: 6, lookup_month_lte: 8}), then=Value(1)
                    ),
                    When(
                        Q(**{lookup_month_gte: 9, lookup_month_lte: 12}), then=Value(2)
                    ),
                    output_field=IntegerField(),
                ),
            )
            .distinct()
        )

    def for_year(self, year, fiscal=True):
        """
        Filter results for a specific year
        """
        q = Q()
        year = int(year)
        for lookup in self.semester_date_lookups:
            filter = {}
            if "sessions" in lookup:
                # get the lookup up to sessions so we can filter only program
                # sessions
                sessions_lookup = re.sub(r"^(.*(__)?sessions).*$", r"\1", lookup)
                filter[sessions_lookup + "__type"] = "PROGRAM"

            if fiscal:
                filter[lookup + "__gte"] = date(
                    year - 1, settings.FISCAL_START_MONTH, 1
                )
                filter[lookup + "__lt"] = date(year, settings.FISCAL_START_MONTH, 1)
            else:
                filter[lookup + "__year"] = year

            q = q | Q(**filter)

        return self.filter(q).distinct()

    def _get_for_semester_q(self, year, semester):
        q = Q()
        year = int(year)
        for lookup in self.semester_date_lookups:
            filter = {}
            if "sessions" in lookup:
                # get the lookup up to sessions so we can filter only program
                # sessions
                sessions_lookup = re.sub(r"^(.*(__)?sessions).*$", r"\1", lookup)
                filter[sessions_lookup + "__type"] = "PROGRAM"

            semester_start, semester_end = utils.get_semester_date_range(year, semester)
            filter[lookup + "__gte"] = semester_start
            filter[lookup + "__lt"] = semester_end
            q = q | Q(**filter)
        return q

    def for_semester(self, year, semester):
        """
        Filter results for a specific year and semester where `semester` is one
        of "FALL", "SUMMER", or "SPRING"
        """
        q = self._get_for_semester_q(year, semester)
        return self.filter(q).distinct()


class SemesterQuerySet(SemesterQuerySetMixin, models.QuerySet):
    @classmethod
    def as_manager(cls, *date_lookups):
        kwargs = {}
        if len(date_lookups):
            kwargs = {"semester_date_lookups": date_lookups}
        cls = type(cls.__name__, (cls,), kwargs)
        return super(SemesterQuerySet, cls).as_manager()

    as_manager.queryset_only = True


class ProgramQuerySet(SemesterQuerySetMixin, models.QuerySet):
    semester_date_lookups = ["start_date"]

    def _get_open_for_registration_q(self, as_of):
        return Q(
            Q(registration_close_date__isnull=True)
            | Q(registration_close_date__gte=as_of),
            registration_open_date__isnull=False,
            registration_open_date__lte=as_of,
            canceled=False,
        )

    def with_end_date(self):
        return self.annotate(
            end_date=Max("sessions__date", filter=Q(sessions__canceled=False))
        )

    def open_for_registration(self, as_of=None):
        as_of = as_of or timezone.now().today()
        return self.filter(self._get_open_for_registration_q(as_of=as_of))

    def current(self, only_started=False, as_of=None):
        """
        Includes all programs for current semester or open for registration
        """
        as_of = as_of or timezone.now()
        programs = self.with_end_date()

        # don't include programs that have ended
        current_q = Q(end_date__gte=as_of)

        if only_started:
            current_q &= Q(start_date__lte=as_of)
        else:
            current_q &= self._get_for_semester_q(
                *utils.get_current_semester(as_of=as_of)
            )

        q = current_q | self._get_open_for_registration_q(as_of=as_of)

        return programs.filter(q).distinct()

    def with_volunteer_stats(self):
        """
        Adds `remaining_volunteers_needed` to the queryset.

        This is calculated by looking at individual session signups rather than all volunteers
        related to the program (some can be floaters, etc).
        """

        # KLUDGE: this calculates remaining volunteers needed based on assignments where
        # role=MENTOR. Some programs in the future might not require a "mentor" role. In such a
        # case, we should change hive so that we can specifiy number of volunteers needed by role.

        volunteers_needed = ExpressionWrapper(
            F("num_student_seats") / F("student_volunteer_ratio"),
            output_field=models.IntegerField(),
        )

        # TODO: upgrade to Django 2 and use filtered count feature instead of conditional
        # aggregation
        num_mentors = ExpressionWrapper(
            Sum(
                Case(
                    When(volunteer_assignments__role="MENTOR", then=1),
                    default=0,
                    output_field=models.IntegerField(),
                )
            ),
            output_field=models.IntegerField(),
        )

        qs = self.annotate(
            volunteers_needed=volunteers_needed, num_mentors=num_mentors
        ).annotate(remaining_volunteers_needed=(volunteers_needed - num_mentors))
        return qs

    def with_attendance_stats(self):
        """
        Use when calculating attendance stats over multiple programs to reduce number of queries
        needed
        """
        return self.prefetch_related(
            "sessions__volunteer_attendances", "sessions__student_attendances"
        )


class SessionQuerySet(SemesterQuerySetMixin, PastFutureQuerySetMixin, models.QuerySet):
    date_lookup = "date"
    semester_date_lookups = ["date"]

    def with_attendance_stats(self):
        return self.annotate(
            num_volunteer_attendances=Count("volunteer_attendances"),
            num_student_attendances=Count("student_attendances"),
        )


class AttendanceQuerySet(PastFutureQuerySetMixin, SemesterQuerySet):
    date_lookup = "session__date"
    semester_date_lookups = ["session__date"]


class VolunteerAttendanceQuerySet(AttendanceQuerySet):
    def verified(self):
        return self.filter(assignment__verified=True)


class StudentRegistrationQuerySet(SemesterQuerySet):
    semester_date_lookups = ["program__start_date"]

    def by_status(self):
        return self.annotate(
            status_order=Case(
                When(status="COMPLETE", then=0),
                When(status="CANCELED", then=2),
                default=1,
                output_field=IntegerField(),
            )
        ).order_by("status_order")


class AnnouncementQuerySet(models.QuerySet):
    def current(self):
        now = timezone.now()
        return self.filter(
            Q(publish_date=None) | Q(publish_date__lte=now),
            Q(archive_date=None) | Q(archive_date__gt=now),
        )

    def for_volunteers(self):
        return self.filter(Q(visibility="ANYONE") | Q(visibility="VOLUNTEER"))

    def for_students(self):
        return self.filter(Q(visibility="ANYONE") | Q(visibility="STUDENT"))
