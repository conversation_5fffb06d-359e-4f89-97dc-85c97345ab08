from datetime import date

from django.conf import settings
from django.core.mail import send_mail
from django.template import loader
from django.utils import timezone
from django.utils.translation import get_language

from hive import utils


def get_semester(date):
    if date.month <= 5:
        return "SPRING"
    if date.month >= 6 and date.month <= 8:
        return "SUMMER"
    if date.month >= 9:
        return "FALL"


def get_semester_date_range(year, semester):
    if semester == "SPRING":
        return (date(year, 1, 1), date(year, 6, 1))
    elif semester == "SUMMER":
        return (date(year, 6, 1), date(year, 9, 1))
    elif semester == "FALL":
        return (date(year, 9, 1), date(year + 1, 1, 1))
    else:
        raise ValueError("semester must be one of 'FALL', 'SPRING', or 'SUMMER'")


def parse_semester(semester_string):
    year, sem = semester_string.split("-")
    error_message = (
        "string must be in the format of 'YYYY-SEM', where SEM is one of "
        "'FALL', 'SPRING', or 'SUMMER'"
    )
    try:
        year = int(year)
    except ValueError:
        raise ValueError(error_message)

    if sem not in ("FALL", "SPRING", "SUMMER"):
        raise ValueError(error_message)

    return year, sem


def get_semester_description(date):
    semester = get_semester(date)
    return "{} {}".format(semester.title(), date.year)


def get_grade_choices():
    GRADE_CHOICES = []
    if get_language() == "es":
        GRADE_CHOICES = [(n, (str(n) + ".º")) for n in range(1, 13)]
    else:
        GRADE_CHOICES = [(n, utils.ordinal(n)) for n in range(1, 13)]

    return GRADE_CHOICES


def grammatical_join(items):
    items = list(items)
    return ", ".join(items[:-2] + [" and ".join(items[-2:])])


def get_current_school_year(as_of=None):
    """
    Returns the current school year assuming the year ends June 31.

    School year is referenced by its starting year.
    """
    today = as_of or timezone.now()
    if today.month < 7:
        return today.year - 1
    return today.year


def get_current_school_year_description(as_of=None):
    year = get_current_school_year(as_of)
    return "{} - {}".format(year, year + 1)


def get_current_semester(as_of=None):
    as_of = as_of or timezone.now()
    return as_of.year, get_semester(as_of)


def send_registration_confirmation(registration):
    subject = "Your registration has been received"
    student_names = grammatical_join(
        s.first_name for s in registration.students.distinct()
    )
    program_names = grammatical_join(
        p.category.name for p in registration.programs.distinct()
    )
    current_school_year = get_current_school_year_description()

    registration_msg = loader.get_template(
        "programs/emails/registration_email.txt"
    ).render(
        {
            "contact": registration.account.default_contact,
            "current_school_year": current_school_year,
            "student_names": student_names,
            "program_names": program_names,
            "registration": registration,
        }
    )
    send_mail(
        subject,
        registration_msg,
        settings.DEFAULT_FROM_EMAIL,
        [registration.account.user.email],
    )


def send_registration_notification(registration):
    notification_subj = "A registration has been submitted"
    notification_msg = loader.get_template(
        "programs/emails/registration_notification.txt"
    ).render({"registration": registration})
    send_mail(
        notification_subj,
        notification_msg,
        settings.DEFAULT_FROM_EMAIL,
        settings.REGISTRATION_NOTIFICATION_RECIPIENTS,
    )


def get_semesters_lookup():
    """
    Generates a tuple of two-item tuples
    """
    from programs.models import Session

    sessions = Session.objects.all().with_semester()
    sessions = sessions.values("year", "semester_num", "semester").distinct()
    for item in sessions.order_by("-year", "semester_num"):
        yield (
            "{}-{}".format(item["year"], item["semester"]),
            "{} {}".format(item["semester"].title(), item["year"]),
        )


def setup_chat(program):
    """
    Creates the initial configuration for chat
    """
    from chat.models import Channel, MemberGroup

    # create the mentors member group
    mentor_group, group_created = MemberGroup.objects.get_or_create(
        slug="mentors", name="Mentors", program=program
    )

    # create the default channel
    channel, created = Channel.objects.get_or_create(
        name=settings.CHAT_DEFAULT_CHANNEL, program=program
    )

    # create the mentors channel
    mentor_channel, created = Channel.objects.get_or_create(
        name="mentors", program=program, private=True
    )

    if mentor_channel.group_members.filter(slug="mentors").count() == 0:
        mentor_channel.group_members.add(mentor_group)


def create_student_chat_channel(student, program):
    from chat.models import Channel, MemberGroup

    channel_name = student.user.student_channel_name
    channel, created = Channel.objects.get_or_create(
        name=channel_name,
        program=program,
        defaults={
            "type": "STUDENT",
            "private": True,
            "metadata": {"student_id": student.id},
        },
    )

    # Add student to channel
    if channel.members.filter(pk=student.user.pk).count() == 0:
        channel.members.add(student.user)

    # Add mentors member group
    if not channel.group_members.filter(slug="mentors"):
        mentor_group = MemberGroup.objects.get(program=program, slug="mentors")
        channel.group_members.add(mentor_group)
